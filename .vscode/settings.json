{"i18n-ally.localesPaths": ["messages", "public/locales", "src/i18n", "src/messages", "src/lib/i18n"], "go.gopath": "", "go.goroot": "", "go.toolsManagement.autoUpdate": true, "go.useLanguageServer": true, "go.languageServerExperimentalFeatures": {"diagnostics": true, "documentLink": true}, "go.lintOnSave": "package", "go.formatTool": "goimports", "go.lintTool": "golangci-lint", "go.buildOnSave": "package", "go.vetOnSave": "package", "go.coverOnSave": false, "go.testOnSave": false, "gopls": {"ui.semanticTokens": true, "ui.completion.usePlaceholders": true, "formatting.gofumpt": true}, "files.associations": {"*.go": "go"}}