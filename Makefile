# ADC Restaurant Platform Makefile
# This Makefile provides commands for running the frontend and backend servers

# Variables
FRONTEND_PORT = 4000
BACKEND_PORT = 5000
WEBSOCKET_PORT = 8200
DB_URL = $(shell grep DATABASE_URL .env 2>/dev/null | cut -d '=' -f2 || echo "postgresql://postgres:postgres@localhost:5432/adc_restaurant")

# Colors for terminal output
YELLOW = \033[1;33m
GREEN = \033[1;32m
RED = \033[1;31m
BLUE = \033[1;34m
NC = \033[0m # No Color

# Default target
.PHONY: all
all: help

# Help command
.PHONY: help
help:
	@echo "${BLUE}ADC Restaurant Platform Development Commands${NC}"
	@echo ""
	@echo "${YELLOW}Usage:${NC}"
	@echo "  make ${GREEN}<command>${NC}"
	@echo ""
	@echo "${YELLOW}Available commands:${NC}"
	@echo "  ${GREEN}dev${NC}              - Start both frontend and backend servers"
	@echo "  ${GREEN}frontend${NC}         - Start only the frontend server"
	@echo "  ${GREEN}backend${NC}          - Start only the backend server (Go Restaurant API)"
	@echo "  ${GREEN}websocket${NC}        - Start only the WebSocket server"
	@echo "  ${GREEN}dev-all${NC}          - Start frontend, backend, and WebSocket servers"
	@echo "  ${GREEN}db-setup${NC}         - Set up the database (generate Prisma client)"
	@echo "  ${GREEN}db-migrate${NC}       - Run database migrations"
	@echo "  ${GREEN}db-seed${NC}          - Seed the database with initial data"
	@echo "  ${GREEN}db-studio${NC}        - Open Prisma Studio to view/edit database"
	@echo "  ${GREEN}clean${NC}            - Clean build artifacts"
	@echo "  ${GREEN}build${NC}            - Build the application for production"
	@echo "  ${GREEN}install${NC}          - Install dependencies"
	@echo "  ${GREEN}backend-build${NC}    - Build the backend server"
	@echo "  ${GREEN}backend-test${NC}     - Run backend tests"
	@echo "  ${GREEN}backend-deps${NC}     - Install backend dependencies"
	@echo "  ${GREEN}backend-clean${NC}    - Clean backend build artifacts"
	@echo "  ${GREEN}backend-run${NC}      - Run backend server (production build)"
	@echo "  ${GREEN}redis-start${NC}      - Start Redis server (Docker)"
	@echo "  ${GREEN}redis-stop${NC}       - Stop Redis server"
	@echo "  ${GREEN}redis-logs${NC}       - View Redis logs"
	@echo "  ${GREEN}redis-cli${NC}        - Connect to Redis CLI"
	@echo "  ${GREEN}redis-ui${NC}         - Open Redis UI (Redis Commander)"
	@echo "  ${GREEN}redis-status${NC}     - Check Redis status"
	@echo "  ${GREEN}help${NC}             - Show this help message"
	@echo ""
	@echo "${YELLOW}Examples:${NC}"
	@echo "  make dev          # Start both servers"
	@echo "  make frontend     # Start only the frontend"
	@echo "  make backend      # Start only the backend"
	@echo "  make db-setup     # Set up the database"

# Start both frontend and backend servers
.PHONY: dev
dev:
	@echo "${BLUE}Starting development servers...${NC}"
	@make -j 2 frontend backend

# Start all servers (frontend, backend, and WebSocket)
.PHONY: dev-all
dev-all:
	@echo "${BLUE}Starting all development servers...${NC}"
	@make -j 3 frontend backend websocket

# Start frontend server
.PHONY: frontend
frontend:
	@echo "${GREEN}Starting frontend server on port ${FRONTEND_PORT}...${NC}"
	@bun run dev

# Start backend server (Go Restaurant API)
.PHONY: backend
backend:
	@echo "${GREEN}Starting Go backend server (Restaurant API)...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make dev

# Start WebSocket server
.PHONY: websocket
websocket:
	@echo "${GREEN}Starting WebSocket server on port ${WEBSOCKET_PORT}...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make dev-ws

# Database setup
.PHONY: db-setup
db-setup:
	@echo "${GREEN}Setting up database...${NC}"
	@npx prisma generate

# Database migrations
.PHONY: db-migrate
db-migrate:
	@echo "${GREEN}Running database migrations...${NC}"
	@npx prisma migrate dev

# Seed database
.PHONY: db-seed
db-seed:
	@echo "${GREEN}Seeding database...${NC}"
	@npx prisma db seed

# Open Prisma Studio
.PHONY: db-studio
db-studio:
	@echo "${GREEN}Opening Prisma Studio...${NC}"
	@npx prisma studio

# Clean build artifacts
.PHONY: clean
clean:
	@echo "${GREEN}Cleaning build artifacts...${NC}"
	@rm -rf .next
	@rm -rf node_modules/.cache
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make clean

# Build for production
.PHONY: build
build:
	@echo "${GREEN}Building for production...${NC}"
	@bun run build

# Install dependencies
.PHONY: install
install:
	@echo "${GREEN}Installing frontend dependencies...${NC}"
	@bun install
	@echo "${GREEN}Installing backend dependencies...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make deps

# Docker commands (for future use)
.PHONY: docker-up
docker-up:
	@echo "${GREEN}Starting Docker containers...${NC}"
	@docker-compose up -d

.PHONY: docker-down
docker-down:
	@echo "${GREEN}Stopping Docker containers...${NC}"
	@docker-compose down

# Check environment
.PHONY: check-env
check-env:
	@echo "${GREEN}Checking environment...${NC}"
	@if [ ! -f .env ]; then \
		echo "${RED}Error: .env file not found. Please create one based on .env.example${NC}"; \
		exit 1; \
	fi
	@echo "${GREEN}Environment check passed.${NC}"

# Backend specific commands
.PHONY: backend-build
backend-build:
	@echo "${GREEN}Building backend server...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make build

.PHONY: backend-test
backend-test:
	@echo "${GREEN}Running backend tests...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make test

.PHONY: backend-deps
backend-deps:
	@echo "${GREEN}Installing backend dependencies...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make deps

.PHONY: backend-clean
backend-clean:
	@echo "${GREEN}Cleaning backend build artifacts...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make clean

# Run backend with production build
.PHONY: backend-run
backend-run:
	@echo "${GREEN}Running backend server (production build)...${NC}"
	@cd restaurant-backend && export PATH=$$PATH:$$(go env GOPATH)/bin && make run

# Redis commands
.PHONY: redis-start
redis-start:
	@echo "${GREEN}Starting Redis server (Docker)...${NC}"
	@docker-compose -f docker-compose.redis.yml up -d
	@echo "${BLUE}Redis is starting up...${NC}"
	@echo "Redis will be available at: ${YELLOW}localhost:6379${NC}"
	@echo "Redis UI will be available at: ${YELLOW}http://localhost:8081${NC} (admin/admin)"

.PHONY: redis-stop
redis-stop:
	@echo "${GREEN}Stopping Redis server...${NC}"
	@docker-compose -f docker-compose.redis.yml down

.PHONY: redis-logs
redis-logs:
	@echo "${GREEN}Viewing Redis logs...${NC}"
	@docker-compose -f docker-compose.redis.yml logs -f redis

.PHONY: redis-cli
redis-cli:
	@echo "${GREEN}Connecting to Redis CLI...${NC}"
	@docker exec -it adc-redis-dev redis-cli

.PHONY: redis-ui
redis-ui:
	@echo "${GREEN}Opening Redis UI...${NC}"
	@echo "Redis Commander is available at: ${YELLOW}http://localhost:8081${NC}"
	@echo "Username: ${YELLOW}admin${NC}, Password: ${YELLOW}admin${NC}"
	@open http://localhost:8081 2>/dev/null || echo "Please open http://localhost:8081 in your browser"

.PHONY: redis-status
redis-status:
	@echo "${GREEN}Checking Redis status...${NC}"
	@if docker ps | grep -q adc-redis-dev; then \
		echo "${GREEN}✅ Redis container is running${NC}"; \
		docker exec adc-redis-dev redis-cli ping && echo "${GREEN}✅ Redis is responding${NC}" || echo "${RED}❌ Redis is not responding${NC}"; \
		echo "${BLUE}Queue stats:${NC}"; \
		docker exec adc-redis-dev redis-cli ZCARD ai_generation_jobs | sed 's/^/  Pending jobs: /'; \
	else \
		echo "${RED}❌ Redis container is not running${NC}"; \
		echo "Run '${YELLOW}make redis-start${NC}' to start Redis"; \
	fi
