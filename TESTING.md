# Testing Documentation

This document provides comprehensive information about the testing setup and strategies for the ADC Shop Merchants application.

## 🧪 Testing Stack

### Unit & Integration Testing
- **Jest** - JavaScript testing framework
- **React Testing Library** - React component testing utilities
- **<PERSON><PERSON> (Mock Service Worker)** - API mocking for reliable tests
- **@testing-library/user-event** - User interaction simulation

### End-to-End Testing
- **Playwright** - Cross-browser E2E testing
- **Multi-browser support** - Chrome, Firefox, Safari, Mobile

### Test Utilities
- **Custom test utilities** - Shared helpers and mock data
- **Redux testing helpers** - Store setup and state management testing

## 📁 Test Structure

```
src/
├── app/[locale]/app/restaurant/
│   ├── __tests__/                    # Restaurant list page tests
│   ├── [slugShop]/[slugBranch]/
│   │   ├── dashboard/__tests__/       # Dashboard tests
│   │   ├── menu/__tests__/           # Menu management tests
│   │   ├── orders/__tests__/         # Orders management tests
│   │   ├── staff/__tests__/          # Staff management tests
│   │   └── tables/__tests__/         # Tables management tests
├── lib/
│   ├── __tests__/                    # Utility function tests
│   └── test-utils.tsx                # Shared testing utilities
tests/
├── e2e/                              # End-to-end tests
│   ├── tables-page.spec.ts
│   └── restaurant-management.spec.ts
├── jest.config.js                    # Jest configuration
├── jest.setup.js                     # Global test setup
└── playwright.config.ts              # Playwright configuration
```

## 🚀 Running Tests

### Unit Tests
```bash
# Run all unit tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage report
npm run test:coverage

# Run specific page tests
npm run test:tables
npm run test:dashboard
npm run test:menu
npm run test:orders
npm run test:staff
npm run test:restaurant
```

### End-to-End Tests
```bash
# Run all E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode (visible browser)
npm run test:e2e:headed

# Debug E2E tests
npm run test:e2e:debug
```

### Combined Testing
```bash
# Run all tests (unit + E2E)
npm run test:all

# Run tests for CI/CD
npm run test:ci
```

## 📋 Test Coverage

### Pages Tested
- ✅ **Restaurant List Page** - Restaurant listing, search, CRUD operations
- ✅ **Dashboard Page** - Stats display, navigation, real-time data
- ✅ **Menu Management** - Menu items, categories, search, view modes
- ✅ **Orders Management** - Order listing, status updates, search
- ✅ **Staff Management** - Staff listing, roles, permissions, schedules
- ✅ **Tables Management** - Table layout, reservations, areas

### Test Types
- ✅ **Component Rendering** - Proper UI rendering
- ✅ **User Interactions** - Clicks, form inputs, navigation
- ✅ **API Integration** - Real API calls and error handling
- ✅ **State Management** - Redux store and data flow
- ✅ **Error Boundaries** - Graceful error handling
- ✅ **Loading States** - Proper loading indicators
- ✅ **Empty States** - No data scenarios
- ✅ **Search Functionality** - Filtering and search
- ✅ **Responsive Design** - Mobile and desktop layouts
- ✅ **Accessibility** - ARIA compliance and keyboard navigation

## 🎯 Testing Strategies

### 1. Unit Tests
Focus on individual components and functions:
- Component rendering with different props
- User event handling
- Data transformation and formatting
- Utility function behavior

### 2. Integration Tests
Test component interactions with APIs and state:
- API integration with real endpoints
- Redux state management
- Component communication
- Data flow validation

### 3. End-to-End Tests
Test complete user workflows:
- Full page navigation
- Cross-browser compatibility
- Mobile responsiveness
- Real user scenarios

## 🔧 Test Configuration

### Jest Configuration (`jest.config.js`)
- Next.js integration
- TypeScript support
- Module path mapping
- Coverage thresholds
- Test environment setup

### Playwright Configuration (`playwright.config.ts`)
- Multi-browser testing
- Mobile device simulation
- Screenshot and video capture
- Parallel test execution

### Global Setup (`jest.setup.js`)
- Testing Library DOM matchers
- Mock implementations
- Global test utilities
- Environment configuration

## 📊 Mock Data

### Comprehensive Mock Data Available
- **Merchants** - Restaurant and branch data
- **Tables** - Table layouts and areas
- **Reservations** - Booking information
- **Menu Items** - Food items and categories
- **Orders** - Customer orders and status
- **Staff** - Employee data and schedules
- **Dashboard Stats** - Analytics and metrics

### API Mocking Strategies
- **MSW** for integration tests
- **Jest mocks** for unit tests
- **Playwright route mocking** for E2E tests

## 🎨 Best Practices

### Writing Tests
1. **Arrange, Act, Assert** pattern
2. **Descriptive test names** that explain the scenario
3. **Test user behavior** not implementation details
4. **Use semantic queries** (getByRole, getByLabelText)
5. **Mock external dependencies** appropriately

### Test Organization
1. **Group related tests** in describe blocks
2. **Use beforeEach/afterEach** for setup/cleanup
3. **Keep tests independent** and isolated
4. **Test edge cases** and error scenarios

### Performance
1. **Use waitFor** for async operations
2. **Avoid unnecessary re-renders** in tests
3. **Mock heavy operations** appropriately
4. **Parallel test execution** where possible

## 🐛 Debugging Tests

### Jest Debugging
```bash
# Run specific test file
npm test -- --testPathPattern=tables

# Run tests with verbose output
npm test -- --verbose

# Run tests in debug mode
node --inspect-brk node_modules/.bin/jest --runInBand
```

### Playwright Debugging
```bash
# Debug specific test
npm run test:e2e:debug -- --grep "should load tables"

# Run with headed browser
npm run test:e2e:headed

# Use Playwright Inspector
npm run test:e2e -- --debug
```

## 📈 Coverage Reports

### Viewing Coverage
```bash
# Generate coverage report
npm run test:coverage

# Open coverage report in browser
open coverage/lcov-report/index.html
```

### Coverage Thresholds
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## 🔄 Continuous Integration

### CI/CD Pipeline
```bash
# Run all tests for CI
npm run test:ci
```

### Pre-commit Hooks
Consider adding pre-commit hooks to run tests before commits:
```bash
# Install husky for git hooks
npm install --save-dev husky

# Add pre-commit hook
npx husky add .husky/pre-commit "npm run test:ci"
```

## 📚 Additional Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [MSW Documentation](https://mswjs.io/docs/)

## 🤝 Contributing

When adding new features:
1. **Write tests first** (TDD approach)
2. **Update existing tests** if behavior changes
3. **Add E2E tests** for new user workflows
4. **Maintain coverage thresholds**
5. **Update this documentation** as needed

## 📞 Support

For testing-related questions or issues:
1. Check existing test examples
2. Review this documentation
3. Consult team testing guidelines
4. Ask for help in team channels
