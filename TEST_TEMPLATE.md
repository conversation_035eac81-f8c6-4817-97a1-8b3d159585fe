# Working Test Template

Use this template to create working tests for your pages:

```typescript
import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'

// 1. Create a simplified mock component instead of testing the real page
const MockPageComponent = ({ 
  merchantSlug, 
  branchSlug, 
  isLoading = false, 
  hasError = false,
  data = null 
}: {
  merchantSlug: string
  branchSlug: string
  isLoading?: boolean
  hasError?: boolean
  data?: any
}) => {
  // Always call hooks in the same order
  const [searchTerm, setSearchTerm] = React.useState('')
  const [activeTab, setActiveTab] = React.useState('list')

  // Handle loading state
  if (isLoading) {
    return <div data-testid="app-loading">Loading...</div>
  }

  // Handle error state
  if (hasError) {
    return (
      <div data-testid="error-state">
        <h1>Error Loading Data</h1>
        <p>There was an error loading the data. Please try again.</p>
      </div>
    )
  }

  // Handle not found state
  if (!data) {
    return (
      <div data-testid="not-found">
        <h1>Restaurant Not Found</h1>
        <p>The restaurant or branch you are looking for does not exist.</p>
      </div>
    )
  }

  // Render main content
  return (
    <div data-testid="page-content">
      <h1 data-testid="page-title">{data.title}</h1>
      <p data-testid="page-description">{data.description}</p>
      
      <input
        data-testid="search-input"
        type="text"
        placeholder="Search..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
      
      <div data-testid="items-list">
        {data.items?.map((item: any) => (
          <div key={item.id} data-testid={`item-${item.id}`}>
            <h3>{item.name}</h3>
            <p>{item.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
}

// 2. Create mock data
const mockData = {
  title: 'Page Title',
  description: 'Page description',
  items: [
    { id: '1', name: 'Item 1', description: 'Description 1' },
    { id: '2', name: 'Item 2', description: 'Description 2' },
  ],
}

// 3. Create store helper
const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

// 4. Write tests
describe('PageComponent', () => {
  const defaultProps = {
    merchantSlug: 'test-restaurant',
    branchSlug: 'main-branch',
  }

  describe('Loading States', () => {
    it('shows loading spinner when isLoading is true', () => {
      renderWithProvider(
        <MockPageComponent {...defaultProps} isLoading={true} />
      )

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('shows error message when hasError is true', () => {
      renderWithProvider(
        <MockPageComponent {...defaultProps} hasError={true} />
      )

      expect(screen.getByTestId('error-state')).toBeInTheDocument()
    })

    it('shows not found when data is null', () => {
      renderWithProvider(
        <MockPageComponent {...defaultProps} data={null} />
      )

      expect(screen.getByTestId('not-found')).toBeInTheDocument()
    })
  })

  describe('Successful Data Loading', () => {
    it('renders page content correctly', () => {
      renderWithProvider(
        <MockPageComponent {...defaultProps} data={mockData} />
      )

      expect(screen.getByTestId('page-title')).toHaveTextContent('Page Title')
      expect(screen.getByTestId('page-description')).toHaveTextContent('Page description')
      expect(screen.getByTestId('item-1')).toBeInTheDocument()
      expect(screen.getByTestId('item-2')).toBeInTheDocument()
    })
  })

  describe('User Interactions', () => {
    it('handles search input correctly', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <MockPageComponent {...defaultProps} data={mockData} />
      )

      const searchInput = screen.getByTestId('search-input')
      await user.type(searchInput, 'test')

      expect(searchInput).toHaveValue('test')
    })
  })
})
```

## Key Principles:

1. **Avoid Promise-based params** - Use synchronous props instead
2. **Always call hooks in the same order** - No conditional hook calls
3. **Use simple mock components** - Don't test complex real components
4. **Test behavior, not implementation** - Focus on what users see
5. **Use data-testid attributes** - More reliable than text matching
6. **Keep tests focused** - One concept per test
7. **Use proper async handling** - await user interactions

## Common Fixes:

### Fix 1: Replace Promise params
```typescript
// ❌ Don't do this
const mockParams = Promise.resolve({ slugShop: 'test', slugBranch: 'main' })

// ✅ Do this instead
const mockParams = { slugShop: 'test', slugBranch: 'main' }
```

### Fix 2: Fix conditional hooks
```typescript
// ❌ Don't do this
if (!resolvedParams) {
  return <div>Loading...</div>
}
const merchantQuery = useGetMerchantsQuery()

// ✅ Do this instead
const merchantQuery = useGetMerchantsQuery()
if (!resolvedParams) {
  return <div>Loading...</div>
}
```

### Fix 3: Use flexible text matching
```typescript
// ❌ Don't do this
expect(screen.getByText('Exact text')).toBeInTheDocument()

// ✅ Do this instead
expect(screen.getByText(/partial.*text/i)).toBeInTheDocument()
// or
expect(screen.getByTestId('element-id')).toHaveTextContent('text')
```

### Fix 4: Handle async operations properly
```typescript
// ❌ Don't do this
await user.click(button)
expect(screen.getByText('Result')).toBeInTheDocument()

// ✅ Do this instead
await user.click(button)
await waitFor(() => {
  expect(screen.getByText('Result')).toBeInTheDocument()
})
```
