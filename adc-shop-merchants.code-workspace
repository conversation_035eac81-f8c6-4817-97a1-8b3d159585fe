{"folders": [{"name": "Frontend", "path": "."}, {"name": "Backend (Go)", "path": "./restaurant-backend"}], "settings": {"go.gopath": "", "go.goroot": "", "go.toolsManagement.autoUpdate": true, "go.useLanguageServer": true, "go.languageServerExperimentalFeatures": {"diagnostics": true, "documentLink": true}, "go.lintOnSave": "package", "go.formatTool": "goimports", "go.lintTool": "golangci-lint", "go.buildOnSave": "package", "go.vetOnSave": "package", "go.coverOnSave": false, "go.testOnSave": false, "gopls": {"ui.semanticTokens": true, "ui.completion.usePlaceholders": true, "formatting.gofumpt": true, "build.directoryFilters": ["-node_modules", "-tmp", "-vendor"]}, "files.associations": {"*.go": "go"}, "search.exclude": {"**/node_modules": true, "**/tmp": true, "**/vendor": true, "**/.git": true, "**/dist": true, "**/.next": true}}, "extensions": {"recommendations": ["golang.go", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-vscode.vscode-json"]}}