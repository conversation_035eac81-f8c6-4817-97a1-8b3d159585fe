version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: adc-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    environment:
      - REDIS_REPLICATION_MODE=master
    networks:
      - adc-network

  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: adc-redis-ui
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - adc-network

volumes:
  redis_data:
    driver: local

networks:
  adc-network:
    driver: bridge
