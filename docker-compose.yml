version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: adc-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: adc_restaurant
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching (optional, for future use)
  redis:
    image: redis:7
    container_name: adc-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Adminer for database management (optional)
  adminer:
    image: adminer
    container_name: adc-adminer
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
    restart: unless-stopped
    depends_on:
      - postgres

volumes:
  postgres_data:
  redis_data:
