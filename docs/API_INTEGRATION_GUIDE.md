# API Integration Guide

This guide explains how to replace mock data with real API calls in the ADC Shop Merchants application.

## 🎯 Overview

The application has been updated to use real API endpoints instead of mock data. This document outlines the changes made and how to continue the integration process.

## 🔧 Configuration

### Environment Variables

Add these environment variables to your `.env.local` file:

```env
# Backend API Configuration
BACKEND_API_URL=http://localhost:8080/api/v1
NEXT_PUBLIC_BACKEND_API_URL=http://localhost:8080/api/v1
```

### API Base Configuration

The API slice has been updated to use the backend API URL and NextAuth authentication:

```typescript
// src/lib/redux/api/apiSlice.ts
const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8080/api/v1';

const baseQuery = fetchBaseQuery({
  baseUrl: BACKEND_API_URL,
  prepareHeaders: async (headers) => {
    const session = await getSession();
    if (session?.user?.id) {
      headers.set('authorization', `Bearer ${session.user.id}`);
      headers.set('x-user-id', session.user.id);
    }
    headers.set('content-type', 'application/json');
    return headers;
  },
});
```

## 📋 Implemented API Endpoints

### 1. Menu API (`src/lib/redux/api/endpoints/menuApi.ts`)

**Endpoints:**
- `getMenuItems` - Get all menu items for a merchant/branch
- `getMenuItem` - Get specific menu item by ID
- `getMenuItemBySlug` - Get menu item by slug
- `getMenuCategories` - Get menu categories
- `createMenuItem` - Create new menu item
- `updateMenuItem` - Update existing menu item
- `deleteMenuItem` - Delete menu item

**Usage:**
```typescript
import { useGetMenuItemsQuery } from '@/lib/redux/api/endpoints/menuApi';

const { data: menuItems, isLoading, isError } = useGetMenuItemsQuery({
  merchantId: 'merchant-id',
  branchId: 'branch-id' // optional
});
```

### 2. Notification API (`src/lib/redux/api/endpoints/notificationApi.ts`)

**Endpoints:**
- `getNotifications` - Get paginated notifications
- `getNotification` - Get specific notification
- `getNotificationStats` - Get notification statistics
- `createNotification` - Create new notification
- `updateNotification` - Update notification (mark as read)
- `markAllNotificationsAsRead` - Mark all as read
- `deleteNotification` - Delete notification

**Usage:**
```typescript
import { useGetNotificationsQuery } from '@/lib/redux/api/endpoints/notificationApi';

const { data: notifications, isLoading } = useGetNotificationsQuery({
  merchantId: 'merchant-id',
  page: 1,
  limit: 20
});
```

### 3. Review API (`src/lib/redux/api/endpoints/reviewApi.ts`)

**Endpoints:**
- `getReviews` - Get paginated reviews
- `getReview` - Get specific review
- `getReviewStats` - Get review statistics
- `createReview` - Create new review
- `updateReview` - Update review status
- `respondToReview` - Respond to review
- `markReviewHelpful` - Mark review as helpful
- `reportReview` - Report review
- `deleteReview` - Delete review

**Usage:**
```typescript
import { useGetReviewsQuery } from '@/lib/redux/api/endpoints/reviewApi';

const { data: reviews, isLoading } = useGetReviewsQuery({
  merchantId: 'merchant-id',
  status: 'published',
  sortBy: 'date'
});
```

## 🔄 Pages Updated

### 1. Digital Products Page (`src/app/[locale]/app/digital/page.tsx`)

**Changes Made:**
- ✅ Added real API integration with `useGetDigitalProductsQuery`
- ✅ Added loading and error states
- ✅ Dynamic product counts and statistics
- ✅ Fallback to mock data when API is unavailable

**Features:**
- Real-time product data
- Dynamic statistics calculation
- Error handling with graceful fallback

### 2. Main App Dashboard (`src/app/[locale]/app/page.tsx`)

**Status:** ✅ Already implemented
- Uses `useGetMerchantsQuery` for real merchant data
- Falls back to mock categories when no merchants available

## 🚧 Next Steps

### Pages That Need API Integration

1. **Restaurant Pages**
   - Menu management
   - Order tracking
   - Table reservations
   - Staff management

2. **Convenience Store Page**
   - Product inventory
   - Sales tracking
   - Order management

3. **Service Pages**
   - Appointment booking
   - Service management
   - Staff scheduling

4. **Notification System**
   - Real-time notifications
   - Notification preferences
   - Push notifications

### Implementation Pattern

For each page that needs API integration:

1. **Import the API hook:**
```typescript
import { useGetDataQuery } from '@/lib/redux/api/endpoints/yourApi';
```

2. **Add loading and error states:**
```typescript
const { data, isLoading, isError } = useGetDataQuery(params);

if (isLoading) return <AppLoading />;
if (isError) return <ErrorComponent />;
```

3. **Use real data with fallback:**
```typescript
const items = data || mockData;
```

4. **Update UI to handle real data structure:**
```typescript
// Ensure compatibility with both real and mock data
const count = items?.length || 0;
const status = item.status || (item.available ? 'Active' : 'Inactive');
```

## 🔒 Authentication

The API integration uses NextAuth session data for authentication:

- User ID is sent in the `authorization` header
- Additional `x-user-id` header for backend processing
- Automatic token refresh handled by NextAuth

## 🧪 Testing

### Development Testing

1. **Start Backend Server:**
```bash
cd restaurant-backend
go run main.go
```

2. **Start Frontend:**
```bash
npm run dev
```

3. **Test API Endpoints:**
- Visit pages with API integration
- Check browser network tab for API calls
- Verify fallback to mock data when backend is unavailable

### Error Handling

All API integrations include:
- Loading states with `<AppLoading />` component
- Error states with user-friendly messages
- Graceful fallback to mock data
- Console warnings for debugging

## 📊 Benefits

- ✅ **Real-time Data:** Live updates from backend
- ✅ **Better Performance:** Efficient data fetching with RTK Query
- ✅ **Error Resilience:** Graceful fallback to mock data
- ✅ **Type Safety:** Full TypeScript support
- ✅ **Caching:** Automatic data caching and invalidation
- ✅ **Loading States:** Better user experience

## 🔍 Debugging

### Common Issues

1. **CORS Errors:** Ensure backend allows frontend origin
2. **Authentication Errors:** Check NextAuth session and token
3. **Network Errors:** Verify backend is running on correct port
4. **Data Structure Mismatches:** Check API response format

### Debug Tools

- Browser Network tab for API calls
- Redux DevTools for state inspection
- Console logs for error tracking
- Backend logs for server-side issues

The API integration provides a robust foundation for real-time data while maintaining backward compatibility with mock data for development and testing.
