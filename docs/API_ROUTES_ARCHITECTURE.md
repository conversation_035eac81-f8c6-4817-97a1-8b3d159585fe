# API Routes Architecture

This document explains the correct API architecture using Next.js API routes as a proxy to the backend.

## 🏗️ **Architecture Overview**

```
Frontend (React/Redux) → Next.js API Routes → Go Backend
     ↓                        ↓                  ↓
Client-side calls         Server-side proxy    Real API
/api/merchants           /api/merchants       /api/v1/merchants
```

## ✅ **Benefits of This Architecture**

1. **Security**: Backend URL and credentials hidden from client
2. **No CORS Issues**: Same-origin requests to Next.js
3. **Authentication**: Server-side NextAuth token handling
4. **Flexibility**: Can add caching, rate limiting, etc.
5. **Error Handling**: Centralized error handling and fallbacks

## 📋 **Current API Routes Structure**

### **Frontend Redux API Configuration**
```typescript
// src/lib/redux/api/apiSlice.ts
const baseQuery = fetchBaseQuery({
  baseUrl: '/api',  // ← Calls Next.js API routes
  prepareHeaders: async (headers) => {
    headers.set('content-type', 'application/json');
    // No auth headers needed - handled server-side
    return headers;
  },
});
```

### **Available API Routes**

#### **1. Merchants API**
- **Frontend calls**: `/api/merchants`
- **Next.js route**: `src/app/api/merchants/route.ts`
- **Backend calls**: `http://localhost:8080/api/v1/merchants`

#### **2. Menu Items API**
- **Frontend calls**: `/api/menu-items?merchantId=123&branchId=456`
- **Next.js route**: `src/app/api/menu-items/route.ts`
- **Backend calls**: `http://localhost:8080/api/v1/merchants/123/branches/456/menu/items`

#### **3. Digital Products API**
- **Frontend calls**: `/api/digital-products?merchantId=123`
- **Next.js route**: `src/app/api/digital-products/route.ts`
- **Backend calls**: `http://localhost:8080/api/v1/merchants/123/digital-products`

## 🔧 **How API Routes Work**

### **Example: Menu Items API Route**

```typescript
// src/app/api/menu-items/route.ts
export async function GET(request: NextRequest) {
  try {
    // 1. Extract parameters from frontend request
    const { searchParams } = new URL(request.url);
    const merchantId = searchParams.get('merchantId');
    const branchId = searchParams.get('branchId');
    
    // 2. Construct backend URL
    let url = `/merchants/${merchantId}`;
    if (branchId) {
      url += `/branches/${branchId}`;
    }
    url += '/menu/items';

    // 3. Call backend with authentication
    const response = await serverFetchClient(url, request, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
    });

    // 4. Return data to frontend
    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    // 5. Handle errors gracefully
    return NextResponse.json([]);
  }
}
```

### **Authentication Flow**

```typescript
// serverFetchClient automatically handles authentication
const authHeaders = await getBackendAuthHeaders(request);
// Generates:
// Authorization: Bearer <base64-encoded-token>
// X-User-ID: 100635498199217100373
// X-User-Email: <EMAIL>
// X-User-Role: user
```

## 🚀 **Request Flow Example**

### **1. Frontend Redux Call**
```typescript
// Component calls Redux hook
const { data: menuItems } = useGetMenuItemsQuery({
  merchantId: 'merchant-123',
  branchId: 'branch-456'
});
```

### **2. Redux API Slice**
```typescript
// Generates request to Next.js API route
GET /api/menu-items?merchantId=merchant-123&branchId=branch-456
```

### **3. Next.js API Route**
```typescript
// src/app/api/menu-items/route.ts processes request
// Calls backend with authentication
GET http://localhost:8080/api/v1/merchants/merchant-123/branches/branch-456/menu/items
Headers:
  Authorization: Bearer <token>
  X-User-Email: <EMAIL>
```

### **4. Go Backend**
```go
// Backend processes authenticated request
// Returns JSON data
{
  "data": [
    { "id": "1", "name": "Pizza", "price": 12.99 },
    { "id": "2", "name": "Burger", "price": 8.99 }
  ]
}
```

### **5. Response Chain**
```
Backend → Next.js API Route → Redux → Component
```

## 🛠️ **Adding New API Routes**

### **Step 1: Create Next.js API Route**
```typescript
// src/app/api/your-endpoint/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { serverFetchClient } from '@/lib/fetch/authFetchClient';

export async function GET(request: NextRequest) {
  try {
    const response = await serverFetchClient('/your-backend-endpoint', request);
    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    return NextResponse.json({ error: 'Failed to fetch data' }, { status: 500 });
  }
}
```

### **Step 2: Add Redux API Endpoint**
```typescript
// src/lib/redux/api/endpoints/yourApi.ts
export const yourApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    getYourData: builder.query<YourDataType[], void>({
      query: () => '/your-endpoint',
      providesTags: ['YourData'],
    }),
  }),
});
```

### **Step 3: Use in Component**
```typescript
// Component
import { useGetYourDataQuery } from '@/lib/redux/api/endpoints/yourApi';

const { data, isLoading, isError } = useGetYourDataQuery();
```

## 🔍 **Debugging API Routes**

### **Check Network Tab**
```
✅ Correct: GET /api/merchants
❌ Wrong:   GET http://localhost:8080/api/v1/merchants
```

### **API Route Logs**
```typescript
// Add logging to API routes
console.log('API Route called:', request.url);
console.log('Backend URL:', backendUrl);
console.log('Auth headers:', authHeaders);
```

### **Backend Logs**
```go
// Check backend receives requests with proper headers
log.Printf("Received request: %s %s", r.Method, r.URL.Path)
log.Printf("Auth header: %s", r.Header.Get("Authorization"))
```

## 🚨 **Common Issues & Solutions**

### **Issue: Direct Backend Calls**
```typescript
// ❌ Wrong - calls backend directly
baseUrl: 'http://localhost:8080/api/v1'

// ✅ Correct - calls Next.js API routes
baseUrl: '/api'
```

### **Issue: Missing API Routes**
```
Error: 404 Not Found /api/some-endpoint
Solution: Create src/app/api/some-endpoint/route.ts
```

### **Issue: Authentication Errors**
```
Error: 401 Unauthorized
Solution: Check serverFetchClient and getBackendAuthHeaders
```

## 📊 **Performance Benefits**

1. **Caching**: API routes can implement caching
2. **Rate Limiting**: Protect backend from abuse
3. **Data Transformation**: Modify data before sending to frontend
4. **Error Handling**: Graceful fallbacks and error messages
5. **Security**: Hide backend implementation details

## 🎯 **Next Steps**

1. **Test Current Setup**: Verify API routes are working
2. **Add More Routes**: Create routes for notifications, reviews, etc.
3. **Add Caching**: Implement Redis or in-memory caching
4. **Add Rate Limiting**: Protect against abuse
5. **Add Monitoring**: Log API performance and errors

This architecture provides a robust, secure, and scalable foundation for your API integration!
