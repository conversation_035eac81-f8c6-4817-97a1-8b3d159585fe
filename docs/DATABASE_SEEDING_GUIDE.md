# Database Seeding Guide

This guide explains how to seed your database with test data instead of using frontend mock data fallbacks.

## 🎯 **Philosophy: Real Data Only**

- ✅ **Frontend**: Only displays real data from API
- ✅ **Backend**: Returns real data from database
- ✅ **Testing**: Use database seeds for test data
- ❌ **No Mock Fallbacks**: Frontend never falls back to hardcoded mock data

## 🗄️ **Database Schema Requirements**

### **1. Merchants Table**
```sql
CREATE TABLE merchants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    description TEXT,
    logo_url TEXT,
    cover_image_url TEXT,
    business_type VARCHAR(100), -- 'restaurant', 'convenience', 'service', 'digital', 'retail'
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **2. Convenience Products Table**
```sql
CREATE TABLE convenience_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    stock INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(50) DEFAULT 'In Stock', -- 'In Stock', 'Low Stock', 'Out of Stock'
    image_url TEXT,
    barcode VARCHAR(100),
    supplier VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **3. Services Table**
```sql
CREATE TABLE services (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    duration INTEGER NOT NULL, -- in minutes
    category VARCHAR(100) NOT NULL,
    image_url TEXT,
    available BOOLEAN DEFAULT true,
    max_capacity INTEGER DEFAULT 1,
    requires_staff BOOLEAN DEFAULT false,
    preparation_time INTEGER DEFAULT 0, -- in minutes
    cleanup_time INTEGER DEFAULT 0, -- in minutes
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **4. Service Appointments Table**
```sql
CREATE TABLE service_appointments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    service_id UUID REFERENCES services(id) ON DELETE CASCADE,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255),
    customer_phone VARCHAR(50),
    appointment_date DATE NOT NULL,
    appointment_time TIME NOT NULL,
    status VARCHAR(50) DEFAULT 'Pending', -- 'Pending', 'Confirmed', 'Cancelled', 'Completed'
    duration INTEGER NOT NULL,
    notes TEXT,
    staff_id UUID, -- Reference to staff table if exists
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### **5. Digital Products Table**
```sql
CREATE TABLE digital_products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100) NOT NULL,
    image_url TEXT,
    download_url TEXT,
    file_size BIGINT, -- in bytes
    file_type VARCHAR(50),
    status VARCHAR(50) DEFAULT 'Active', -- 'Active', 'Inactive', 'Draft'
    sales_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🌱 **Seeding Scripts**

### **1. Seed Merchants**
```sql
-- Insert test merchants
INSERT INTO merchants (id, name, slug, email, phone, address, business_type, description) VALUES
('550e8400-e29b-41d4-a716-************', 'Scandine Shop', 'scandine-shop', '<EMAIL>', '+**************', '123 Main St, Bangkok, Thailand', 'convenience', 'Modern convenience store with wide selection'),
('550e8400-e29b-41d4-a716-************', 'Wellness Spa', 'wellness-spa', '<EMAIL>', '+**************', '456 Spa Ave, Bangkok, Thailand', 'service', 'Premium wellness and spa services'),
('550e8400-e29b-41d4-a716-************', 'Digital Store', 'digital-store', '<EMAIL>', '+**************', 'Online Only', 'digital', 'Digital products and downloads');
```

### **2. Seed Convenience Products**
```sql
-- Insert convenience products for Scandine Shop
INSERT INTO convenience_products (merchant_id, name, category, price, stock, status, image_url) VALUES
('550e8400-e29b-41d4-a716-************', 'Bottled Water', 'Beverages', 15.00, 120, 'In Stock', 'https://images.unsplash.com/photo-1616118132534-381148898bb4?q=80&w=1964&auto=format&fit=crop'),
('550e8400-e29b-41d4-a716-************', 'Energy Drink', 'Beverages', 35.00, 85, 'In Stock', 'https://images.unsplash.com/photo-1622543925917-763c34d1a86e?q=80&w=2070&auto=format&fit=crop'),
('550e8400-e29b-41d4-a716-************', 'Potato Chips', 'Snacks', 25.00, 42, 'In Stock', 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?q=80&w=2070&auto=format&fit=crop'),
('550e8400-e29b-41d4-a716-************', 'Instant Noodles', 'Food', 12.00, 8, 'Low Stock', 'https://images.unsplash.com/photo-1569718212165-3a8278d5f624?q=80&w=2070&auto=format&fit=crop'),
('550e8400-e29b-41d4-a716-************', 'Coffee', 'Beverages', 45.00, 0, 'Out of Stock', 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?q=80&w=2070&auto=format&fit=crop');
```

### **3. Seed Services**
```sql
-- Insert services for Wellness Spa
INSERT INTO services (merchant_id, name, description, price, duration, category, image_url, max_capacity, requires_staff) VALUES
('550e8400-e29b-41d4-a716-************', 'Swedish Massage', 'Relaxing full-body massage using Swedish techniques', 1200.00, 60, 'Massage', 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?q=80&w=2070&auto=format&fit=crop', 1, true),
('550e8400-e29b-41d4-a716-************', 'Facial Treatment', 'Deep cleansing facial with moisturizing treatment', 800.00, 45, 'Skincare', 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?q=80&w=2070&auto=format&fit=crop', 1, true),
('550e8400-e29b-41d4-a716-************', 'Deep Tissue Massage', 'Therapeutic massage targeting muscle tension', 1500.00, 90, 'Massage', 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?q=80&w=2069&auto=format&fit=crop', 1, true),
('550e8400-e29b-41d4-a716-************', 'Aromatherapy Session', 'Relaxing aromatherapy with essential oils', 900.00, 60, 'Wellness', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?q=80&w=2070&auto=format&fit=crop', 1, true);
```

### **4. Seed Service Appointments**
```sql
-- Insert sample appointments
INSERT INTO service_appointments (merchant_id, service_id, customer_name, customer_email, customer_phone, appointment_date, appointment_time, status, duration, total_price, notes) VALUES
('550e8400-e29b-41d4-a716-************', (SELECT id FROM services WHERE name = 'Swedish Massage' LIMIT 1), 'Sarah Johnson', '<EMAIL>', '+**************', '2024-01-15', '10:00:00', 'Confirmed', 60, 1200.00, 'First time customer'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM services WHERE name = 'Facial Treatment' LIMIT 1), 'Michael Chen', '<EMAIL>', '+**************', '2024-01-15', '14:30:00', 'Pending', 45, 800.00, 'Sensitive skin'),
('550e8400-e29b-41d4-a716-************', (SELECT id FROM services WHERE name = 'Deep Tissue Massage' LIMIT 1), 'Emma Wilson', '<EMAIL>', '+**************', '2024-01-16', '11:15:00', 'Confirmed', 90, 1500.00, 'Focus on back and shoulders');
```

### **5. Seed Digital Products**
```sql
-- Insert digital products
INSERT INTO digital_products (merchant_id, name, description, price, category, image_url, status, sales_count) VALUES
('550e8400-e29b-41d4-a716-************', 'E-book: Digital Marketing Guide', 'Comprehensive guide to digital marketing strategies', 299.00, 'E-books', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?q=80&w=2070&auto=format&fit=crop', 'Active', 156),
('550e8400-e29b-41d4-a716-************', 'Stock Photos Bundle', 'High-quality stock photos for commercial use', 599.00, 'Graphics', 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=2070&auto=format&fit=crop', 'Active', 89),
('550e8400-e29b-41d4-a716-************', 'Web Template Pack', 'Modern responsive web templates', 799.00, 'Templates', 'https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?q=80&w=2069&auto=format&fit=crop', 'Active', 234);
```

## 🚀 **Implementation Steps**

### **Step 1: Create Database Tables**
Run the schema creation scripts in your database (PostgreSQL, MySQL, etc.)

### **Step 2: Run Seeding Scripts**
Execute the INSERT statements to populate your database with test data

### **Step 3: Update Backend API**
Ensure your Go backend connects to the database and returns real data

### **Step 4: Test Frontend**
- Frontend should now display real data from database
- No mock data fallbacks
- Proper error handling when backend is unavailable

## 🔧 **Backend Integration**

Your Go backend should implement endpoints that query these tables:

```go
// Example: Get convenience products
func GetConvenienceProducts(c *gin.Context) {
    merchantID := c.Param("merchantId")
    
    var products []ConvenienceProduct
    err := db.Where("merchant_id = ?", merchantID).Find(&products).Error
    if err != nil {
        c.JSON(500, gin.H{"error": "Failed to fetch products"})
        return
    }
    
    c.JSON(200, products)
}
```

## ✅ **Benefits of Database Seeding**

1. **Real Data Flow**: Complete end-to-end real data
2. **Consistent Testing**: Same test data across environments
3. **Production-like**: Behavior matches production
4. **No Frontend Coupling**: Test data managed at database level
5. **Easy Maintenance**: Update seeds instead of frontend code

## 🧪 **Testing Your Seeds**

After seeding:
1. **Start your backend server**
2. **Visit frontend pages**
3. **Verify real data displays**
4. **Check that empty states work when no data exists**
5. **Confirm error handling when backend is down**

This approach ensures your application always uses real data and provides a production-like experience during development!
