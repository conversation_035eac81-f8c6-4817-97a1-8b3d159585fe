# Generic API Proxy Route

## Overview

The generic API proxy route (`src/app/api/[...services]/route.ts`) provides a unified way to proxy all API requests to the backend without creating individual route files for each endpoint.

## How It Works

### Route Pattern
- **Path**: `/api/[...services]`
- **Catches**: All API routes except those with specific route files (like `/api/auth/*`)
- **Methods**: GET, POST, PUT, PATCH, DELETE

### Request Flow
1. Frontend makes request to `/api/some/endpoint`
2. Next.js routes to `[...services]/route.ts`
3. Route extracts path segments and rebuilds backend URL
4. Forwards request to backend with authentication
5. Returns response to frontend

## Features

### ✅ Authentication Handling
- Uses `serverFetchClient` for automatic auth header injection
- Includes NextAuth session management
- Forwards user context to backend

### ✅ All HTTP Methods
- GET, POST, PUT, PATCH, DELETE
- Proper status code handling (201 for POST, 200 for others)
- Special handling for DELETE responses

### ✅ Request Forwarding
- Preserves query parameters
- Forwards request body for POST/PUT/PATCH
- Maintains headers and content type

### ✅ Error Handling
- Consistent error response format
- Proper HTTP status codes
- Detailed logging for debugging

## Usage Examples

### Frontend API Calls
```typescript
// These calls will be automatically proxied:

// GET request
fetch('/api/shops/123/staff')
// → Backend: GET /shops/123/staff

// POST request with body
fetch('/api/shops/123/staff', {
  method: 'POST',
  body: JSON.stringify({ name: 'John Doe' })
})
// → Backend: POST /shops/123/staff

// With query parameters
fetch('/api/shops/123/staff?page=1&limit=10')
// → Backend: GET /shops/123/staff?page=1&limit=10
```

### Redux RTK Query
```typescript
// Works seamlessly with existing RTK Query endpoints
const staffApi = createApi({
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  endpoints: (builder) => ({
    getStaff: builder.query({
      query: ({ shopId, ...params }) => ({
        url: `shops/${shopId}/staff`,
        params
      })
    })
  })
});
```

## Route Exclusions

### Auth Routes
Auth routes (`/api/auth/*`) are handled by their specific route files:
- `/api/auth/[...nextauth]` - NextAuth handler
- `/api/auth/login` - Custom login
- `/api/auth/register` - User registration

### Special Routes
Any route with a specific route file will take precedence over the generic proxy.

## Migration from Specific Routes

### ✅ Removed (Simple Proxies)
The following routes were successfully removed as they were simple proxies:
- `/api/orders/route.ts`
- `/api/purchase-orders/route.ts`
- `/api/services/route.ts`
- `/api/digital-products/route.ts`
- `/api/convenience-products/route.ts`
- `/api/service-appointments/route.ts`
- `/api/users/route.ts`
- `/api/staff/[...staff]/route.ts`
- `/api/shops/route.ts`
- `/api/shops/[shopId]/route.ts`
- `/api/merchants/route.ts`
- `/api/merchants/[merchantId]/route.ts`

### 🔄 Kept (Special Logic Required)
These routes remain because they have special logic:
- `/api/auth/*` - NextAuth and custom authentication
- `/api/booking-widget/*` - Widget validation and API key logic
- `/api/menu-items/route.ts` - Mock data fallback for development
- `/api/inventory/route.ts` - Complex data transformation
- `/api/notifications/*` - Mock data and special notification logic
- `/api/reports/route.ts` - Data transformation and analytics
- `/api/test-auth/route.ts` - Authentication testing
- `/api/test-backend-auth/route.ts` - Backend auth testing

### After Migration
```
src/app/api/
├── [...services]/route.ts     ← Handles all simple proxies
├── auth/                      ← NextAuth routes
├── booking-widget/            ← Widget-specific logic
├── inventory/route.ts         ← Data transformation
├── menu-items/route.ts        ← Mock data fallback
├── notifications/             ← Mock data & special logic
├── reports/route.ts           ← Analytics & transformation
└── test-*/                    ← Testing routes
```

## Benefits

1. **Reduced Boilerplate**: No need to create individual proxy files
2. **Consistent Auth**: All routes use the same authentication pattern
3. **Easier Maintenance**: Single file to update for proxy logic changes
4. **Automatic Coverage**: New backend endpoints work immediately
5. **Type Safety**: Maintains TypeScript support

## Debugging

The proxy logs detailed information for each request:
```typescript
console.log('Generic API proxy:', {
  method: 'GET',
  servicePath: 'shops/123/staff',
  backendUrl: '/shops/123/staff?page=1',
  queryParams: { page: '1' },
  hasSession: true
});
```

## Backend URL Mapping

| Frontend Request | Backend Request |
|-----------------|----------------|
| `/api/shops/123` | `/shops/123` |
| `/api/staff?shopSlug=abc` | `/staff?shopSlug=abc` |
| `/api/orders/456/items` | `/orders/456/items` |

## Error Responses

```typescript
// Standard error format
{
  "error": "Failed to fetch data",
  "status": 500
}

// Auth route error
{
  "error": "Auth routes should not be proxied through this handler",
  "status": 400
}
```
