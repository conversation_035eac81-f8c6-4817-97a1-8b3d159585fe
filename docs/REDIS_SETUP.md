# Redis Setup for Local Development

## Quick Start

### 1. Start Redis
```bash
make redis-start
```

This will:
- Start Redis server on `localhost:6379`
- Start Redis Commander UI on `http://localhost:8081`
- Create persistent data volume

### 2. Verify Redis is Running
```bash
make redis-status
```

### 3. Start Your Backend
```bash
make backend
```

The backend will automatically connect to Redis and start the AI job queue workers.

## Available Commands

| Command | Description |
|---------|-------------|
| `make redis-start` | Start Redis server (Docker) |
| `make redis-stop` | Stop Redis server |
| `make redis-status` | Check Redis status and queue stats |
| `make redis-logs` | View Redis logs |
| `make redis-cli` | Connect to Redis CLI |
| `make redis-ui` | Open Redis Commander UI |

## Redis Commander UI

Access the Redis UI at: **http://localhost:8081**
- Username: `admin`
- Password: `admin`

You can monitor:
- AI generation job queue (`ai_generation_jobs`)
- Pub/sub channels (`ai_job_notifications`)
- Memory usage and performance metrics

## Queue Monitoring

### Check Queue Size
```bash
# Via CLI
make redis-cli
> ZCARD ai_generation_jobs

# Via status command
make redis-status
```

### Monitor Job Processing
```bash
# Watch queue in real-time
make redis-cli
> MONITOR
```

### View Pending Jobs
```bash
make redis-cli
> ZRANGE ai_generation_jobs 0 -1 WITHSCORES
```

## Configuration

Redis is configured for local development with:
- **Host**: localhost
- **Port**: 6379
- **Password**: (none)
- **Database**: 0
- **Memory Limit**: 512MB
- **Persistence**: AOF + RDB snapshots

## AI Queue Features

### Priority Levels
- **1**: High priority (food_images)
- **2**: Normal priority (default)
- **3**: Low priority

### Worker Configuration
- **3 workers** by default
- **Pub/sub notifications** for immediate processing
- **Automatic retries** for failed jobs
- **Periodic queue checking** (fallback)

### Job Flow
1. User uploads image → API creates job in DB
2. Job enqueued in Redis with priority
3. Pub/sub notification sent to workers
4. Available worker processes job
5. WebSocket updates sent to frontend
6. Job marked as completed/failed

## Troubleshooting

### Redis Not Starting
```bash
# Check Docker is running
docker ps

# Check logs
make redis-logs

# Restart Redis
make redis-stop
make redis-start
```

### Backend Can't Connect to Redis
1. Verify Redis is running: `make redis-status`
2. Check environment variables in `.env`
3. Restart backend: `make backend`

### Queue Not Processing Jobs
1. Check worker logs in backend console
2. Verify Redis connection: `make redis-cli` then `PING`
3. Check queue size: `make redis-status`

### Clear Queue (Development)
```bash
make redis-cli
> FLUSHDB
```

## Production Considerations

For production deployment:
- Use Redis Cluster for high availability
- Configure authentication and SSL
- Set up monitoring and alerting
- Adjust memory limits based on load
- Consider Redis Sentinel for failover

## Environment Variables

Add to your `.env` file:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

## Docker Compose

The Redis setup uses `docker-compose.redis.yml`:
- Redis 7 Alpine image
- Persistent data volume
- Redis Commander for monitoring
- Optimized configuration for AI workloads
