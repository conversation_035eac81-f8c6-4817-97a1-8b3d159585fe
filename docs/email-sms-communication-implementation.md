# Email and SMS Communication Service Implementation Plan

This document outlines the implementation plan for the Email and SMS Communication Service feature.

## Overview

The Email and SMS Communication Service provides functionality for sending automated and manual messages to customers, including:

- Email templates for appointment confirmations, reminders, and follow-ups
- SMS notifications for immediate communications
- Campaign management for marketing messages
- Customizable templates with merchant branding
- Scheduled communications based on appointment status changes

## Implementation Steps

### 1. Service Layer Implementation ✅

- Create `communicationService.ts` file with the following functionality:
  - Template management (CRUD operations)
  - Email sending functionality
  - SMS sending functionality
  - Template-based communication
  - Scheduled communications

### 2. Data Validation ✅

- Create `communicationSchema.ts` with Zod schemas for:
  - Communication templates
  - Email and SMS messages
  - Communication campaigns

### 3. API Route Handlers ✅

- Create API routes for:
  - Template management (`/api/merchants/[merchantId]/communications/templates`)
  - Sending communications (`/api/merchants/[merchantId]/communications/send`)

### 4. Redux Integration ✅

- Create RTK Query endpoints in `communicationApi.ts`
- Update API slice to include Communications tag

### 5. UI Components ✅

- Create UI components for:
  - Template management (list, create, edit, delete) ✅
  - Email and SMS sending ✅
  - Campaign management (placeholder implementation) ✅
  - Template editor with variable support ✅
  - Integrated shadcn UI components for consistent design ✅

### 6. Integration with Existing Features

- Integrate with Appointment Service: ✅
  - Send confirmation emails/SMS when appointments are created ✅
  - Send reminder emails/SMS before appointments ✅
  - Send cancellation notifications when appointments are cancelled ✅
  - Added UI components for managing appointment communications ✅

- Integrate with Customer Service: ✅
  - Send birthday greetings ✅
  - Send special offers to loyal customers ✅
  - Send re-engagement campaigns to inactive customers ✅
  - Added UI components for managing customer communications ✅

### 7. Third-Party Service Integration ✅

- Integrate with email service providers (e.g., SendGrid, Mailgun) ✅
  - Created provider interface and mock implementation
  - Added support for real providers (commented out until needed)
- Integrate with SMS service providers (e.g., Twilio, Nexmo) ✅
  - Created provider interface and mock implementation
  - Added support for real providers (commented out until needed)
- Implement error handling for failed communications ✅

## Database Schema

The following database tables will be needed:

### CommunicationTemplate

| Field       | Type                | Description                                |
|-------------|---------------------|--------------------------------------------|
| id          | string              | Unique identifier                          |
| merchantId  | string              | Reference to merchant                      |
| name        | string              | Template name                              |
| type        | enum                | 'email' or 'sms'                           |
| subject     | string (optional)   | Email subject line                         |
| content     | string              | Template content with variable placeholders|
| variables   | string[]            | List of variables used in the template     |
| isDefault   | boolean             | Whether this is a default template         |
| category    | enum                | Template category                          |
| createdAt   | datetime            | Creation timestamp                         |
| updatedAt   | datetime            | Last update timestamp                      |

### Communication

| Field       | Type                | Description                                |
|-------------|---------------------|--------------------------------------------|
| id          | string              | Unique identifier                          |
| merchantId  | string              | Reference to merchant                      |
| userId      | string (optional)   | Reference to user                          |
| templateId  | string (optional)   | Reference to template                      |
| type        | enum                | 'email' or 'sms'                           |
| recipient   | string              | Email address or phone number              |
| subject     | string (optional)   | Email subject line                         |
| content     | string              | Message content                            |
| status      | enum                | 'pending', 'sent', or 'failed'             |
| scheduledFor| datetime (optional) | When to send the communication             |
| sentAt      | datetime (optional) | When the communication was sent            |
| metadata    | jsonb (optional)    | Additional data                            |
| createdAt   | datetime            | Creation timestamp                         |
| updatedAt   | datetime            | Last update timestamp                      |

### CommunicationCampaign

| Field       | Type                | Description                                |
|-------------|---------------------|--------------------------------------------|
| id          | string              | Unique identifier                          |
| merchantId  | string              | Reference to merchant                      |
| name        | string              | Campaign name                              |
| type        | enum                | 'email' or 'sms'                           |
| templateId  | string              | Reference to template                      |
| recipients  | string[]            | List of recipients                         |
| scheduledFor| datetime (optional) | When to send the campaign                  |
| status      | enum                | Campaign status                            |
| metadata    | jsonb (optional)    | Additional data                            |
| createdAt   | datetime            | Creation timestamp                         |
| updatedAt   | datetime            | Last update timestamp                      |

## Implementation Progress

### Completed
1. ✅ Service layer implementation with template management and communication sending
2. ✅ Data validation with Zod schemas
3. ✅ API route handlers for templates and sending communications
4. ✅ Redux integration with RTK Query endpoints
5. ✅ UI components for template management and sending communications
6. ✅ Third-party service integration with provider interfaces and mock implementations
7. ✅ Integration with Appointment Service for automated communications
8. ✅ UI components for managing appointment communications
9. ✅ Campaign management functionality
   - ✅ Campaign service for creating and managing campaigns
   - ✅ Campaign segment service for targeting specific customer groups
   - ✅ API routes for campaign and segment management
   - ✅ RTK Query endpoints for campaign and segment operations
   - ✅ UI components for creating and managing campaigns
10. ✅ Integration with Customer Service for marketing campaigns
    - ✅ Customer communication service for birthday greetings and loyalty offers
    - ✅ Re-engagement campaigns for inactive customers
    - ✅ API routes for customer communications
    - ✅ RTK Query endpoints for customer communications
    - ✅ UI components for managing customer communications
11. ✅ Communication analytics
    - ✅ Analytics service for tracking communication performance
    - ✅ Event tracking for deliveries, opens, clicks, etc.
    - ✅ API routes for analytics data
    - ✅ RTK Query endpoints for analytics
    - ✅ UI components for visualizing analytics data
12. ✅ Real email and SMS provider integration
    - ✅ SendGrid and Mailgun integration for email
    - ✅ Twilio and Nexmo/Vonage integration for SMS
    - ✅ Tracking utilities for email opens and clicks
    - ✅ Unsubscribe functionality for marketing emails
    - ✅ UI components for configuring providers

### Next Steps
1. Implement A/B testing for communication templates
2. Add more advanced segmentation options for campaigns
3. Implement message scheduling and throttling for high-volume campaigns
