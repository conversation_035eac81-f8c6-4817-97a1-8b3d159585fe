# Internationalization (i18n) Guide

This document provides guidance on how to use internationalization (i18n) in the ADC Shop Merchants project.

## Overview

The project uses [i18next](https://www.i18next.com/) and [react-i18next](https://react.i18next.com/) for internationalization, which provides a simple and flexible way to handle translations in Next.js applications.

## Project Structure

- **Translations**: Located in `public/locales/{locale}/common.json`
- **Configuration**: Main configuration in `src/lib/i18n/i18n.ts`
- **Language Switcher**: Component in `src/components/LanguageSwitcher.tsx`

## Supported Languages

Currently, the application supports the following languages:

- English (en) - Default
- Thai (th)

## Adding Translations

To add a new translation:

1. Add your translation key and value to `public/locales/en/common.json` for English
2. Add the corresponding translation to `public/locales/th/common.json` for Thai

Example:

```json
// public/locales/en/common.json
{
  "myFeature": {
    "title": "My Feature",
    "description": "This is my feature"
  }
}

// public/locales/th/common.json
{
  "myFeature": {
    "title": "คุณลักษณะของฉัน",
    "description": "นี่คือคุณลักษณะของฉัน"
  }
}
```

## Using Translations in Components

### Client Components

```tsx
'use client';

import { useTranslation } from 'react-i18next';

export default function MyComponent() {
  const { t } = useTranslation('common');

  return (
    <div>
      <h1>{t('myFeature.title')}</h1>
      <p>{t('myFeature.description')}</p>
    </div>
  );
}
```

## Switching Languages

The project includes a `LanguageSwitcher` component that allows users to switch between languages:

```tsx
import LanguageSwitcher from '@/components/LanguageSwitcher';

export default function Header() {
  return (
    <header>
      <h1>My App</h1>
      <LanguageSwitcher />
    </header>
  );
}
```

## Adding a New Language

To add a new language:

1. Create a new translation file in `public/locales/{new-locale}/common.json`
2. Add the new language option to the `LanguageSwitcher` component
3. Add the language name to the `language` section in all translation files

## Best Practices

1. **Use Namespaces**: Organize translations using namespaces (e.g., `auth.login`, `products.title`)
2. **Keep Keys Consistent**: Use the same keys across all language files
3. **Use Variables**: For dynamic content, use variables in translations
4. **Document New Keys**: When adding new translation keys, document them for other developers
5. **Test All Languages**: When implementing a new feature, test it in all supported languages

## Formatting

i18next supports various formatting options:

### Date Formatting

```tsx
import { useTranslation } from 'react-i18next';

export default function DateDisplay({ date }) {
  const { t, i18n } = useTranslation('common');

  return (
    <div>
      {new Intl.DateTimeFormat(i18n.language, { dateStyle: 'full' }).format(date)}
    </div>
  );
}
```

### Number Formatting

```tsx
import { useTranslation } from 'react-i18next';

export default function PriceDisplay({ price }) {
  const { t, i18n } = useTranslation('common');

  return (
    <div>
      {new Intl.NumberFormat(i18n.language, { style: 'currency', currency: 'THB' }).format(price)}
    </div>
  );
}
```

## Resources

- [i18next Documentation](https://www.i18next.com/)
- [react-i18next Documentation](https://react.i18next.com/)
- [Internationalization in Next.js](https://nextjs.org/docs/app/building-your-application/routing/internationalization)
