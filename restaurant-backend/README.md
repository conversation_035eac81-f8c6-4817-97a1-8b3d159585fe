# Restaurant Management API

A comprehensive REST API for restaurant management built with Go, Gin, and PostgreSQL.

## Features

- **Authentication & Authorization**: JWT-based authentication with role-based access control
- **Multi-tenant Architecture**: Support for multiple restaurant chains and branches
- **Order Management**: Complete order lifecycle management
- **Menu Management**: Dynamic menu with categories, items, and options
- **Staff Management**: User management with roles and permissions
- **Table Management**: Table layout and reservation system
- **Review Management**: Customer review aggregation and response system
- **Analytics & Reporting**: Sales trends and performance metrics
- **Real-time Updates**: WebSocket support for live order updates

## Tech Stack

- **Backend**: Go 1.21, Gin Web Framework
- **Database**: PostgreSQL with GORM ORM
- **Cache**: Redis
- **Authentication**: JWT tokens
- **Documentation**: Swagger/OpenAPI
- **Containerization**: Docker & Docker Compose
- **Monitoring**: Prometheus metrics

## Quick Start

### Prerequisites

- Go 1.21 or higher
- PostgreSQL 15+
- Redis 7+
- Docker & Docker Compose (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd restaurant-backend
   ```

2. **Install dependencies**
   ```bash
   make deps
   ```

3. **Setup environment**
   ```bash
   make setup
   # Edit .env file with your configuration
   ```

4. **Run with Docker Compose (Recommended)**
   ```bash
   make compose-up
   ```

   Or run locally:
   ```bash
   # Start PostgreSQL and Redis
   make run
   ```

### Development

1. **Install development tools**
   ```bash
   make install-tools
   ```

2. **Run with hot reload**
   ```bash
   make dev
   ```

3. **Run tests**
   ```bash
   make test
   make test-coverage
   ```

4. **Generate API documentation**
   ```bash
   make swagger
   ```

## API Documentation

Once the server is running, you can access:

- **API Documentation**: http://localhost:8080/docs/
- **Health Check**: http://localhost:8080/health
- **Metrics**: http://localhost:8080/metrics

## Configuration

The application can be configured using environment variables or a config file. See `.env.example` for all available options.

### Key Configuration Options

```env
# Server
PORT=8080
ENV=development

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=restaurant_db
DB_USER=restaurant_user
DB_PASSWORD=restaurant_pass

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=24h

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
```

## API Endpoints

### Authentication
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/refresh` - Refresh token
- `GET /api/v1/auth/me` - Get current user

### Orders
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/orders` - Get orders
- `POST /api/v1/merchants/{merchantId}/branches/{branchId}/orders` - Create order
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/orders/{orderId}` - Get order
- `PUT /api/v1/merchants/{merchantId}/branches/{branchId}/orders/{orderId}` - Update order
- `PATCH /api/v1/merchants/{merchantId}/branches/{branchId}/orders/{orderId}/status` - Update status

### Menu
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/menu/categories` - Get categories
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/menu/items` - Get menu items
- `POST /api/v1/merchants/{merchantId}/branches/{branchId}/menu/items` - Create menu item

### Staff
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/staff` - Get staff
- `POST /api/v1/merchants/{merchantId}/branches/{branchId}/staff` - Create staff member

### Tables
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/tables` - Get tables
- `POST /api/v1/merchants/{merchantId}/branches/{branchId}/tables` - Create table

### Reservations
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/reservations` - Get reservations
- `POST /api/v1/merchants/{merchantId}/branches/{branchId}/reservations` - Create reservation

### Reviews
- `GET /api/v1/merchants/{merchantId}/branches/{branchId}/reviews` - Get reviews
- `POST /api/v1/merchants/{merchantId}/branches/{branchId}/reviews/{reviewId}/respond` - Respond to review

## Database Schema

The application uses PostgreSQL with the following main entities:

- **Merchants**: Restaurant chains or single restaurant owners
- **Branches**: Individual restaurant locations
- **Users**: Staff members with role-based permissions
- **Orders**: Customer orders with items and payments
- **Menu**: Categories and items with options and pricing
- **Tables**: Table layout and management
- **Reservations**: Table reservations and scheduling
- **Reviews**: Customer reviews and responses

## Development

### Project Structure

```
restaurant-backend/
├── cmd/server/          # Application entry point
├── internal/
│   ├── api/
│   │   ├── handlers/    # HTTP handlers
│   │   ├── middleware/  # HTTP middleware
│   │   └── routes/      # Route definitions
│   ├── config/          # Configuration management
│   ├── database/        # Database connection and migrations
│   ├── models/          # Data models
│   ├── repositories/    # Data access layer
│   ├── services/        # Business logic layer
│   └── utils/           # Utility functions
├── pkg/                 # Public packages
├── migrations/          # Database migrations
├── docs/               # API documentation
├── docker/             # Docker configurations
└── scripts/            # Build and deployment scripts
```

### Adding New Features

1. **Define Models**: Add new models in `internal/models/`
2. **Create Repository**: Implement data access in `internal/repositories/`
3. **Add Service**: Implement business logic in `internal/services/`
4. **Create Handler**: Add HTTP handlers in `internal/api/handlers/`
5. **Define Routes**: Add routes in `internal/api/routes/`
6. **Add Tests**: Write tests for all layers
7. **Update Documentation**: Add Swagger annotations

### Testing

```bash
# Run all tests
make test

# Run tests with coverage
make test-coverage

# Run tests with race detection
make test-race

# Run specific test
go test -v ./internal/services/
```

### Code Quality

```bash
# Format code
make fmt

# Lint code
make lint

# Security scan
make security

# Run all checks
make check
```

## Deployment

### Docker

```bash
# Build image
make docker-build

# Run container
make docker-run

# Push to registry
make docker-push
```

### Production

1. **Build for production**
   ```bash
   make build-prod
   ```

2. **Run migrations**
   ```bash
   make migrate-up
   ```

3. **Start server**
   ```bash
   ./restaurant-api
   ```

## Monitoring

The application includes:

- **Health Checks**: `/health` and `/ready` endpoints
- **Metrics**: Prometheus metrics at `/metrics`
- **Logging**: Structured JSON logging
- **Tracing**: Request ID tracking

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run quality checks: `make check`
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:

- Create an issue on GitHub
- Check the API documentation
- Review the example requests in the docs

## Roadmap

- [ ] WebSocket support for real-time updates
- [ ] Advanced analytics and reporting
- [ ] Integration with external payment systems
- [ ] Mobile app API enhancements
- [ ] Multi-language support
- [ ] Advanced inventory management
