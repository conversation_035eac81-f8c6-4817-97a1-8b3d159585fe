# Additional Integrations Guide

## Overview

This guide covers integrating the Restaurant Management API with external services, third-party platforms, and additional tools to enhance functionality and business operations.

## Payment Processing Integrations

### 1. Stripe Integration

```go
// Stripe payment processor implementation
package payments

import (
    "github.com/stripe/stripe-go/v74"
    "github.com/stripe/stripe-go/v74/paymentintent"
    "github.com/stripe/stripe-go/v74/customer"
)

type StripeProcessor struct {
    secretKey string
}

func NewStripeProcessor(secretKey string) *StripeProcessor {
    stripe.Key = secretKey
    return &StripeProcessor{secretKey: secretKey}
}

func (s *StripeProcessor) CreatePaymentIntent(amount int64, currency string, customerID string) (*stripe.PaymentIntent, error) {
    params := &stripe.PaymentIntentParams{
        Amount:   stripe.Int64(amount),
        Currency: stripe.String(currency),
        Customer: stripe.String(customerID),
        Metadata: map[string]string{
            "integration": "restaurant-api",
        },
    }

    return paymentintent.New(params)
}

func (s *StripeProcessor) CreateCustomer(email, name string) (*stripe.Customer, error) {
    params := &stripe.CustomerParams{
        Email: stripe.String(email),
        Name:  stripe.String(name),
    }

    return customer.New(params)
}

// Webhook handler for Stripe events
func (s *StripeProcessor) HandleWebhook(payload []byte, signature string) error {
    event, err := webhook.ConstructEvent(payload, signature, s.webhookSecret)
    if err != nil {
        return err
    }

    switch event.Type {
    case "payment_intent.succeeded":
        var paymentIntent stripe.PaymentIntent
        err := json.Unmarshal(event.Data.Raw, &paymentIntent)
        if err != nil {
            return err
        }
        // Update order status
        return s.handlePaymentSuccess(&paymentIntent)
    
    case "payment_intent.payment_failed":
        var paymentIntent stripe.PaymentIntent
        err := json.Unmarshal(event.Data.Raw, &paymentIntent)
        if err != nil {
            return err
        }
        // Handle payment failure
        return s.handlePaymentFailure(&paymentIntent)
    }

    return nil
}
```

### 2. Square Integration

```go
// Square payment processor
type SquareProcessor struct {
    client *squareup.Client
    config SquareConfig
}

func NewSquareProcessor(config SquareConfig) *SquareProcessor {
    client := squareup.NewClient(config.AccessToken, config.Environment)
    return &SquareProcessor{
        client: client,
        config: config,
    }
}

func (s *SquareProcessor) CreatePayment(amount int64, sourceID string) (*models.Payment, error) {
    request := &models.CreatePaymentRequest{
        SourceId:      sourceID,
        AmountMoney:   &models.Money{Amount: amount, Currency: "USD"},
        IdempotencyKey: uuid.New().String(),
    }

    response, err := s.client.PaymentsApi.CreatePayment(context.Background(), request)
    if err != nil {
        return nil, err
    }

    return response.Payment, nil
}
```

## Delivery Platform Integrations

### 1. Uber Eats Integration

```go
// Uber Eats API integration
type UberEatsIntegration struct {
    client     *http.Client
    baseURL    string
    clientID   string
    clientSecret string
    storeID    string
}

func NewUberEatsIntegration(config UberEatsConfig) *UberEatsIntegration {
    return &UberEatsIntegration{
        client:       &http.Client{Timeout: 30 * time.Second},
        baseURL:      config.BaseURL,
        clientID:     config.ClientID,
        clientSecret: config.ClientSecret,
        storeID:      config.StoreID,
    }
}

// Sync menu to Uber Eats
func (u *UberEatsIntegration) SyncMenu(menu []*models.MenuItem) error {
    menuData := u.transformMenuForUberEats(menu)
    
    req, err := http.NewRequest("PUT", 
        fmt.Sprintf("%s/v1/eats/stores/%s/menus", u.baseURL, u.storeID),
        bytes.NewBuffer(menuData))
    if err != nil {
        return err
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+u.getAccessToken())

    resp, err := u.client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("failed to sync menu: %d", resp.StatusCode)
    }

    return nil
}

// Handle incoming orders from Uber Eats
func (u *UberEatsIntegration) HandleOrderWebhook(payload []byte) error {
    var orderData UberEatsOrder
    if err := json.Unmarshal(payload, &orderData); err != nil {
        return err
    }

    // Transform to internal order format
    order := u.transformUberEatsOrder(&orderData)
    
    // Create order in system
    return u.orderService.CreateOrder(context.Background(), order.BranchID, order)
}

type UberEatsOrder struct {
    ID          string                `json:"id"`
    DisplayID   string                `json:"display_id"`
    Type        string                `json:"type"`
    Items       []UberEatsOrderItem   `json:"cart"`
    Customer    UberEatsCustomer      `json:"eater"`
    DeliveryInfo UberEatsDeliveryInfo `json:"delivery"`
}
```

### 2. DoorDash Integration

```go
// DoorDash API integration
type DoorDashIntegration struct {
    client       *http.Client
    baseURL      string
    developerID  string
    keyID        string
    signingSecret string
}

func (d *DoorDashIntegration) CreateDelivery(order *models.Order) (*DoorDashDelivery, error) {
    deliveryRequest := &DoorDashDeliveryRequest{
        ExternalDeliveryID: order.ID.String(),
        PickupAddress:      d.getRestaurantAddress(),
        DropoffAddress:     d.getCustomerAddress(order),
        Items:             d.transformOrderItems(order.Items),
        OrderValue:        int(order.Total * 100), // Convert to cents
    }

    payload, err := json.Marshal(deliveryRequest)
    if err != nil {
        return nil, err
    }

    req, err := http.NewRequest("POST", d.baseURL+"/drive/v2/deliveries", bytes.NewBuffer(payload))
    if err != nil {
        return nil, err
    }

    // Add DoorDash authentication headers
    d.addAuthHeaders(req, payload)

    resp, err := d.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var delivery DoorDashDelivery
    err = json.NewDecoder(resp.Body).Decode(&delivery)
    return &delivery, err
}
```

## Accounting System Integrations

### 1. QuickBooks Integration

```go
// QuickBooks API integration
type QuickBooksIntegration struct {
    client       *http.Client
    baseURL      string
    companyID    string
    accessToken  string
    refreshToken string
}

func (q *QuickBooksIntegration) CreateSalesReceipt(order *models.Order) error {
    salesReceipt := &QuickBooksSalesReceipt{
        CustomerRef: QuickBooksRef{Value: order.CustomerID.String()},
        Line: []QuickBooksLine{},
        TotalAmt: order.Total,
    }

    // Add line items
    for _, item := range order.Items {
        line := QuickBooksLine{
            Amount: item.Total,
            DetailType: "SalesItemLineDetail",
            SalesItemLineDetail: QuickBooksSalesItemLineDetail{
                ItemRef: QuickBooksRef{Value: item.MenuItemID.String()},
                Qty:     item.Quantity,
            },
        }
        salesReceipt.Line = append(salesReceipt.Line, line)
    }

    payload, err := json.Marshal(salesReceipt)
    if err != nil {
        return err
    }

    req, err := http.NewRequest("POST", 
        fmt.Sprintf("%s/v3/company/%s/salesreceipt", q.baseURL, q.companyID),
        bytes.NewBuffer(payload))
    if err != nil {
        return err
    }

    req.Header.Set("Authorization", "Bearer "+q.accessToken)
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Accept", "application/json")

    resp, err := q.client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()

    return q.handleResponse(resp)
}
```

### 2. Xero Integration

```go
// Xero accounting integration
type XeroIntegration struct {
    client      *http.Client
    tenantID    string
    accessToken string
}

func (x *XeroIntegration) CreateInvoice(order *models.Order) error {
    invoice := &XeroInvoice{
        Type: "ACCREC",
        Contact: XeroContact{
            Name: order.CustomerName,
        },
        Date: time.Now().Format("2006-01-02"),
        DueDate: time.Now().AddDate(0, 0, 30).Format("2006-01-02"),
        LineItems: []XeroLineItem{},
        Status: "AUTHORISED",
    }

    // Add line items
    for _, item := range order.Items {
        lineItem := XeroLineItem{
            Description: item.Name,
            Quantity:    float64(item.Quantity),
            UnitAmount:  item.Price,
            AccountCode: "200", // Sales account
        }
        invoice.LineItems = append(invoice.LineItems, lineItem)
    }

    return x.sendToXero("Invoices", invoice)
}
```

## Marketing and Communication Integrations

### 1. Twilio SMS Integration

```go
// Twilio SMS service
type TwilioSMSService struct {
    client    *twilio.RestClient
    fromPhone string
}

func NewTwilioSMSService(accountSID, authToken, fromPhone string) *TwilioSMSService {
    client := twilio.NewRestClientWithParams(twilio.ClientParams{
        Username: accountSID,
        Password: authToken,
    })

    return &TwilioSMSService{
        client:    client,
        fromPhone: fromPhone,
    }
}

func (t *TwilioSMSService) SendOrderConfirmation(order *models.Order) error {
    if order.CustomerPhone == "" {
        return nil // No phone number provided
    }

    message := fmt.Sprintf(
        "Order confirmed! Order #%s for $%.2f will be ready in %d minutes. Thank you!",
        order.OrderNumber,
        order.Total,
        order.EstimatedTime,
    )

    params := &api.CreateMessageParams{}
    params.SetTo(order.CustomerPhone)
    params.SetFrom(t.fromPhone)
    params.SetBody(message)

    _, err := t.client.Api.CreateMessage(params)
    return err
}

func (t *TwilioSMSService) SendOrderReady(order *models.Order) error {
    message := fmt.Sprintf(
        "Your order #%s is ready for pickup! Please come to the restaurant.",
        order.OrderNumber,
    )

    params := &api.CreateMessageParams{}
    params.SetTo(order.CustomerPhone)
    params.SetFrom(t.fromPhone)
    params.SetBody(message)

    _, err := t.client.Api.CreateMessage(params)
    return err
}
```

### 2. SendGrid Email Integration

```go
// SendGrid email service
type SendGridEmailService struct {
    client *sendgrid.Client
    fromEmail string
}

func NewSendGridEmailService(apiKey, fromEmail string) *SendGridEmailService {
    return &SendGridEmailService{
        client:    sendgrid.NewSendClient(apiKey),
        fromEmail: fromEmail,
    }
}

func (s *SendGridEmailService) SendOrderReceipt(order *models.Order) error {
    if order.CustomerEmail == "" {
        return nil
    }

    from := mail.NewEmail("Restaurant", s.fromEmail)
    to := mail.NewEmail(order.CustomerName, order.CustomerEmail)
    subject := fmt.Sprintf("Order Receipt - #%s", order.OrderNumber)

    // Generate HTML content
    htmlContent := s.generateReceiptHTML(order)
    plainContent := s.generateReceiptText(order)

    message := mail.NewSingleEmail(from, subject, to, plainContent, htmlContent)

    response, err := s.client.Send(message)
    if err != nil {
        return err
    }

    if response.StatusCode >= 400 {
        return fmt.Errorf("failed to send email: %d", response.StatusCode)
    }

    return nil
}
```

## Analytics and Business Intelligence

### 1. Google Analytics Integration

```go
// Google Analytics reporting
type GoogleAnalyticsService struct {
    service   *analyticsreporting.Service
    viewID    string
}

func (g *GoogleAnalyticsService) TrackOrderEvent(order *models.Order) error {
    // Track order completion event
    event := &analytics.Event{
        Category: "ecommerce",
        Action:   "purchase",
        Label:    order.OrderNumber,
        Value:    int64(order.Total * 100),
    }

    return g.sendEvent(event)
}

func (g *GoogleAnalyticsService) GetSalesReport(startDate, endDate time.Time) (*SalesReport, error) {
    request := &analyticsreporting.GetReportsRequest{
        ReportRequests: []*analyticsreporting.ReportRequest{
            {
                ViewId: g.viewID,
                DateRanges: []*analyticsreporting.DateRange{
                    {
                        StartDate: startDate.Format("2006-01-02"),
                        EndDate:   endDate.Format("2006-01-02"),
                    },
                },
                Metrics: []*analyticsreporting.Metric{
                    {Expression: "ga:transactionRevenue"},
                    {Expression: "ga:transactions"},
                },
                Dimensions: []*analyticsreporting.Dimension{
                    {Name: "ga:date"},
                },
            },
        },
    }

    response, err := g.service.Reports.BatchGet(request).Do()
    if err != nil {
        return nil, err
    }

    return g.parseReportResponse(response), nil
}
```

### 2. Mixpanel Integration

```go
// Mixpanel analytics service
type MixpanelService struct {
    client *mixpanel.Mixpanel
    token  string
}

func NewMixpanelService(token string) *MixpanelService {
    return &MixpanelService{
        client: mixpanel.New(token, ""),
        token:  token,
    }
}

func (m *MixpanelService) TrackOrderCreated(order *models.Order) error {
    properties := map[string]interface{}{
        "order_id":       order.ID.String(),
        "order_number":   order.OrderNumber,
        "total":          order.Total,
        "item_count":     len(order.Items),
        "order_type":     order.Type,
        "branch_id":      order.BranchID.String(),
        "customer_type":  m.getCustomerType(order),
    }

    return m.client.Track(order.CustomerID.String(), "Order Created", properties)
}

func (m *MixpanelService) TrackMenuItemView(userID, itemID string, itemName string) error {
    properties := map[string]interface{}{
        "item_id":   itemID,
        "item_name": itemName,
    }

    return m.client.Track(userID, "Menu Item Viewed", properties)
}
```

## Inventory Management Integrations

### 1. Restaurant Supply Chain Integration

```go
// Generic supplier API integration
type SupplierIntegration struct {
    client   *http.Client
    baseURL  string
    apiKey   string
    supplierID string
}

func (s *SupplierIntegration) CreatePurchaseOrder(items []InventoryItem) (*PurchaseOrder, error) {
    order := &PurchaseOrder{
        SupplierID: s.supplierID,
        Items:      items,
        OrderDate:  time.Now(),
        Status:     "pending",
    }

    payload, err := json.Marshal(order)
    if err != nil {
        return nil, err
    }

    req, err := http.NewRequest("POST", s.baseURL+"/purchase-orders", bytes.NewBuffer(payload))
    if err != nil {
        return nil, err
    }

    req.Header.Set("Authorization", "Bearer "+s.apiKey)
    req.Header.Set("Content-Type", "application/json")

    resp, err := s.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var createdOrder PurchaseOrder
    err = json.NewDecoder(resp.Body).Decode(&createdOrder)
    return &createdOrder, err
}

func (s *SupplierIntegration) CheckInventoryLevels() ([]InventoryLevel, error) {
    req, err := http.NewRequest("GET", s.baseURL+"/inventory", nil)
    if err != nil {
        return nil, err
    }

    req.Header.Set("Authorization", "Bearer "+s.apiKey)

    resp, err := s.client.Do(req)
    if err != nil {
        return nil, err
    }
    defer resp.Body.Close()

    var levels []InventoryLevel
    err = json.NewDecoder(resp.Body).Decode(&levels)
    return levels, err
}
```

## Integration Management Service

```go
// Central integration management
type IntegrationManager struct {
    integrations map[string]Integration
    config       *IntegrationConfig
    logger       *logrus.Logger
}

type Integration interface {
    Name() string
    IsEnabled() bool
    HealthCheck() error
    Configure(config map[string]interface{}) error
}

func NewIntegrationManager(config *IntegrationConfig, logger *logrus.Logger) *IntegrationManager {
    return &IntegrationManager{
        integrations: make(map[string]Integration),
        config:       config,
        logger:       logger,
    }
}

func (im *IntegrationManager) RegisterIntegration(integration Integration) {
    im.integrations[integration.Name()] = integration
    im.logger.WithField("integration", integration.Name()).Info("Integration registered")
}

func (im *IntegrationManager) EnableIntegration(name string, config map[string]interface{}) error {
    integration, exists := im.integrations[name]
    if !exists {
        return fmt.Errorf("integration %s not found", name)
    }

    if err := integration.Configure(config); err != nil {
        return fmt.Errorf("failed to configure integration %s: %w", name, err)
    }

    im.logger.WithField("integration", name).Info("Integration enabled")
    return nil
}

func (im *IntegrationManager) HealthCheckAll() map[string]error {
    results := make(map[string]error)
    
    for name, integration := range im.integrations {
        if integration.IsEnabled() {
            results[name] = integration.HealthCheck()
        }
    }
    
    return results
}
```

This comprehensive integration guide provides the foundation for connecting the Restaurant Management API with various external services, enabling a complete ecosystem for restaurant operations.
