# AI Generation Queue System

## Overview

The AI Generation Queue System uses Redis pub/sub with priority queues to handle AI generation jobs asynchronously. This provides better scalability, reliability, and performance compared to direct goroutine processing.

## Architecture

```
Frontend Request → Handler → Create Job in DB → Enqueue Job → Redis Queue
                                                      ↓
Worker Pool ← Redis Pub/Sub ← Queue Processing ← Job Processing
     ↓
WebSocket Updates → Frontend
```

## Features

- **Priority-based processing**: Higher priority jobs are processed first
- **Horizontal scaling**: Multiple workers can process jobs concurrently
- **Fault tolerance**: Jobs persist in Redis even if workers restart
- **Real-time notifications**: Pub/sub for immediate job processing
- **Monitoring**: Queue statistics and health checks

## Configuration

### Redis Configuration

Add to your `.env` file:
```env
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

### Queue Settings

The queue is configured with:
- **3 workers** by default (configurable)
- **Priority levels**: 1 (high), 2 (normal), 3 (low)
- **Automatic retries** for failed jobs
- **Dead letter queue** for permanently failed jobs

## Usage

### 1. Creating AI Generation Jobs

When a user uploads an image and requests AI generation:

```go
// In the handler
job, err := h.aiService.CreateJob(ctx, branchID, userID, req)
if err != nil {
    return err
}

// Enqueue for processing
priority := 2 // Normal priority
if req.Type == "food_images" {
    priority = 1 // Higher priority for food images
}

err = h.aiQueue.EnqueueJob(ctx, job.ID, req.Type, priority)
```

### 2. Job Processing Flow

1. **Job Creation**: Job is saved to database with "pending" status
2. **Enqueuing**: Job is added to Redis priority queue
3. **Pub/Sub Notification**: Workers are notified immediately
4. **Processing**: Available worker picks up the job
5. **Status Updates**: WebSocket updates sent to frontend
6. **Completion**: Job marked as "completed" or "failed"

### 3. Monitoring Queue

```go
// Get queue statistics
stats, err := aiQueue.GetQueueStats(ctx)
// Returns: {"pending_jobs": 5, "queue_name": "ai_generation_jobs", "workers": 3}
```

## Benefits Over Direct Processing

### Before (Direct Goroutines)
- ❌ No persistence if server restarts
- ❌ No priority handling
- ❌ Difficult to scale horizontally
- ❌ No monitoring or observability
- ❌ Memory usage grows with job count

### After (Redis Queue)
- ✅ Jobs persist across server restarts
- ✅ Priority-based processing
- ✅ Easy horizontal scaling
- ✅ Built-in monitoring and stats
- ✅ Constant memory usage
- ✅ Better error handling and retries

## Scaling

### Vertical Scaling
Increase worker count:
```go
aiJobQueue = queue.NewAIJobQueue(redisClient, aiService, logger, 10) // 10 workers
```

### Horizontal Scaling
Run multiple server instances - they'll all share the same Redis queue and process jobs collaboratively.

## Error Handling

- **Transient errors**: Jobs are retried automatically
- **Permanent failures**: Jobs moved to dead letter queue
- **Worker failures**: Other workers pick up abandoned jobs
- **Redis failures**: Graceful degradation (jobs created but not processed)

## WebSocket Integration

The queue system integrates with the existing WebSocket system to provide real-time updates:

```javascript
// Frontend receives updates
{
  "type": "ai_generation_update",
  "job_id": "uuid",
  "status": "processing",
  "progress": 45,
  "message": "Analyzing food images..."
}
```

## Future Enhancements

1. **Job Scheduling**: Delay job processing until specific times
2. **Batch Processing**: Process multiple similar jobs together
3. **Rate Limiting**: Limit jobs per user/branch
4. **Analytics**: Track processing times and success rates
5. **Auto-scaling**: Dynamically adjust worker count based on queue size
