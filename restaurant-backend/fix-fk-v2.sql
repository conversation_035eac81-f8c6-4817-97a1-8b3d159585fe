-- Fix foreign key constraint issues by assigning orphaned records to valid branches
-- This script fixes all tables with invalid branch_id references

-- First, check what shop branches we have
SELECT 'Available shop branches:' as status;
SELECT id, shop_id, name, slug FROM shop_branches ORDER BY created_at LIMIT 5;

-- Get the first available branch ID to use as default
DO $$
DECLARE
    default_branch_id UUID;
BEGIN
    -- Get the first available branch ID
    SELECT id INTO default_branch_id FROM shop_branches ORDER BY created_at LIMIT 1;
    
    IF default_branch_id IS NULL THEN
        RAISE EXCEPTION 'No shop branches found in database';
    END IF;
    
    RAISE NOTICE 'Using default branch ID: %', default_branch_id;
    
    -- Fix menu_categories table
    UPDATE menu_categories 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix menu_items table
    UPDATE menu_items 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix table_areas table
    UPDATE table_areas 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix tables table
    UPDATE tables 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix orders table (if exists)
    UPDATE orders 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix reservations table (if exists)
    UPDATE reservations 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix reviews table (if exists)
    UPDATE reviews 
    SET branch_id = default_branch_id, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
    -- Fix users table (set to NULL since it allows NULL)
    UPDATE users 
    SET branch_id = NULL, updated_at = NOW() 
    WHERE branch_id IS NOT NULL 
    AND branch_id NOT IN (SELECT id FROM shop_branches);
    
END $$;

-- Verify the fix
SELECT 'Verification:' as status;

SELECT 'Orphaned menu_categories:' as check_type, COUNT(*) as count
FROM menu_categories mc 
WHERE mc.branch_id IS NOT NULL 
AND mc.branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'Orphaned menu_items:' as check_type, COUNT(*) as count
FROM menu_items mi 
WHERE mi.branch_id IS NOT NULL 
AND mi.branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'Orphaned table_areas:' as check_type, COUNT(*) as count
FROM table_areas ta 
WHERE ta.branch_id IS NOT NULL 
AND ta.branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'Orphaned tables:' as check_type, COUNT(*) as count
FROM tables t 
WHERE t.branch_id IS NOT NULL 
AND t.branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'Orphaned users:' as check_type, COUNT(*) as count
FROM users u 
WHERE u.branch_id IS NOT NULL 
AND u.branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'All foreign key constraint issues fixed!' as result;
