-- Fix foreign key constraint issues
-- This script fixes all tables with invalid branch_id references

-- First, check what we have
SELECT 'Current state:' as status;
SELECT COUNT(*) as total_shop_branches FROM shop_branches;

-- Check and fix users table
SELECT 'Checking users table...' as status;
SELECT COUNT(*) as orphaned_users
FROM users u
WHERE u.branch_id IS NOT NULL
AND u.branch_id NOT IN (SELECT id FROM shop_branches);

UPDATE users
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Check and fix menu_categories table
SELECT 'Checking menu_categories table...' as status;
SELECT COUNT(*) as orphaned_menu_categories
FROM menu_categories mc
WHERE mc.branch_id IS NOT NULL
AND mc.branch_id NOT IN (SELECT id FROM shop_branches);

UPDATE menu_categories
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Check and fix menu_items table
SELECT 'Checking menu_items table...' as status;
SELECT COUNT(*) as orphaned_menu_items
FROM menu_items mi
WHERE mi.branch_id IS NOT NULL
AND mi.branch_id NOT IN (SELECT id FROM shop_branches);

UPDATE menu_items
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Check and fix all other tables that might have branch_id references
-- Fix table_areas
UPDATE table_areas
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix tables
UPDATE tables
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix orders
UPDATE orders
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix reservations
UPDATE reservations
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix reviews
UPDATE reviews
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix inventory_items
UPDATE inventory_items
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix purchase_orders
UPDATE purchase_orders
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix inventory_transactions
UPDATE inventory_transactions
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

-- Fix waste_logs
UPDATE waste_logs
SET branch_id = NULL, updated_at = NOW()
WHERE branch_id IS NOT NULL
AND branch_id NOT IN (SELECT id FROM shop_branches);

SELECT 'All foreign key constraint issues fixed!' as result;
