package handlers

import (
	"net/http"
	"time"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// AnalyticsHandler handles analytics-related HTTP requests
type AnalyticsHandler struct {
	analyticsService *services.AnalyticsService
	logger           *logrus.Logger
}

// NewAnalyticsHandler creates a new analytics handler
func NewAnalyticsHandler(analyticsService *services.AnalyticsService, logger *logrus.Logger) *AnalyticsHandler {
	return &AnalyticsHandler{
		analyticsService: analyticsService,
		logger:           logger,
	}
}

// GetDashboard godoc
// @Summary Get analytics dashboard
// @Description Get comprehensive analytics dashboard data for a branch
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} services.DashboardData
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/dashboard [get]
func (h *AnalyticsHandler) GetDashboard(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	dashboard, err := h.analyticsService.GetDashboardData(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get dashboard data")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get dashboard data"})
		return
	}

	c.JSON(http.StatusOK, dashboard)
}

// GetSalesReport godoc
// @Summary Get sales report analytics
// @Description Get sales report analytics with filtering, sorting, and pagination
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param report_type query string false "Report type (daily, weekly, monthly, yearly)"
// @Param payment_method query string false "Filter by payment method"
// @Param order_type query string false "Filter by order type"
// @Param table_id query string false "Filter by table ID"
// @Param staff_id query string false "Filter by staff ID"
// @Param min_amount query number false "Minimum amount filter"
// @Param max_amount query number false "Maximum amount filter"
// @Param include_refunds query bool false "Include refunds in report"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("date")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.SalesReportAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/analytics/sales-report [get]
func (h *AnalyticsHandler) GetSalesReport(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.SalesReportFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	salesReport, err := h.analyticsService.GetSalesReportAnalytics(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get sales report analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get sales report analytics"})
		return
	}

	c.JSON(http.StatusOK, salesReport)
}

// GetPopularItems godoc
// @Summary Get popular items analytics
// @Description Get popular menu items analytics for a branch with filtering, sorting, and pagination
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category_id query string false "Filter by category ID"
// @Param min_orders query int false "Minimum order count filter"
// @Param period query string false "Time period (today, week, month, quarter, year)"
// @Param is_available query bool false "Filter by availability"
// @Param is_vegetarian query bool false "Filter by vegetarian items"
// @Param is_vegan query bool false "Filter by vegan items"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("order_count")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.PopularItemsAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/analytics/popular-items [get]
func (h *AnalyticsHandler) GetPopularItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.PopularItemsFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	popularItems, err := h.analyticsService.GetPopularItemsAnalytics(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular items analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get popular items analytics"})
		return
	}

	c.JSON(http.StatusOK, popularItems)
}

// GetCustomerAnalytics godoc
// @Summary Get customer analytics
// @Description Get customer analytics with filtering, sorting, and pagination
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param customer_type query string false "Filter by customer type (new, returning, vip)"
// @Param min_order_value query number false "Minimum order value filter"
// @Param max_order_value query number false "Maximum order value filter"
// @Param min_order_count query int false "Minimum order count filter"
// @Param max_order_count query int false "Maximum order count filter"
// @Param segment_id query string false "Filter by segment ID"
// @Param payment_method query string false "Filter by payment method"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("total_spent")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.CustomerAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/analytics/customers [get]
func (h *AnalyticsHandler) GetCustomerAnalytics(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.CustomerAnalyticsFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	customerAnalytics, err := h.analyticsService.GetCustomerAnalytics(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get customer analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get customer analytics"})
		return
	}

	c.JSON(http.StatusOK, customerAnalytics)
}

// GetStaffPerformance godoc
// @Summary Get staff performance analytics
// @Description Get staff performance analytics with filtering, sorting, and pagination
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param staff_id query string false "Filter by staff ID"
// @Param position query string false "Filter by position"
// @Param department query string false "Filter by department"
// @Param min_rating query number false "Minimum rating filter (0-5)"
// @Param max_rating query number false "Maximum rating filter (0-5)"
// @Param metric_type query string false "Metric type (efficiency, orders_served, customer_rating, sales)"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("efficiency_score")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.StaffPerformanceAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/analytics/staff [get]
func (h *AnalyticsHandler) GetStaffPerformance(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.StaffPerformanceFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	staffPerformance, err := h.analyticsService.GetStaffPerformanceAnalytics(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get staff performance analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get staff performance analytics"})
		return
	}

	c.JSON(http.StatusOK, staffPerformance)
}

// GetTableUtilization godoc
// @Summary Get table utilization analytics
// @Description Get table utilization analytics with filtering, sorting, and pagination
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param table_id query string false "Filter by table ID"
// @Param area_id query string false "Filter by area ID"
// @Param min_capacity query int false "Minimum capacity filter"
// @Param max_capacity query int false "Maximum capacity filter"
// @Param time_slot query string false "Time slot (breakfast, lunch, dinner, late_night)"
// @Param day_of_week query int false "Day of week (0=Sunday, 6=Saturday)"
// @Param min_utilization query number false "Minimum utilization percentage (0-100)"
// @Param max_utilization query number false "Maximum utilization percentage (0-100)"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("utilization_rate")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.TableUtilizationAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/analytics/tables [get]
func (h *AnalyticsHandler) GetTableUtilization(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.TableUtilizationFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	tableUtilization, err := h.analyticsService.GetTableUtilizationAnalytics(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get table utilization analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get table utilization analytics"})
		return
	}

	c.JSON(http.StatusOK, tableUtilization)
}

// GetCommunicationAnalytics godoc
// @Summary Get communication analytics
// @Description Get communication analytics with filtering, sorting, and pagination
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param campaign_id query string false "Filter by campaign ID"
// @Param channel_type query string false "Filter by channel type (email, sms, push, notification)"
// @Param status query string false "Filter by status (sent, delivered, opened, clicked, failed)"
// @Param segment_id query string false "Filter by segment ID"
// @Param min_open_rate query number false "Minimum open rate filter (0-100)"
// @Param max_open_rate query number false "Maximum open rate filter (0-100)"
// @Param min_click_rate query number false "Minimum click rate filter (0-100)"
// @Param max_click_rate query number false "Maximum click rate filter (0-100)"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("sent_date")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.CommunicationAnalyticsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/communication-analytics/overview [get]
func (h *AnalyticsHandler) GetCommunicationAnalytics(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.CommunicationAnalyticsFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	communicationAnalytics, err := h.analyticsService.GetCommunicationAnalytics(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get communication analytics")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get communication analytics"})
		return
	}

	c.JSON(http.StatusOK, communicationAnalytics)
}

// ExportReport godoc
// @Summary Export analytics report
// @Description Export analytics report in various formats (PDF, Excel, CSV)
// @Tags analytics
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param report_type query string true "Report type (sales, customers, staff, tables)"
// @Param format query string true "Export format (pdf, excel, csv)"
// @Param start_date query string true "Start date (YYYY-MM-DD)"
// @Param end_date query string true "End date (YYYY-MM-DD)"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/analytics/export [post]
func (h *AnalyticsHandler) ExportReport(c *gin.Context) {
	_, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	reportType := c.Query("report_type")
	format := c.Query("format")
	startDateStr := c.Query("start_date")
	endDateStr := c.Query("end_date")

	// Validate required parameters
	if reportType == "" || format == "" || startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "report_type, format, start_date, and end_date are required"})
		return
	}

	// Validate report type
	validReportTypes := []string{"sales", "customers", "staff", "tables"}
	isValidReportType := false
	for _, validType := range validReportTypes {
		if reportType == validType {
			isValidReportType = true
			break
		}
	}
	if !isValidReportType {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid report_type. Must be one of: sales, customers, staff, tables"})
		return
	}

	// Validate format
	validFormats := []string{"pdf", "excel", "csv"}
	isValidFormat := false
	for _, validFmt := range validFormats {
		if format == validFmt {
			isValidFormat = true
			break
		}
	}
	if !isValidFormat {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid format. Must be one of: pdf, excel, csv"})
		return
	}

	// Parse dates
	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid start_date format. Use YYYY-MM-DD"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid end_date format. Use YYYY-MM-DD"})
		return
	}

	// For now, return a placeholder response
	// In a real implementation, this would generate and return the actual file
	exportInfo := gin.H{
		"message":      "Report export initiated",
		"report_type":  reportType,
		"format":       format,
		"start_date":   startDate.Format("2006-01-02"),
		"end_date":     endDate.Format("2006-01-02"),
		"status":       "processing",
		"download_url": "", // Would contain the actual download URL
	}

	c.JSON(http.StatusOK, exportInfo)
}
