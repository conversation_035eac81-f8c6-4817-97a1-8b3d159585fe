package handlers

import (
	"net/http"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// BranchHandler handles branch-related HTTP requests
type BranchHandler struct {
	shopService *services.ShopService
	logger      *logrus.Logger
}

func NewBranchHandler(shopService *services.ShopService, logger *logrus.Logger) *BranchHandler {
	return &BranchHandler{shopService: shopService, logger: logger}
}

// GetBranches godoc
// @Summary Get all branches for a merchant
// @Description Get all branches for a specific merchant
// @Tags branches
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Param search query string false "Search term"
// @Success 200 {object} types.BranchesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches [get]
func (h *BranchHandler) GetBranches(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.BranchFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	branches, err := h.shopService.GetBranches(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get branches: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get branches"})
		return
	}

	c.JSON(http.StatusOK, branches)
}

// CreateBranch godoc
// @Summary Create a new branch
// @Description Create a new branch for a merchant
// @Tags branches
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branch body types.CreateBranchRequest true "Branch data"
// @Success 201 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches [post]
func (h *BranchHandler) CreateBranch(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	branch, err := h.shopService.CreateBranch(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create branch: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create branch"})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// GetBranch godoc
// @Summary Get a specific branch
// @Description Get a specific branch by ID
// @Tags branches
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId} [get]
func (h *BranchHandler) GetBranch(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	branch, err := h.shopService.GetBranchByID(c.Request.Context(), merchantID, branchID)
	if err != nil {
		h.logger.Error("Failed to get branch: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// UpdateBranch godoc
// @Summary Update a branch
// @Description Update a specific branch by ID
// @Tags branches
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param branch body types.UpdateBranchRequest true "Branch update data"
// @Success 200 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId} [put]
func (h *BranchHandler) UpdateBranch(c *gin.Context) {
	_, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.UpdateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	branch, err := h.shopService.UpdateBranch(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to update branch: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update branch"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteBranch godoc
// @Summary Delete a branch
// @Description Delete a specific branch by ID
// @Tags branches
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId} [delete]
func (h *BranchHandler) DeleteBranch(c *gin.Context) {
	_, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	if err := h.shopService.DeleteBranch(c.Request.Context(), branchID); err != nil {
		h.logger.Error("Failed to delete branch: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete branch"})
		return
	}

	c.Status(http.StatusNoContent)
}
