package handlers

import (
	"net/http"

	"restaurant-backend/internal/api/middleware"
	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// InventoryHandler handles inventory-related HTTP requests
type InventoryHandler struct {
	inventoryService *services.InventoryService
	logger           *logrus.Logger
}

// NewInventoryHandler creates a new inventory handler
func NewInventoryHandler(inventoryService *services.InventoryService, logger *logrus.Logger) *InventoryHandler {
	return &InventoryHandler{
		inventoryService: inventoryService,
		logger:           logger,
	}
}

// GetDashboard godoc
// @Summary Get inventory dashboard
// @Description Get comprehensive inventory dashboard data for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} services.InventoryDashboard
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/dashboard [get]
func (h *InventoryHandler) GetDashboard(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	dashboard, err := h.inventoryService.GetInventoryDashboard(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get inventory dashboard")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get inventory dashboard"})
		return
	}

	c.JSON(http.StatusOK, dashboard)
}

// CreateIngredient godoc
// @Summary Create a new ingredient
// @Description Create a new ingredient in the system
// @Tags inventory
// @Accept json
// @Produce json
// @Param ingredient body services.CreateIngredientRequest true "Ingredient data"
// @Success 201 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/ingredients [post]
func (h *InventoryHandler) CreateIngredient(c *gin.Context) {
	var req services.CreateIngredientRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	ingredient, err := h.inventoryService.CreateIngredient(c.Request.Context(), req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create ingredient")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create ingredient"})
		return
	}

	c.JSON(http.StatusCreated, ingredient)
}

// UpdateStock godoc
// @Summary Update stock levels
// @Description Update stock levels for an ingredient at a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param stock body services.UpdateStockRequest true "Stock update data"
// @Success 200 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/stock [put]
func (h *InventoryHandler) UpdateStock(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := middleware.GetUserIDAsUUID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.UpdateStockRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	inventoryItem, err := h.inventoryService.UpdateStock(c.Request.Context(), branchID, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update stock")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, inventoryItem)
}

// CreatePurchaseOrder godoc
// @Summary Create a purchase order
// @Description Create a new purchase order for ingredients
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param order body services.CreatePurchaseOrderRequest true "Purchase order data"
// @Success 201 {object} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/purchase-orders [post]
func (h *InventoryHandler) CreatePurchaseOrder(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := middleware.GetUserIDAsUUID(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	var req services.CreatePurchaseOrderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	purchaseOrder, err := h.inventoryService.CreatePurchaseOrder(c.Request.Context(), branchID, userID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create purchase order")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create purchase order"})
		return
	}

	c.JSON(http.StatusCreated, purchaseOrder)
}

// GetLowStockItems godoc
// @Summary Get low stock items
// @Description Get items that are below their reorder point with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category query string false "Filter by ingredient category"
// @Param supplier_id query string false "Filter by supplier ID"
// @Param severity query string false "Filter by severity (critical, high, medium, low)"
// @Param min_stock query number false "Minimum stock filter"
// @Param max_stock query number false "Maximum stock filter"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("severity")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.InventoryItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/inventory/low-stock [get]
func (h *InventoryHandler) GetLowStockItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.InventoryItemFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Set stock level filter to low for this endpoint
	filters.StockLevel = "low"

	lowStockItems, err := h.inventoryService.GetInventoryItems(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get low stock items")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get low stock items"})
		return
	}

	c.JSON(http.StatusOK, lowStockItems)
}

// GetExpiringItems godoc
// @Summary Get expiring items
// @Description Get items that are expiring with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category query string false "Filter by ingredient category"
// @Param supplier_id query string false "Filter by supplier ID"
// @Param expiry_status query string false "Filter by expiry status (expired, expiring_soon, fresh)"
// @Param days query int false "Number of days to check for expiry (default: 7)"
// @Param min_value query number false "Minimum value filter"
// @Param max_value query number false "Maximum value filter"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("expiry_date")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.InventoryItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/inventory/expiring [get]
func (h *InventoryHandler) GetExpiringItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.InventoryItemFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Set expiry status filter to expiring_soon if not specified
	if filters.ExpiryStatus == "" {
		filters.ExpiryStatus = "expiring_soon"
	}

	expiringItems, err := h.inventoryService.GetInventoryItems(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get expiring items")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get expiring items"})
		return
	}

	c.JSON(http.StatusOK, expiringItems)
}

// GetInventoryItems godoc
// @Summary Get inventory items
// @Description Get all inventory items for a branch with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param category query string false "Filter by ingredient category"
// @Param status query string false "Filter by status"
// @Param is_active query bool false "Filter by active status"
// @Param supplier_id query string false "Filter by supplier ID"
// @Param stock_level query string false "Filter by stock level (low, out_of_stock, normal)"
// @Param expiry_status query string false "Filter by expiry status (expired, expiring_soon, fresh)"
// @Param min_stock query number false "Minimum stock filter"
// @Param max_stock query number false "Maximum stock filter"
// @Param min_value query number false "Minimum value filter"
// @Param max_value query number false "Maximum value filter"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("name")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Success 200 {object} types.InventoryItemsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/inventory/items [get]
func (h *InventoryHandler) GetInventoryItems(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.InventoryItemFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	items, err := h.inventoryService.GetInventoryItems(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get inventory items")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get inventory items"})
		return
	}

	c.JSON(http.StatusOK, items)
}

// GetIngredients godoc
// @Summary Get ingredients
// @Description Get all available ingredients with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param category query string false "Filter by category"
// @Param supplier_id query string false "Filter by supplier ID"
// @Param unit query string false "Filter by unit"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("name")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.IngredientsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /ingredients [get]
func (h *InventoryHandler) GetIngredients(c *gin.Context) {
	var filters types.IngredientFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	ingredients, err := h.inventoryService.GetIngredients(c.Request.Context(), filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get ingredients")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get ingredients"})
		return
	}

	c.JSON(http.StatusOK, ingredients)
}

// GetSuppliers godoc
// @Summary Get suppliers
// @Description Get all suppliers with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param country query string false "Filter by country"
// @Param city query string false "Filter by city"
// @Param rating query int false "Filter by rating (1-5)"
// @Param is_active query bool false "Filter by active status"
// @Param search query string false "Search term"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("name")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.SuppliersResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /suppliers [get]
func (h *InventoryHandler) GetSuppliers(c *gin.Context) {
	var filters types.SupplierFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	suppliers, err := h.inventoryService.GetSuppliers(c.Request.Context(), filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get suppliers")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get suppliers"})
		return
	}

	c.JSON(http.StatusOK, suppliers)
}

// GetPurchaseOrders godoc
// @Summary Get purchase orders
// @Description Get purchase orders for a branch
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param status query string false "Filter by status (pending, approved, ordered, received, cancelled)"
// @Success 200 {array} object
// @Failure 400 {object} object
// @Failure 401 {object} object
// @Failure 500 {object} object
// @Router /api/v1/merchants/{merchantId}/branches/{branchId}/inventory/purchase-orders [get]
func (h *InventoryHandler) GetPurchaseOrders(c *gin.Context) {
	_, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	// For now, return placeholder data
	// In a real implementation, this would fetch from the repository
	purchaseOrders := []gin.H{}

	c.JSON(http.StatusOK, purchaseOrders)
}

// GetStockMovements godoc
// @Summary Get stock movements
// @Description Get stock movement history for a branch with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param ingredient_id query string false "Filter by ingredient ID"
// @Param movement_type query string false "Filter by movement type (in, out, adjustment, waste)"
// @Param user_id query string false "Filter by user ID"
// @Param min_quantity query number false "Minimum quantity filter"
// @Param max_quantity query number false "Maximum quantity filter"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("created_at")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.StockMovementsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/inventory/movements [get]
func (h *InventoryHandler) GetStockMovements(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.StockMovementFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	movements, err := h.inventoryService.GetStockMovements(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get stock movements")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get stock movements"})
		return
	}

	c.JSON(http.StatusOK, movements)
}

// GetWasteRecords godoc
// @Summary Get waste records
// @Description Get food waste records for a branch with filtering, sorting, and pagination
// @Tags inventory
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param branchId path string true "Branch ID"
// @Param ingredient_id query string false "Filter by ingredient ID"
// @Param reason query string false "Filter by waste reason"
// @Param user_id query string false "Filter by user ID"
// @Param min_quantity query number false "Minimum quantity filter"
// @Param max_quantity query number false "Maximum quantity filter"
// @Param min_value query number false "Minimum value filter"
// @Param max_value query number false "Maximum value filter"
// @Param date_from query string false "Filter from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter to date (YYYY-MM-DD)"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort field" default("waste_date")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.WasteRecordsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/branches/{branchId}/inventory/waste [get]
func (h *InventoryHandler) GetWasteRecords(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		h.logger.WithError(err).Error("Invalid branch ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.WasteRecordFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	wasteRecords, err := h.inventoryService.GetWasteRecords(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get waste records")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get waste records"})
		return
	}

	c.JSON(http.StatusOK, wasteRecords)
}
