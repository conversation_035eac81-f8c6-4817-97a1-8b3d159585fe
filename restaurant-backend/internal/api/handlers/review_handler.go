package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"
)

// ReviewHandler handles review-related HTTP requests
type ReviewHandler struct {
	reviewService services.ReviewService
	shopService   *services.ShopService
	logger        *logrus.Logger
}

// NewReviewHandler creates a new review handler
func NewReviewHandler(reviewService services.ReviewService, shopService *services.ShopService, logger *logrus.Logger) *ReviewHandler {
	return &ReviewHandler{
		reviewService: reviewService,
		shopService:   shopService,
		logger:        logger,
	}
}

// GetReviews godoc
// @Summary Get reviews for a branch
// @Description Get paginated list of reviews for a specific branch with filtering and sorting
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param status query string false "Filter by status (pending, approved, rejected, flagged)"
// @Param rating query int false "Filter by rating (1-5)"
// @Param source query string false "Filter by source (google, yelp, facebook, etc.)"
// @Param sentiment query string false "Filter by sentiment (positive, neutral, negative)"
// @Param search query string false "Search in customer name, comment, or title"
// @Param sort_by query string false "Sort by field (rating, date, customer)" default("date")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.ReviewsListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews [get]
func (h *ReviewHandler) GetReviews(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var filters types.ReviewFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	reviews, err := h.reviewService.GetReviews(c.Request.Context(), branchID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reviews")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// GetReview godoc
// @Summary Get a specific review
// @Description Get details of a specific review by ID
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} models.Review
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId} [get]
func (h *ReviewHandler) GetReview(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	review, err := h.reviewService.GetReview(c.Request.Context(), reviewID)
	if err != nil {
		if err.Error() == "review not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Review not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to get review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get review"})
		return
	}

	c.JSON(http.StatusOK, review)
}

// GetReviewStats godoc
// @Summary Get review statistics
// @Description Get review statistics for a branch including average rating, total reviews, etc.
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param period query string false "Time period (7d, 30d, 90d, 1y)" default("30d")
// @Success 200 {object} types.ReviewStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/stats [get]
func (h *ReviewHandler) GetReviewStats(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	period := c.DefaultQuery("period", "30d")

	stats, err := h.reviewService.GetReviewStats(c.Request.Context(), branchID, period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get review stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get review stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetRecentReviews godoc
// @Summary Get recent reviews
// @Description Get the most recent reviews for a branch
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param limit query int false "Number of reviews to return" default(10)
// @Success 200 {object} []models.Review
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/recent [get]
func (h *ReviewHandler) GetRecentReviews(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid limit parameter"})
		return
	}

	reviews, err := h.reviewService.GetRecentReviews(c.Request.Context(), branchID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get recent reviews")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// GetPendingReviews godoc
// @Summary Get pending reviews
// @Description Get all pending reviews for a branch
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} []models.Review
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/pending [get]
func (h *ReviewHandler) GetPendingReviews(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	reviews, err := h.reviewService.GetPendingReviews(c.Request.Context(), branchID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get pending reviews")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get pending reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// CreateReview godoc
// @Summary Create a new review
// @Description Create a new review for a branch
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param review body types.CreateReviewRequest true "Review data"
// @Success 201 {object} models.Review
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews [post]
func (h *ReviewHandler) CreateReview(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.CreateReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	review, err := h.reviewService.CreateReview(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create review"})
		return
	}

	c.JSON(http.StatusCreated, review)
}

// RespondToReview godoc
// @Summary Respond to a review
// @Description Add a response to a customer review
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Param response body types.RespondToReviewRequest true "Response data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/respond [post]
func (h *ReviewHandler) RespondToReview(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	var req types.RespondToReviewRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	err = h.reviewService.RespondToReview(c.Request.Context(), reviewID, req)
	if err != nil {
		if err.Error() == "review not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Review not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to respond to review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to respond to review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Response added successfully"})
}

// UpdateReviewResponse godoc
// @Summary Update review response
// @Description Update an existing response to a review
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Param response body types.UpdateReviewResponseRequest true "Updated response data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/respond [put]
func (h *ReviewHandler) UpdateReviewResponse(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	var req types.UpdateReviewResponseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	err = h.reviewService.UpdateReviewResponse(c.Request.Context(), reviewID, req)
	if err != nil {
		if err.Error() == "review not found" || err.Error() == "review has no response to update" {
			c.JSON(http.StatusNotFound, gin.H{"error": err.Error()})
			return
		}
		h.logger.WithError(err).Error("Failed to update review response")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update review response"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Response updated successfully"})
}

// DeleteReviewResponse godoc
// @Summary Delete review response
// @Description Delete a response to a review
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/respond [delete]
func (h *ReviewHandler) DeleteReviewResponse(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.DeleteReviewResponse(c.Request.Context(), reviewID)
	if err != nil {
		if err.Error() == "review not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Review not found"})
			return
		}
		h.logger.WithError(err).Error("Failed to delete review response")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete review response"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Response deleted successfully"})
}

// UpdateReviewStatus godoc
// @Summary Update review status
// @Description Update the status of a review (pending, approved, rejected, flagged)
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Param status body types.UpdateReviewStatusRequest true "Status update data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/status [patch]
func (h *ReviewHandler) UpdateReviewStatus(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	var req types.UpdateReviewStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	err = h.reviewService.UpdateReviewStatus(c.Request.Context(), reviewID, req.Status)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update review status")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update review status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review status updated successfully"})
}

// FlagReview godoc
// @Summary Flag a review
// @Description Flag a review as inappropriate
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/flag [post]
func (h *ReviewHandler) FlagReview(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.FlagReview(c.Request.Context(), reviewID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to flag review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to flag review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review flagged successfully"})
}

// HideReview godoc
// @Summary Hide a review
// @Description Hide a review from public view
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/hide [post]
func (h *ReviewHandler) HideReview(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.HideReview(c.Request.Context(), reviewID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to hide review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to hide review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review hidden successfully"})
}

// ShowReview godoc
// @Summary Show a review
// @Description Make a review visible to public
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/{reviewId}/show [post]
func (h *ReviewHandler) ShowReview(c *gin.Context) {
	reviewID, err := uuid.Parse(c.Param("reviewId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.ShowReview(c.Request.Context(), reviewID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to show review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to show review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review shown successfully"})
}

// GetReviewInsights godoc
// @Summary Get review insights
// @Description Get insights and analytics for reviews including common keywords, improvement areas, and strengths
// @Tags reviews
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param period query string false "Time period (7d, 30d, 90d, 1y)" default("30d")
// @Success 200 {object} types.ReviewInsights
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/{shopId}/branches/{branchId}/reviews/insights [get]
func (h *ReviewHandler) GetReviewInsights(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	period := c.DefaultQuery("period", "30d")

	insights, err := h.reviewService.GetReviewInsights(c.Request.Context(), branchID, period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get review insights")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get review insights"})
		return
	}

	c.JSON(http.StatusOK, insights)
}

// Slug-based handlers

// GetReviewsBySlug godoc
// @Summary Get reviews for a branch by slug
// @Description Get paginated list of reviews for a specific branch using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param status query string false "Filter by status (pending, approved, rejected, flagged)"
// @Param rating query int false "Filter by rating (1-5)"
// @Param source query string false "Filter by source (google, yelp, facebook, etc.)"
// @Param sentiment query string false "Filter by sentiment (positive, neutral, negative)"
// @Param search query string false "Search in customer name, comment, or title"
// @Param sort_by query string false "Sort by field (rating, date, customer)" default("date")
// @Param sort_order query string false "Sort order (asc, desc)" default("desc")
// @Success 200 {object} types.ReviewsListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews [get]
func (h *ReviewHandler) GetReviewsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get branch by slug to get the branch ID
	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Parse filters
	filters := types.ReviewFilters{
		Page:      1,
		Limit:     20,
		SortBy:    "date",
		SortOrder: "desc",
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			filters.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			filters.Limit = l
		}
	}

	if status := c.Query("status"); status != "" {
		filters.Status = status
	}

	if rating := c.Query("rating"); rating != "" {
		if r, err := strconv.Atoi(rating); err == nil && r >= 1 && r <= 5 {
			filters.Rating = r
		}
	}

	if source := c.Query("source"); source != "" {
		filters.Source = source
	}

	if sentiment := c.Query("sentiment"); sentiment != "" {
		filters.Sentiment = sentiment
	}

	if search := c.Query("search"); search != "" {
		filters.Search = search
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		filters.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		filters.SortOrder = sortOrder
	}

	response, err := h.reviewService.GetReviews(c.Request.Context(), branch.ID, filters)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get reviews")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get reviews"})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetReviewStatsBySlug godoc
// @Summary Get review statistics by slug
// @Description Get review statistics for a specific branch using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param period query string false "Time period (7d, 30d, 90d, 1y)" default("30d")
// @Success 200 {object} types.ReviewStats
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/stats [get]
func (h *ReviewHandler) GetReviewStatsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get branch by slug to get the branch ID
	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	period := c.DefaultQuery("period", "30d")

	stats, err := h.reviewService.GetReviewStats(c.Request.Context(), branch.ID, period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get review stats")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get review stats"})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetRecentReviewsBySlug godoc
// @Summary Get recent reviews by slug
// @Description Get recent reviews for a specific branch using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param limit query int false "Number of reviews to return" default(10)
// @Success 200 {array} models.Review
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/recent [get]
func (h *ReviewHandler) GetRecentReviewsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get branch by slug to get the branch ID
	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	limit := 10
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	reviews, err := h.reviewService.GetRecentReviews(c.Request.Context(), branch.ID, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get recent reviews")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get recent reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// GetPendingReviewsBySlug godoc
// @Summary Get pending reviews by slug
// @Description Get pending reviews for a specific branch using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Success 200 {array} models.Review
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/pending [get]
func (h *ReviewHandler) GetPendingReviewsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get branch by slug to get the branch ID
	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	reviews, err := h.reviewService.GetPendingReviews(c.Request.Context(), branch.ID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get pending reviews")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get pending reviews"})
		return
	}

	c.JSON(http.StatusOK, reviews)
}

// GetReviewInsightsBySlug godoc
// @Summary Get review insights by slug
// @Description Get insights and analytics for reviews using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param period query string false "Time period (7d, 30d, 90d, 1y)" default("30d")
// @Success 200 {object} types.ReviewInsights
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/insights [get]
func (h *ReviewHandler) GetReviewInsightsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	// Get branch by slug to get the branch ID
	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	period := c.DefaultQuery("period", "30d")

	insights, err := h.reviewService.GetReviewInsights(c.Request.Context(), branch.ID, period)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get review insights")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get review insights"})
		return
	}

	c.JSON(http.StatusOK, insights)
}

// RespondToReviewBySlug godoc
// @Summary Respond to a review by slug
// @Description Add or update a response to a review using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param reviewId path string true "Review ID"
// @Param request body types.ReviewResponseRequest true "Response data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/{reviewId}/respond [post]
func (h *ReviewHandler) RespondToReviewBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	reviewID := c.Param("reviewId")

	// Validate that the branch exists (for security)
	_, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	var request struct {
		Response    string `json:"response" binding:"required"`
		RespondedBy string `json:"responded_by"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Convert string reviewID to UUID
	reviewUUID, err := uuid.Parse(reviewID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	// Set default responded_by if not provided
	respondedBy := request.RespondedBy
	if respondedBy == "" {
		respondedBy = "Restaurant Manager"
	}

	req := types.RespondToReviewRequest{
		Message:     request.Response,
		RespondedBy: respondedBy,
	}

	err = h.reviewService.RespondToReview(c.Request.Context(), reviewUUID, req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to respond to review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to respond to review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Response added successfully"})
}

// UpdateReviewStatusBySlug godoc
// @Summary Update review status by slug
// @Description Update the status of a review (approve, flag, reject) using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param reviewId path string true "Review ID"
// @Param request body types.ReviewStatusRequest true "Status data"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/{reviewId}/status [patch]
func (h *ReviewHandler) UpdateReviewStatusBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	reviewID := c.Param("reviewId")

	// Validate that the branch exists (for security)
	_, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	var request struct {
		Status string `json:"status" binding:"required,oneof=pending approved rejected flagged"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body. Status must be one of: pending, approved, rejected, flagged"})
		return
	}

	// Convert string reviewID to UUID
	reviewUUID, err := uuid.Parse(reviewID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.UpdateReviewStatus(c.Request.Context(), reviewUUID, request.Status)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update review status")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update review status"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review status updated successfully"})
}

// FlagReviewBySlug godoc
// @Summary Flag a review by slug
// @Description Flag a review as inappropriate using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/{reviewId}/flag [post]
func (h *ReviewHandler) FlagReviewBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	reviewID := c.Param("reviewId")

	// Validate that the branch exists (for security)
	_, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Convert string reviewID to UUID
	reviewUUID, err := uuid.Parse(reviewID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.UpdateReviewStatus(c.Request.Context(), reviewUUID, "flagged")
	if err != nil {
		h.logger.WithError(err).Error("Failed to flag review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to flag review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review flagged successfully"})
}

// ApproveReviewBySlug godoc
// @Summary Approve a review by slug
// @Description Approve a review using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/{reviewId}/approve [post]
func (h *ReviewHandler) ApproveReviewBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	reviewID := c.Param("reviewId")

	// Validate that the branch exists (for security)
	_, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Convert string reviewID to UUID
	reviewUUID, err := uuid.Parse(reviewID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.UpdateReviewStatus(c.Request.Context(), reviewUUID, "approved")
	if err != nil {
		h.logger.WithError(err).Error("Failed to approve review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to approve review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review approved successfully"})
}

// RejectReviewBySlug godoc
// @Summary Reject a review by slug
// @Description Reject a review using shop and branch slugs
// @Tags reviews
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Param reviewId path string true "Review ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /api/v1/shops/slug/{slug}/branches/slug/{branchSlug}/reviews/{reviewId}/reject [post]
func (h *ReviewHandler) RejectReviewBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	reviewID := c.Param("reviewId")

	// Validate that the branch exists (for security)
	_, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	// Convert string reviewID to UUID
	reviewUUID, err := uuid.Parse(reviewID)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid review ID"})
		return
	}

	err = h.reviewService.UpdateReviewStatus(c.Request.Context(), reviewUUID, "rejected")
	if err != nil {
		h.logger.WithError(err).Error("Failed to reject review")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to reject review"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Review rejected successfully"})
}

// Placeholder implementations for other slug-based handlers
func (h *ReviewHandler) GetReviewBySlug(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *ReviewHandler) CreateReviewBySlug(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *ReviewHandler) UpdateReviewResponseBySlug(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *ReviewHandler) DeleteReviewResponseBySlug(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *ReviewHandler) HideReviewBySlug(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}

func (h *ReviewHandler) ShowReviewBySlug(c *gin.Context) {
	c.JSON(http.StatusNotImplemented, gin.H{"error": "Not implemented yet"})
}
