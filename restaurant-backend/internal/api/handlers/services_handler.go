package handlers

import (
	"net/http"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ServiceHandler handles service-related HTTP requests
type ServiceHandler struct {
	serviceService *services.ServiceService
	logger         *logrus.Logger
}

// NewServiceHandler creates a new service handler
func NewServiceHandler(serviceService *services.ServiceService, logger *logrus.Logger) *ServiceHandler {
	return &ServiceHandler{
		serviceService: serviceService,
		logger:         logger,
	}
}

// GetServices godoc
// @Summary Get services
// @Description Get services for a merchant
// @Tags services
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param category query string false "Category filter"
// @Param requires_staff query bool false "Requires staff filter"
// @Param is_active query bool false "Active filter"
// @Param min_price query number false "Minimum price filter"
// @Param max_price query number false "Maximum price filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ServicesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services [get]
func (h *ServiceHandler) GetServices(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.ServiceFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	services, err := h.serviceService.GetServices(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get services: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get services"})
		return
	}

	c.JSON(http.StatusOK, services)
}

// GetService godoc
// @Summary Get service by ID
// @Description Get a specific service by ID
// @Tags services
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param serviceId path string true "Service ID"
// @Success 200 {object} types.ServiceResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services/{serviceId} [get]
func (h *ServiceHandler) GetService(c *gin.Context) {
	serviceID, err := uuid.Parse(c.Param("serviceId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
		return
	}

	service, err := h.serviceService.GetServiceByID(c.Request.Context(), serviceID)
	if err != nil {
		h.logger.Error("Failed to get service: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Service not found"})
		return
	}

	c.JSON(http.StatusOK, service)
}

// CreateService godoc
// @Summary Create a new service
// @Description Create a new service for a merchant
// @Tags services
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param service body types.CreateServiceRequest true "Service data"
// @Success 201 {object} types.ServiceResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services [post]
func (h *ServiceHandler) CreateService(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	service, err := h.serviceService.CreateService(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create service: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create service"})
		return
	}

	c.JSON(http.StatusCreated, service)
}

// UpdateService godoc
// @Summary Update a service
// @Description Update an existing service
// @Tags services
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param serviceId path string true "Service ID"
// @Param service body types.UpdateServiceRequest true "Service update data"
// @Success 200 {object} types.ServiceResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services/{serviceId} [put]
func (h *ServiceHandler) UpdateService(c *gin.Context) {
	serviceID, err := uuid.Parse(c.Param("serviceId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
		return
	}

	var req types.UpdateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	service, err := h.serviceService.UpdateService(c.Request.Context(), serviceID, req)
	if err != nil {
		h.logger.Error("Failed to update service: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update service"})
		return
	}

	c.JSON(http.StatusOK, service)
}

// DeleteService godoc
// @Summary Delete a service
// @Description Delete an existing service
// @Tags services
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param serviceId path string true "Service ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services/{serviceId} [delete]
func (h *ServiceHandler) DeleteService(c *gin.Context) {
	serviceID, err := uuid.Parse(c.Param("serviceId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid service ID"})
		return
	}

	if err := h.serviceService.DeleteService(c.Request.Context(), serviceID); err != nil {
		h.logger.Error("Failed to delete service: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete service"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetAppointments godoc
// @Summary Get appointments
// @Description Get appointments for a merchant
// @Tags appointments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param service_id query string false "Service ID filter"
// @Param staff_id query string false "Staff ID filter"
// @Param status query string false "Status filter"
// @Param date query string false "Date filter (YYYY-MM-DD)"
// @Param start_date query string false "Start date filter (YYYY-MM-DD)"
// @Param end_date query string false "End date filter (YYYY-MM-DD)"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.AppointmentsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/appointments [get]
func (h *ServiceHandler) GetAppointments(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.AppointmentFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	appointments, err := h.serviceService.GetAppointments(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get appointments: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get appointments"})
		return
	}

	c.JSON(http.StatusOK, appointments)
}

// GetAppointment godoc
// @Summary Get appointment by ID
// @Description Get a specific appointment by ID
// @Tags appointments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param appointmentId path string true "Appointment ID"
// @Success 200 {object} types.AppointmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/appointments/{appointmentId} [get]
func (h *ServiceHandler) GetAppointment(c *gin.Context) {
	appointmentID, err := uuid.Parse(c.Param("appointmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid appointment ID"})
		return
	}

	appointment, err := h.serviceService.GetAppointmentByID(c.Request.Context(), appointmentID)
	if err != nil {
		h.logger.Error("Failed to get appointment: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Appointment not found"})
		return
	}

	c.JSON(http.StatusOK, appointment)
}

// CreateAppointment godoc
// @Summary Create a new appointment
// @Description Create a new appointment
// @Tags appointments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param appointment body types.CreateAppointmentRequest true "Appointment data"
// @Success 201 {object} types.AppointmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/appointments [post]
func (h *ServiceHandler) CreateAppointment(c *gin.Context) {
	var req types.CreateAppointmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	appointment, err := h.serviceService.CreateAppointment(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to create appointment: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create appointment"})
		return
	}

	c.JSON(http.StatusCreated, appointment)
}

// UpdateAppointment godoc
// @Summary Update an appointment
// @Description Update an existing appointment
// @Tags appointments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param appointmentId path string true "Appointment ID"
// @Param appointment body types.UpdateAppointmentRequest true "Appointment update data"
// @Success 200 {object} types.AppointmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/appointments/{appointmentId} [put]
func (h *ServiceHandler) UpdateAppointment(c *gin.Context) {
	appointmentID, err := uuid.Parse(c.Param("appointmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid appointment ID"})
		return
	}

	var req types.UpdateAppointmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	appointment, err := h.serviceService.UpdateAppointment(c.Request.Context(), appointmentID, req)
	if err != nil {
		h.logger.Error("Failed to update appointment: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update appointment"})
		return
	}

	c.JSON(http.StatusOK, appointment)
}

// CancelAppointment godoc
// @Summary Cancel an appointment
// @Description Cancel an existing appointment
// @Tags appointments
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param appointmentId path string true "Appointment ID"
// @Param request body types.CancelAppointmentRequest true "Cancellation data"
// @Success 200 {object} types.AppointmentResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/appointments/{appointmentId}/cancel [post]
func (h *ServiceHandler) CancelAppointment(c *gin.Context) {
	appointmentID, err := uuid.Parse(c.Param("appointmentId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid appointment ID"})
		return
	}

	var req types.CancelAppointmentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	appointment, err := h.serviceService.CancelAppointment(c.Request.Context(), appointmentID, req.Reason)
	if err != nil {
		h.logger.Error("Failed to cancel appointment: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel appointment"})
		return
	}

	c.JSON(http.StatusOK, appointment)
}

// GetStaff godoc
// @Summary Get staff
// @Description Get staff for a merchant
// @Tags staff
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param position query string false "Position filter"
// @Param department query string false "Department filter"
// @Param status query string false "Status filter"
// @Param is_active query bool false "Active filter"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.StaffListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff [get]
func (h *ServiceHandler) GetStaff(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.StaffFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	staff, err := h.serviceService.GetStaff(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get staff: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get staff"})
		return
	}

	c.JSON(http.StatusOK, staff)
}

// GetStaffMember godoc
// @Summary Get staff member by ID
// @Description Get a specific staff member by ID
// @Tags staff
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param staffId path string true "Staff ID"
// @Success 200 {object} types.StaffResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{staffId} [get]
func (h *ServiceHandler) GetStaffMember(c *gin.Context) {
	staffID, err := uuid.Parse(c.Param("staffId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid staff ID"})
		return
	}

	staff, err := h.serviceService.GetStaffByID(c.Request.Context(), staffID)
	if err != nil {
		h.logger.Error("Failed to get staff member: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Staff member not found"})
		return
	}

	c.JSON(http.StatusOK, staff)
}

// CreateStaff godoc
// @Summary Create a new staff member
// @Description Create a new staff member for a merchant
// @Tags staff
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param staff body types.CreateStaffRequest true "Staff data"
// @Success 201 {object} types.StaffResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff [post]
func (h *ServiceHandler) CreateStaff(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateStaffRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	staff, err := h.serviceService.CreateStaff(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create staff: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create staff"})
		return
	}

	c.JSON(http.StatusCreated, staff)
}

// UpdateStaff godoc
// @Summary Update a staff member
// @Description Update an existing staff member
// @Tags staff
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param staffId path string true "Staff ID"
// @Param staff body types.UpdateStaffRequest true "Staff update data"
// @Success 200 {object} types.StaffResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{staffId} [put]
func (h *ServiceHandler) UpdateStaff(c *gin.Context) {
	staffID, err := uuid.Parse(c.Param("staffId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid staff ID"})
		return
	}

	var req types.UpdateStaffRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	staff, err := h.serviceService.UpdateStaff(c.Request.Context(), staffID, req)
	if err != nil {
		h.logger.Error("Failed to update staff: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update staff"})
		return
	}

	c.JSON(http.StatusOK, staff)
}

// DeleteStaff godoc
// @Summary Delete a staff member
// @Description Delete an existing staff member
// @Tags staff
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param staffId path string true "Staff ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{staffId} [delete]
func (h *ServiceHandler) DeleteStaff(c *gin.Context) {
	staffID, err := uuid.Parse(c.Param("staffId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid staff ID"})
		return
	}

	if err := h.serviceService.DeleteStaff(c.Request.Context(), staffID); err != nil {
		h.logger.Error("Failed to delete staff: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete staff"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetServiceAvailability godoc
// @Summary Get service availability
// @Description Get availability for a service on a specific date
// @Tags availability
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param serviceId path string true "Service ID"
// @Param service_id query string true "Service ID"
// @Param staff_id query string false "Staff ID"
// @Param date query string true "Date (YYYY-MM-DD)"
// @Success 200 {object} types.AvailabilityResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services/{serviceId}/availability [get]
func (h *ServiceHandler) GetServiceAvailability(c *gin.Context) {
	var req types.AvailabilityRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	availability, err := h.serviceService.GetAvailability(c.Request.Context(), req)
	if err != nil {
		h.logger.Error("Failed to get availability: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get availability"})
		return
	}

	c.JSON(http.StatusOK, availability)
}

// GetServiceTimeSlots godoc
// @Summary Get service time slots
// @Description Get available time slots for a service with filtering, sorting, and pagination
// @Tags availability
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param serviceId path string true "Service ID"
// @Param service_id query string true "Service ID"
// @Param staff_id query string false "Staff ID"
// @Param date query string true "Date (YYYY-MM-DD)"
// @Param time_from query string false "Filter from time (HH:MM)"
// @Param time_to query string false "Filter to time (HH:MM)"
// @Param duration query int false "Filter by duration (minutes)"
// @Param available_only query bool false "Show only available slots" default(true)
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(50)
// @Param sort_by query string false "Sort field" default("time")
// @Param sort_order query string false "Sort order (asc, desc)" default("asc")
// @Success 200 {object} types.TimeSlotsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/services/{serviceId}/time-slots [get]
func (h *ServiceHandler) GetServiceTimeSlots(c *gin.Context) {
	var filters types.TimeSlotFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	timeSlots, err := h.serviceService.GetServiceTimeSlots(c.Request.Context(), filters)
	if err != nil {
		h.logger.Error("Failed to get time slots: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get time slots"})
		return
	}

	c.JSON(http.StatusOK, timeSlots)
}
