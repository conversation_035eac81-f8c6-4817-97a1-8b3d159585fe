package handlers

import (
	"net/http"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// ShopHandler handles shop-related HTTP requests
type ShopHandler struct {
	shopService *services.ShopService
	logger      *logrus.Logger
}

// NewShopHandler creates a new shop handler
func NewShopHandler(shopService *services.ShopService, logger *logrus.Logger) *ShopHandler {
	return &ShopHandler{
		shopService: shopService,
		logger:      logger,
	}
}

// GetShops godoc
// @Summary Get shops
// @Description Get all shops with optional filters
// @Tags shops
// @Accept json
// @Produce json
// @Param shop_type query string false "Shop type filter"
// @Param cuisine_type query string false "Cuisine type filter"
// @Param price_range query string false "Price range filter"
// @Param city query string false "City filter"
// @Param state query string false "State filter"
// @Param status query string false "Status filter"
// @Param is_active query bool false "Active filter"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.ShopsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops [get]
func (h *ShopHandler) GetShops(c *gin.Context) {
	var filters types.ShopFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	// Set default values if not provided
	if filters.Page <= 0 {
		filters.Page = 1
	}
	if filters.Limit <= 0 {
		filters.Limit = 20
	}

	shops, err := h.shopService.GetShopsWithBranches(c.Request.Context(), filters)
	if err != nil {
		h.logger.Error("Failed to get shops: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get shops"})
		return
	}

	c.JSON(http.StatusOK, shops)
}

// GetShop godoc
// @Summary Get shop by ID
// @Description Get a specific shop by ID
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Success 200 {object} types.ShopResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{id} [get]
func (h *ShopHandler) GetShop(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	shop, err := h.shopService.GetShopByID(c.Request.Context(), shopID)
	if err != nil {
		h.logger.Error("Failed to get shop: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// GetShopBySlug godoc
// @Summary Get shop by slug
// @Description Get a specific shop by slug
// @Tags shops
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Success 200 {object} types.ShopResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/slug/{slug} [get]
func (h *ShopHandler) GetShopBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	shop, err := h.shopService.GetShopBySlug(c.Request.Context(), slug)
	if err != nil {
		h.logger.Error("Failed to get shop by slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Shop not found"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// GetBranchBySlug godoc
// @Summary Get branch by slug
// @Description Get a specific branch by shop slug and branch slug
// @Tags branches
// @Accept json
// @Produce json
// @Param slug path string true "Shop slug"
// @Param branchSlug path string true "Branch slug"
// @Success 200 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/slug/{slug}/branches/slug/{branchSlug} [get]
func (h *ShopHandler) GetBranchBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug is required"})
		return
	}

	if branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Branch slug is required"})
		return
	}

	branch, err := h.shopService.GetBranchBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.Error("Failed to get branch by slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// GetShopsByOwner godoc
// @Summary Get shops by owner
// @Description Get all shops owned by a specific user
// @Tags shops
// @Accept json
// @Produce json
// @Param ownerId path string true "Owner ID"
// @Success 200 {array} types.ShopResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/owner/{ownerId} [get]
func (h *ShopHandler) GetShopsByOwner(c *gin.Context) {
	ownerID, err := uuid.Parse(c.Param("ownerId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid owner ID"})
		return
	}

	shops, err := h.shopService.GetShopsByOwner(c.Request.Context(), ownerID)
	if err != nil {
		h.logger.Error("Failed to get shops by owner: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get shops"})
		return
	}

	c.JSON(http.StatusOK, shops)
}

// GetShopsByType godoc
// @Summary Get shops by type
// @Description Get all shops of a specific type
// @Tags shops
// @Accept json
// @Produce json
// @Param type path string true "Shop type"
// @Success 200 {array} types.ShopResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/type/{type} [get]
func (h *ShopHandler) GetShopsByType(c *gin.Context) {
	shopType := c.Param("type")
	if shopType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop type is required"})
		return
	}

	shops, err := h.shopService.GetShopsByType(c.Request.Context(), shopType)
	if err != nil {
		h.logger.Error("Failed to get shops by type: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get shops"})
		return
	}

	c.JSON(http.StatusOK, shops)
}

// CreateShop godoc
// @Summary Create a new shop
// @Description Create a new shop
// @Tags shops
// @Accept json
// @Produce json
// @Param shop body types.CreateShopRequest true "Shop data"
// @Success 201 {object} types.ShopResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops [post]
func (h *ShopHandler) CreateShop(c *gin.Context) {
	// Get owner ID from context (set by auth middleware)
	ownerIDRaw, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	// Handle both string and UUID formats
	var ownerUUID uuid.UUID
	var err error

	switch v := ownerIDRaw.(type) {
	case string:
		// Try to parse as UUID first
		ownerUUID, err = uuid.Parse(v)
		if err != nil {
			// If not a UUID, create a deterministic UUID from the string
			// This handles OAuth provider IDs like Google's numeric IDs
			ownerUUID = uuid.NewSHA1(uuid.NameSpaceOID, []byte(v))
		}
	case uuid.UUID:
		ownerUUID = v
	default:
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Invalid user ID format"})
		return
	}

	var req types.CreateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	shop, err := h.shopService.CreateShop(c.Request.Context(), ownerUUID, req)
	if err != nil {
		h.logger.Error("Failed to create shop: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create shop"})
		return
	}

	c.JSON(http.StatusCreated, shop)
}

// UpdateShop godoc
// @Summary Update a shop
// @Description Update an existing shop
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Param shop body types.UpdateShopRequest true "Shop update data"
// @Success 200 {object} types.ShopResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{id} [put]
func (h *ShopHandler) UpdateShop(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	var req types.UpdateShopRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	shop, err := h.shopService.UpdateShop(c.Request.Context(), shopID, req)
	if err != nil {
		h.logger.Error("Failed to update shop: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update shop"})
		return
	}

	c.JSON(http.StatusOK, shop)
}

// DeleteShop godoc
// @Summary Delete a shop
// @Description Delete an existing shop
// @Tags shops
// @Accept json
// @Produce json
// @Param id path string true "Shop ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{id} [delete]
func (h *ShopHandler) DeleteShop(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	if err := h.shopService.DeleteShop(c.Request.Context(), shopID); err != nil {
		h.logger.Error("Failed to delete shop: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete shop"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetBranches godoc
// @Summary Get shop branches
// @Description Get branches for a specific shop
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param status query string false "Status filter"
// @Param is_active query bool false "Active filter"
// @Param city query string false "City filter"
// @Param state query string false "State filter"
// @Param search query string false "Search filter"
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Success 200 {object} types.BranchesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches [get]
func (h *ShopHandler) GetBranches(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	var filters types.BranchFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	branches, err := h.shopService.GetBranches(c.Request.Context(), shopID, filters)
	if err != nil {
		h.logger.Error("Failed to get branches: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get branches"})
		return
	}

	c.JSON(http.StatusOK, branches)
}

// GetBranch godoc
// @Summary Get branch by ID
// @Description Get a specific branch by ID
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId} [get]
func (h *ShopHandler) GetBranch(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	branch, err := h.shopService.GetBranchByID(c.Request.Context(), shopID, branchID)
	if err != nil {
		h.logger.Error("Failed to get branch: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch not found"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// CreateBranch godoc
// @Summary Create a new branch
// @Description Create a new branch for a shop
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branch body types.CreateBranchRequest true "Branch data"
// @Success 201 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches [post]
func (h *ShopHandler) CreateBranch(c *gin.Context) {
	shopID, err := uuid.Parse(c.Param("shopId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid shop ID"})
		return
	}

	var req types.CreateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	branch, err := h.shopService.CreateBranch(c.Request.Context(), shopID, req)
	if err != nil {
		h.logger.Error("Failed to create branch: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create branch"})
		return
	}

	c.JSON(http.StatusCreated, branch)
}

// UpdateBranch godoc
// @Summary Update a branch
// @Description Update an existing branch
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param branch body types.UpdateBranchRequest true "Branch update data"
// @Success 200 {object} types.BranchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId} [put]
func (h *ShopHandler) UpdateBranch(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.UpdateBranchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	branch, err := h.shopService.UpdateBranch(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to update branch: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update branch"})
		return
	}

	c.JSON(http.StatusOK, branch)
}

// DeleteBranch godoc
// @Summary Delete a branch
// @Description Delete an existing branch
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId} [delete]
func (h *ShopHandler) DeleteBranch(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	if err := h.shopService.DeleteBranch(c.Request.Context(), branchID); err != nil {
		h.logger.Error("Failed to delete branch: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete branch"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetBranchSettings godoc
// @Summary Get branch settings
// @Description Get settings for a specific branch
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Success 200 {object} types.BranchSettingsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/settings [get]
func (h *ShopHandler) GetBranchSettings(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	settings, err := h.shopService.GetBranchSettings(c.Request.Context(), branchID)
	if err != nil {
		h.logger.Error("Failed to get branch settings: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Branch settings not found"})
		return
	}

	c.JSON(http.StatusOK, settings)
}

// UpdateBranchSettings godoc
// @Summary Update branch settings
// @Description Update settings for a specific branch
// @Tags branches
// @Accept json
// @Produce json
// @Param shopId path string true "Shop ID"
// @Param branchId path string true "Branch ID"
// @Param settings body types.BranchSettingsRequest true "Branch settings data"
// @Success 200 {object} types.BranchSettingsResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /shops/{shopId}/branches/{branchId}/settings [put]
func (h *ShopHandler) UpdateBranchSettings(c *gin.Context) {
	branchID, err := uuid.Parse(c.Param("branchId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid branch ID"})
		return
	}

	var req types.BranchSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	settings, err := h.shopService.UpdateBranchSettings(c.Request.Context(), branchID, req)
	if err != nil {
		h.logger.Error("Failed to update branch settings: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update branch settings"})
		return
	}

	c.JSON(http.StatusOK, settings)
}
