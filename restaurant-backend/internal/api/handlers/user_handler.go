package handlers

import (
	"net/http"

	"restaurant-backend/internal/services"
	"restaurant-backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// UserHandler handles user-related HTTP requests
type UserHandler struct {
	userService *services.UserService
	logger      *logrus.Logger
}

func NewUserHandler(userService *services.UserService, logger *logrus.Logger) *UserHandler {
	return &UserHandler{userService: userService, logger: logger}
}

// GetUsers godoc
// @Summary Get all users/staff for a merchant
// @Description Get all users/staff for a specific merchant with filtering and pagination
// @Tags users
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param page query int false "Page number"
// @Param limit query int false "Items per page"
// @Param search query string false "Search term"
// @Param role query string false "Filter by role"
// @Param status query string false "Filter by status"
// @Success 200 {object} types.UsersResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff [get]
func (h *UserHandler) GetUsers(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var filters types.UserFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	users, err := h.userService.GetUsers(c.Request.Context(), merchantID, filters)
	if err != nil {
		h.logger.Error("Failed to get users: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// CreateUser godoc
// @Summary Create a new user/staff member
// @Description Create a new user/staff member for a merchant
// @Tags users
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param user body types.CreateUserRequest true "User data"
// @Success 201 {object} types.UserResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.CreateUser(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create user: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusCreated, user)
}

// GetUser godoc
// @Summary Get a specific user/staff member
// @Description Get a specific user/staff member by ID
// @Tags users
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param userId path string true "User ID"
// @Success 200 {object} types.UserResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{userId} [get]
func (h *UserHandler) GetUser(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	user, err := h.userService.GetUserByID(c.Request.Context(), merchantID, userID)
	if err != nil {
		h.logger.Error("Failed to get user: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUser godoc
// @Summary Update a user/staff member
// @Description Update a specific user/staff member by ID
// @Tags users
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param userId path string true "User ID"
// @Param user body types.UpdateUserRequest true "User update data"
// @Success 200 {object} types.UserResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{userId} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req types.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.UpdateUser(c.Request.Context(), merchantID, userID, req)
	if err != nil {
		h.logger.Error("Failed to update user: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUser godoc
// @Summary Delete a user/staff member
// @Description Delete a specific user/staff member by ID
// @Tags users
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param userId path string true "User ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{userId} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if err := h.userService.DeleteUser(c.Request.Context(), merchantID, userID); err != nil {
		h.logger.Error("Failed to delete user: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.Status(http.StatusNoContent)
}

// UpdateUserStatus godoc
// @Summary Update user status
// @Description Update the status of a specific user/staff member
// @Tags users
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param userId path string true "User ID"
// @Param status body types.UpdateUserStatusRequest true "Status update data"
// @Success 200 {object} types.UserResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/{userId}/status [patch]
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var req types.UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.UpdateUserStatus(c.Request.Context(), merchantID, userID, req.Status)
	if err != nil {
		h.logger.Error("Failed to update user status: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user status"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// GetRoles godoc
// @Summary Get all roles for a merchant
// @Description Get all roles available for a specific merchant
// @Tags roles
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Success 200 {object} types.RolesResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/roles [get]
func (h *UserHandler) GetRoles(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	roles, err := h.userService.GetRoles(c.Request.Context(), merchantID)
	if err != nil {
		h.logger.Error("Failed to get roles: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get roles"})
		return
	}

	c.JSON(http.StatusOK, roles)
}

// CreateRole godoc
// @Summary Create a new role
// @Description Create a new role for a merchant
// @Tags roles
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param role body types.CreateRoleRequest true "Role data"
// @Success 201 {object} types.RoleResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/roles [post]
func (h *UserHandler) CreateRole(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	var req types.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	role, err := h.userService.CreateRole(c.Request.Context(), merchantID, req)
	if err != nil {
		h.logger.Error("Failed to create role: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create role"})
		return
	}

	c.JSON(http.StatusCreated, role)
}

// UpdateRole godoc
// @Summary Update a role
// @Description Update a specific role by ID
// @Tags roles
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param roleId path string true "Role ID"
// @Param role body types.UpdateRoleRequest true "Role update data"
// @Success 200 {object} types.RoleResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/roles/{roleId} [put]
func (h *UserHandler) UpdateRole(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	roleID, err := uuid.Parse(c.Param("roleId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	var req types.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	role, err := h.userService.UpdateRole(c.Request.Context(), merchantID, roleID, req)
	if err != nil {
		h.logger.Error("Failed to update role: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update role"})
		return
	}

	c.JSON(http.StatusOK, role)
}

// DeleteRole godoc
// @Summary Delete a role
// @Description Delete a specific role by ID
// @Tags roles
// @Accept json
// @Produce json
// @Param merchantId path string true "Merchant ID"
// @Param roleId path string true "Role ID"
// @Success 204
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Security BearerAuth
// @Router /merchants/{merchantId}/staff/roles/{roleId} [delete]
func (h *UserHandler) DeleteRole(c *gin.Context) {
	merchantID, err := uuid.Parse(c.Param("merchantId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid merchant ID"})
		return
	}

	roleID, err := uuid.Parse(c.Param("roleId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	if err := h.userService.DeleteRole(c.Request.Context(), merchantID, roleID); err != nil {
		h.logger.Error("Failed to delete role: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete role"})
		return
	}

	c.Status(http.StatusNoContent)
}

// Slug-based handlers

// GetUsersBySlug handles getting users by shop and branch slug
func (h *UserHandler) GetUsersBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	var filters types.UserFilters
	if err := c.ShouldBindQuery(&filters); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid query parameters", "details": err.Error()})
		return
	}

	users, err := h.userService.GetUsersBySlug(c.Request.Context(), shopSlug, branchSlug, filters)
	if err != nil {
		h.logger.Error("Failed to get users by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get users"})
		return
	}

	c.JSON(http.StatusOK, users)
}

// CreateUserBySlug handles creating a user by shop and branch slug
func (h *UserHandler) CreateUserBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	var req types.CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.CreateUserBySlug(c.Request.Context(), shopSlug, branchSlug, req)
	if err != nil {
		h.logger.Error("Failed to create user by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create user"})
		return
	}

	c.JSON(http.StatusCreated, user)
}

// GetUserBySlug handles getting a user by shop and branch slug
func (h *UserHandler) GetUserBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	user, err := h.userService.GetUserBySlug(c.Request.Context(), shopSlug, branchSlug, userID)
	if err != nil {
		h.logger.Error("Failed to get user by slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUserBySlug handles updating a user by shop and branch slug
func (h *UserHandler) UpdateUserBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	var req types.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.UpdateUserBySlug(c.Request.Context(), shopSlug, branchSlug, userID, req)
	if err != nil {
		h.logger.Error("Failed to update user by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUserBySlug handles deleting a user by shop and branch slug
func (h *UserHandler) DeleteUserBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	if err := h.userService.DeleteUserBySlug(c.Request.Context(), shopSlug, branchSlug, userID); err != nil {
		h.logger.Error("Failed to delete user by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.Status(http.StatusNoContent)
}

// UpdateUserStatusBySlug handles updating user status by shop and branch slug
func (h *UserHandler) UpdateUserStatusBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userID, err := uuid.Parse(c.Param("userId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	var req types.UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.UpdateUserStatusBySlug(c.Request.Context(), shopSlug, branchSlug, userID, req.Status)
	if err != nil {
		h.logger.Error("Failed to update user status by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user status"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// GetRolesBySlug handles getting roles by shop and branch slug
func (h *UserHandler) GetRolesBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	roles, err := h.userService.GetRolesBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.Error("Failed to get roles by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get roles"})
		return
	}

	c.JSON(http.StatusOK, roles)
}

// CreateRoleBySlug handles creating a role by shop and branch slug
func (h *UserHandler) CreateRoleBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	var req types.CreateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	role, err := h.userService.CreateRoleBySlug(c.Request.Context(), shopSlug, branchSlug, req)
	if err != nil {
		h.logger.Error("Failed to create role by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create role"})
		return
	}

	c.JSON(http.StatusCreated, role)
}

// UpdateRoleBySlug handles updating a role by shop and branch slug
func (h *UserHandler) UpdateRoleBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	roleID, err := uuid.Parse(c.Param("roleId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	var req types.UpdateRoleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	role, err := h.userService.UpdateRoleBySlug(c.Request.Context(), shopSlug, branchSlug, roleID, req)
	if err != nil {
		h.logger.Error("Failed to update role by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update role"})
		return
	}

	c.JSON(http.StatusOK, role)
}

// DeleteRoleBySlug handles deleting a role by shop and branch slug
func (h *UserHandler) DeleteRoleBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	roleID, err := uuid.Parse(c.Param("roleId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid role ID"})
		return
	}

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	if err := h.userService.DeleteRoleBySlug(c.Request.Context(), shopSlug, branchSlug, roleID); err != nil {
		h.logger.Error("Failed to delete role by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete role"})
		return
	}

	c.Status(http.StatusNoContent)
}

// GetPermissionsBySlug handles getting permissions by shop and branch slug
func (h *UserHandler) GetPermissionsBySlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")

	if shopSlug == "" || branchSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug and branch slug are required"})
		return
	}

	permissions, err := h.userService.GetPermissionsBySlug(c.Request.Context(), shopSlug, branchSlug)
	if err != nil {
		h.logger.Error("Failed to get permissions by slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get permissions"})
		return
	}

	c.JSON(http.StatusOK, permissions)
}

// GetUserByUserSlug handles getting a user by shop slug, branch slug, and user slug
func (h *UserHandler) GetUserByUserSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userSlug := c.Param("userSlug")

	if shopSlug == "" || branchSlug == "" || userSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug, branch slug, and user slug are required"})
		return
	}

	user, err := h.userService.GetUserByUserSlug(c.Request.Context(), shopSlug, branchSlug, userSlug)
	if err != nil {
		h.logger.Error("Failed to get user by user slug: ", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUserByUserSlug handles updating a user by shop slug, branch slug, and user slug
func (h *UserHandler) UpdateUserByUserSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userSlug := c.Param("userSlug")

	if shopSlug == "" || branchSlug == "" || userSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug, branch slug, and user slug are required"})
		return
	}

	var req types.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	user, err := h.userService.UpdateUserByUserSlug(c.Request.Context(), shopSlug, branchSlug, userSlug, req)
	if err != nil {
		h.logger.Error("Failed to update user by user slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update user"})
		return
	}

	c.JSON(http.StatusOK, user)
}

// DeleteUserByUserSlug handles deleting a user by shop slug, branch slug, and user slug
func (h *UserHandler) DeleteUserByUserSlug(c *gin.Context) {
	shopSlug := c.Param("slug")
	branchSlug := c.Param("branchSlug")
	userSlug := c.Param("userSlug")

	if shopSlug == "" || branchSlug == "" || userSlug == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Shop slug, branch slug, and user slug are required"})
		return
	}

	err := h.userService.DeleteUserByUserSlug(c.Request.Context(), shopSlug, branchSlug, userSlug)
	if err != nil {
		h.logger.Error("Failed to delete user by user slug: ", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}
