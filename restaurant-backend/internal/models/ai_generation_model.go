package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// AIGenerationJob represents an AI menu generation job
type AIGenerationJob struct {
	BaseModel
	BranchID    uuid.UUID              `json:"branch_id" gorm:"type:uuid;not null;index"`
	UserID      uuid.UUID              `json:"user_id" gorm:"type:uuid;not null;index"`
	Type        string                 `json:"type" gorm:"type:varchar(50);not null"` // "menu_image", "text", "food_images"
	Status      string                 `json:"status" gorm:"type:varchar(50);not null;default:'pending'"` // "pending", "processing", "completed", "failed"
	Progress    int                    `json:"progress" gorm:"default:0"` // 0-100
	InputData   AIGenerationInputData  `json:"input_data" gorm:"type:jsonb"`
	OutputData  AIGenerationOutputData `json:"output_data" gorm:"type:jsonb"`
	ErrorMsg    string                 `json:"error_message" gorm:"type:text"`
	StartedAt   *time.Time             `json:"started_at"`
	CompletedAt *time.Time             `json:"completed_at"`

	// Relationships
	Branch ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	User   User       `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// AIGenerationInputData represents the input data for AI generation
type AIGenerationInputData struct {
	// For menu image type
	MenuImageURL string `json:"menu_image_url,omitempty"`
	
	// For text type
	MenuText string `json:"menu_text,omitempty"`
	
	// For food images type
	FoodImageURLs []string `json:"food_image_urls,omitempty"`
	
	// Additional context
	CuisineType   string `json:"cuisine_type,omitempty"`
	PriceRange    string `json:"price_range,omitempty"`
	RestaurantName string `json:"restaurant_name,omitempty"`
}

// AIGenerationOutputData represents the output data from AI generation
type AIGenerationOutputData struct {
	MenuItems []AIGeneratedMenuItem `json:"menu_items,omitempty"`
	MenuInfo  *AIGeneratedMenuInfo  `json:"menu_info,omitempty"`
}

// AIGeneratedMenuItem represents a menu item generated by AI
type AIGeneratedMenuItem struct {
	Name            string   `json:"name"`
	Description     string   `json:"description"`
	Price           float64  `json:"price"`
	Category        string   `json:"category"`
	ImageURL        string   `json:"image_url,omitempty"`
	Ingredients     []string `json:"ingredients,omitempty"`
	Allergens       []string `json:"allergens,omitempty"`
	IsVegetarian    bool     `json:"is_vegetarian"`
	IsVegan         bool     `json:"is_vegan"`
	IsGlutenFree    bool     `json:"is_gluten_free"`
	IsSpicy         bool     `json:"is_spicy"`
	SpiceLevel      int      `json:"spice_level"`
	PreparationTime int      `json:"preparation_time,omitempty"`
	Tags            []string `json:"tags,omitempty"`
}

// AIGeneratedMenuInfo represents menu information generated by AI
type AIGeneratedMenuInfo struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	ImageURL    string `json:"image_url,omitempty"`
}

// BeforeCreate hook for AIGenerationJob
func (job *AIGenerationJob) BeforeCreate(tx *gorm.DB) error {
	if err := job.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Set default status if not provided
	if job.Status == "" {
		job.Status = "pending"
	}

	// Initialize empty data if nil
	if job.InputData.MenuImageURL == "" && job.InputData.MenuText == "" && len(job.InputData.FoodImageURLs) == 0 {
		job.InputData = AIGenerationInputData{}
	}
	if len(job.OutputData.MenuItems) == 0 {
		job.OutputData = AIGenerationOutputData{MenuItems: []AIGeneratedMenuItem{}}
	}

	return nil
}

// IsCompleted returns true if the job is completed (success or failure)
func (job *AIGenerationJob) IsCompleted() bool {
	return job.Status == "completed" || job.Status == "failed"
}

// IsProcessing returns true if the job is currently being processed
func (job *AIGenerationJob) IsProcessing() bool {
	return job.Status == "processing"
}

// MarkAsStarted marks the job as started
func (job *AIGenerationJob) MarkAsStarted() {
	now := time.Now()
	job.Status = "processing"
	job.StartedAt = &now
	job.Progress = 10
}

// MarkAsCompleted marks the job as completed
func (job *AIGenerationJob) MarkAsCompleted() {
	now := time.Now()
	job.Status = "completed"
	job.CompletedAt = &now
	job.Progress = 100
}

// MarkAsFailed marks the job as failed with an error message
func (job *AIGenerationJob) MarkAsFailed(errorMsg string) {
	now := time.Now()
	job.Status = "failed"
	job.CompletedAt = &now
	job.ErrorMsg = errorMsg
}

// UpdateProgress updates the job progress
func (job *AIGenerationJob) UpdateProgress(progress int) {
	if progress < 0 {
		progress = 0
	}
	if progress > 100 {
		progress = 100
	}
	job.Progress = progress
}
