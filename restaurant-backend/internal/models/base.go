package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// BeforeC<PERSON> will set a UUID rather than numeric ID
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if base.ID == uuid.Nil {
		base.ID = uuid.New()
	}
	return nil
}

// PaginatedResponse represents a paginated response structure
type PaginatedResponse[T any] struct {
	Data       []T `json:"data"`
	Total      int `json:"total"`
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	TotalPages int `json:"total_pages"`
}

// Address represents a physical address
type Address struct {
	Street  string `json:"street" gorm:"type:varchar(255)"`
	City    string `json:"city" gorm:"type:varchar(100)"`
	State   string `json:"state" gorm:"type:varchar(100)"`
	ZipCode string `json:"zip_code" gorm:"type:varchar(20)"`
	Country string `json:"country" gorm:"type:varchar(100)"`
}

// ContactInfo represents contact information
type ContactInfo struct {
	Name         string `json:"name" gorm:"type:varchar(255)"`
	Relationship string `json:"relationship" gorm:"type:varchar(100)"`
	Phone        string `json:"phone" gorm:"type:varchar(50)"`
	Email        string `json:"email" gorm:"type:varchar(255)"`
}

// Position represents a 2D position
type Position struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// Size represents dimensions
type Size struct {
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
}

// NutritionalInfo represents nutritional information for menu items
type NutritionalInfo struct {
	Calories int     `json:"calories"`
	Protein  float64 `json:"protein"`
	Carbs    float64 `json:"carbs"`
	Fat      float64 `json:"fat"`
	Fiber    float64 `json:"fiber"`
	Sodium   float64 `json:"sodium"`
}

// Response represents a response to a review
type Response struct {
	Message     string    `json:"message"`
	RespondedBy string    `json:"responded_by"`
	RespondedAt time.Time `json:"responded_at"`
}

// WorkSchedule represents a staff member's work schedule for a day
type WorkSchedule struct {
	DayOfWeek    int    `json:"day_of_week"` // 0-6 (Sunday-Saturday)
	StartTime    string `json:"start_time"`  // HH:MM format
	EndTime      string `json:"end_time"`    // HH:MM format
	IsWorkingDay bool   `json:"is_working_day"`
}

// PerformanceMetrics represents staff performance metrics
type PerformanceMetrics struct {
	OrdersServed     int       `json:"orders_served"`
	CustomerRating   float64   `json:"customer_rating"`
	PunctualityScore int       `json:"punctuality_score"`
	SalesGenerated   float64   `json:"sales_generated"`
	LastReviewDate   time.Time `json:"last_review_date"`
}

// FloorPlan represents the layout configuration for a floor
type FloorPlan struct {
	Width           float64 `json:"width"`
	Height          float64 `json:"height"`
	BackgroundImage string  `json:"background_image"`
}

// MenuItemOption represents an option for a menu item (e.g., size, toppings)
type MenuItemOption struct {
	ID       uuid.UUID        `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name     string           `json:"name" gorm:"type:varchar(255);not null"`
	Type     string           `json:"type" gorm:"type:varchar(50);not null"` // single, multiple
	Required bool             `json:"required" gorm:"default:false"`
	Choices  []MenuItemChoice `json:"choices" gorm:"foreignKey:OptionID;constraint:OnDelete:CASCADE"`
}

// MenuItemChoice represents a choice within an option
type MenuItemChoice struct {
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	OptionID  uuid.UUID `json:"option_id" gorm:"type:uuid;not null"`
	Name      string    `json:"name" gorm:"type:varchar(255);not null"`
	Price     float64   `json:"price" gorm:"type:decimal(10,2);default:0"`
	IsDefault bool      `json:"is_default" gorm:"default:false"`
}

// OrderModification represents modifications to an order item
type OrderModification struct {
	OptionID uuid.UUID `json:"option_id"`
	ChoiceID uuid.UUID `json:"choice_id"`
	Name     string    `json:"name"`
	Price    float64   `json:"price"`
}

// TableReservation represents current reservation info for a table
type TableReservation struct {
	ID           uuid.UUID `json:"id"`
	CustomerName string    `json:"customer_name"`
	PartySize    int       `json:"party_size"`
	Time         string    `json:"time"`
}

// TableOrder represents current order info for a table
type TableOrder struct {
	ID          uuid.UUID `json:"id"`
	OrderNumber string    `json:"order_number"`
	Total       float64   `json:"total"`
	Status      string    `json:"status"`
}

// Common status constants
const (
	// Order statuses
	OrderStatusPending   = "pending"
	OrderStatusConfirmed = "confirmed"
	OrderStatusPreparing = "preparing"
	OrderStatusReady     = "ready"
	OrderStatusServed    = "served"
	OrderStatusCompleted = "completed"
	OrderStatusCancelled = "cancelled"

	// Order types
	OrderTypeDineIn   = "dine-in"
	OrderTypeTakeaway = "takeaway"
	OrderTypeDelivery = "delivery"

	// Payment statuses
	PaymentStatusPending  = "pending"
	PaymentStatusPaid     = "paid"
	PaymentStatusFailed   = "failed"
	PaymentStatusRefunded = "refunded"

	// Reservation statuses
	ReservationStatusPending   = "pending"
	ReservationStatusConfirmed = "confirmed"
	ReservationStatusSeated    = "seated"
	ReservationStatusCompleted = "completed"
	ReservationStatusCancelled = "cancelled"
	ReservationStatusNoShow    = "no-show"

	// Table statuses
	TableStatusAvailable   = "available"
	TableStatusOccupied    = "occupied"
	TableStatusReserved    = "reserved"
	TableStatusCleaning    = "cleaning"
	TableStatusMaintenance = "maintenance"

	// Table shapes
	TableShapeSquare    = "square"
	TableShapeRound     = "round"
	TableShapeRectangle = "rectangle"

	// User/Staff statuses
	UserStatusActive    = "active"
	UserStatusInactive  = "inactive"
	UserStatusSuspended = "suspended"

	// Review statuses
	ReviewStatusPending  = "pending"
	ReviewStatusApproved = "approved"
	ReviewStatusRejected = "rejected"
	ReviewStatusFlagged  = "flagged"

	// Review sources
	ReviewSourceGoogle      = "google"
	ReviewSourceYelp        = "yelp"
	ReviewSourceFacebook    = "facebook"
	ReviewSourceTripAdvisor = "tripadvisor"
	ReviewSourceInternal    = "internal"
	ReviewSourceOther       = "other"

	// Review sentiments
	ReviewSentimentPositive = "positive"
	ReviewSentimentNeutral  = "neutral"
	ReviewSentimentNegative = "negative"

	// Subscription plans
	SubscriptionPlanBasic      = "basic"
	SubscriptionPlanPremium    = "premium"
	SubscriptionPlanEnterprise = "enterprise"
)
