package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MenuCategory represents a category of menu items
type MenuCategory struct {
	BaseModel
	BranchID    uuid.UUID `json:"branch_id" gorm:"type:uuid;not null;index"`
	Name        string    `json:"name" gorm:"type:varchar(255);not null"`
	Slug        string    `json:"slug" gorm:"type:varchar(255);not null"`
	Description string    `json:"description" gorm:"type:text"`
	ImageURL    string    `json:"image_url" gorm:"type:varchar(500)"`
	SortOrder   int       `json:"sort_order" gorm:"default:0"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`

	// Relationships
	Branch    ShopBranch `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	MenuItems []MenuItem `json:"menu_items,omitempty" gorm:"foreignKey:CategoryID"`
}

// MenuItem represents a menu item
type MenuItem struct {
	BaseModel
	BranchID        uuid.UUID           `json:"branch_id" gorm:"type:uuid;not null;index"`
	CategoryID      *uuid.UUID          `json:"category_id" gorm:"type:uuid;index"`
	Name            string              `json:"name" gorm:"type:varchar(255);not null"`
	Slug            string              `json:"slug" gorm:"type:varchar(255);not null"`
	Description     string              `json:"description" gorm:"type:text"`
	Price           float64             `json:"price" gorm:"type:decimal(10,2);not null"`
	Cost            *float64            `json:"cost" gorm:"type:decimal(10,2)"`
	Images          ImagesData          `json:"images" gorm:"type:jsonb"`
	Ingredients     IngredientsData     `json:"ingredients" gorm:"type:jsonb"`
	Allergens       AllergensData       `json:"allergens" gorm:"type:jsonb"`
	NutritionalInfo NutritionalInfoData `json:"nutritional_info" gorm:"type:jsonb"`
	PreparationTime *int                `json:"preparation_time"` // in minutes
	IsAvailable     bool                `json:"is_available" gorm:"default:true"`
	IsVegetarian    bool                `json:"is_vegetarian" gorm:"default:false"`
	IsVegan         bool                `json:"is_vegan" gorm:"default:false"`
	IsGlutenFree    bool                `json:"is_gluten_free" gorm:"default:false"`
	IsSpicy         bool                `json:"is_spicy" gorm:"default:false"`
	SpiceLevel      int                 `json:"spice_level" gorm:"default:0"` // 0-5 scale
	Tags            TagsData            `json:"tags" gorm:"type:jsonb"`
	Options         OptionsData         `json:"options" gorm:"type:jsonb"`

	// Relationships
	Branch   ShopBranch    `json:"branch,omitempty" gorm:"foreignKey:BranchID"`
	Category *MenuCategory `json:"category,omitempty" gorm:"foreignKey:CategoryID"`
}

// Custom types for JSON fields
type (
	ImagesData          []string
	IngredientsData     []string
	AllergensData       []string
	TagsData            []string
	NutritionalInfoData NutritionalInfo
	OptionsData         []MenuItemOption
)

// Scan and Value methods for ImagesData
func (i *ImagesData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, i)
}

func (i ImagesData) Value() (driver.Value, error) {
	return json.Marshal(i)
}

// Scan and Value methods for IngredientsData
func (i *IngredientsData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, i)
}

func (i IngredientsData) Value() (driver.Value, error) {
	return json.Marshal(i)
}

// Scan and Value methods for AllergensData
func (a *AllergensData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, a)
}

func (a AllergensData) Value() (driver.Value, error) {
	return json.Marshal(a)
}

// Scan and Value methods for TagsData
func (t *TagsData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, t)
}

func (t TagsData) Value() (driver.Value, error) {
	return json.Marshal(t)
}

// Scan and Value methods for NutritionalInfoData
func (n *NutritionalInfoData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, n)
}

func (n NutritionalInfoData) Value() (driver.Value, error) {
	return json.Marshal(n)
}

// Scan and Value methods for OptionsData
func (o *OptionsData) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}
	return json.Unmarshal(bytes, o)
}

func (o OptionsData) Value() (driver.Value, error) {
	return json.Marshal(o)
}

// TableName specifies the table name for MenuCategory
func (MenuCategory) TableName() string {
	return "menu_categories"
}

// TableName specifies the table name for MenuItem
func (MenuItem) TableName() string {
	return "menu_items"
}

// BeforeCreate hook for MenuCategory
func (mc *MenuCategory) BeforeCreate(tx *gorm.DB) error {
	if err := mc.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Generate slug if not provided
	if mc.Slug == "" {
		mc.Slug = Slugify(mc.Name)
	}

	return nil
}

// BeforeCreate hook for MenuItem
func (mi *MenuItem) BeforeCreate(tx *gorm.DB) error {
	if err := mi.BaseModel.BeforeCreate(tx); err != nil {
		return err
	}

	// Generate slug if not provided
	if mi.Slug == "" {
		mi.Slug = Slugify(mi.Name)
	}

	// Initialize empty slices if nil
	if mi.Images == nil {
		mi.Images = ImagesData{}
	}
	if mi.Ingredients == nil {
		mi.Ingredients = IngredientsData{}
	}
	if mi.Allergens == nil {
		mi.Allergens = AllergensData{}
	}
	if mi.Tags == nil {
		mi.Tags = TagsData{}
	}
	if mi.Options == nil {
		mi.Options = OptionsData{}
	}

	return nil
}

// GetPrimaryImage returns the first image URL or empty string
func (mi *MenuItem) GetPrimaryImage() string {
	if len(mi.Images) > 0 {
		return mi.Images[0]
	}
	return ""
}

// HasAllergen checks if the menu item contains a specific allergen
func (mi *MenuItem) HasAllergen(allergen string) bool {
	for _, a := range mi.Allergens {
		if a == allergen {
			return true
		}
	}
	return false
}

// HasTag checks if the menu item has a specific tag
func (mi *MenuItem) HasTag(tag string) bool {
	for _, t := range mi.Tags {
		if t == tag {
			return true
		}
	}
	return false
}

// AddTag adds a tag to the menu item if it doesn't already exist
func (mi *MenuItem) AddTag(tag string) {
	if !mi.HasTag(tag) {
		mi.Tags = append(mi.Tags, tag)
	}
}

// RemoveTag removes a tag from the menu item
func (mi *MenuItem) RemoveTag(tag string) {
	for i, t := range mi.Tags {
		if t == tag {
			mi.Tags = append(mi.Tags[:i], mi.Tags[i+1:]...)
			break
		}
	}
}

// GetDietaryInfo returns a summary of dietary information
func (mi *MenuItem) GetDietaryInfo() map[string]bool {
	return map[string]bool{
		"vegetarian":  mi.IsVegetarian,
		"vegan":       mi.IsVegan,
		"gluten_free": mi.IsGlutenFree,
		"spicy":       mi.IsSpicy,
	}
}

// GetSpiceLevelText returns a text representation of the spice level
func (mi *MenuItem) GetSpiceLevelText() string {
	switch mi.SpiceLevel {
	case 0:
		return "Not Spicy"
	case 1:
		return "Mild"
	case 2:
		return "Medium"
	case 3:
		return "Hot"
	case 4:
		return "Very Hot"
	case 5:
		return "Extremely Hot"
	default:
		return "Unknown"
	}
}

// CalculateBasePrice returns the base price without any options
func (mi *MenuItem) CalculateBasePrice() float64 {
	return mi.Price
}

// CalculatePriceWithOptions calculates the price including selected options
func (mi *MenuItem) CalculatePriceWithOptions(selectedOptions map[string][]string) float64 {
	basePrice := mi.Price

	for _, option := range mi.Options {
		if selectedChoices, exists := selectedOptions[option.ID.String()]; exists {
			for _, choice := range option.Choices {
				for _, selectedChoice := range selectedChoices {
					if choice.ID.String() == selectedChoice {
						basePrice += choice.Price
					}
				}
			}
		}
	}

	return basePrice
}

// GetAvailableOptions returns all available options for the menu item
func (mi *MenuItem) GetAvailableOptions() []MenuItemOption {
	return mi.Options
}

// GetRequiredOptions returns only the required options
func (mi *MenuItem) GetRequiredOptions() []MenuItemOption {
	var required []MenuItemOption
	for _, option := range mi.Options {
		if option.Required {
			required = append(required, option)
		}
	}
	return required
}

// IsValidOptionSelection validates if the selected options are valid
func (mi *MenuItem) IsValidOptionSelection(selectedOptions map[string][]string) bool {
	// Check if all required options are selected
	for _, option := range mi.GetRequiredOptions() {
		if _, exists := selectedOptions[option.ID.String()]; !exists {
			return false
		}
	}

	// Check if selected options exist and respect single/multiple choice rules
	for optionID, selectedChoices := range selectedOptions {
		var option *MenuItemOption
		for _, opt := range mi.Options {
			if opt.ID.String() == optionID {
				option = &opt
				break
			}
		}

		if option == nil {
			return false // Option doesn't exist
		}

		// For single choice options, only one choice should be selected
		if option.Type == "single" && len(selectedChoices) > 1 {
			return false
		}

		// Validate that all selected choices exist
		for _, selectedChoice := range selectedChoices {
			found := false
			for _, choice := range option.Choices {
				if choice.ID.String() == selectedChoice {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}

	return true
}

// GetItemCount returns the number of menu items in the category
func (mc *MenuCategory) GetItemCount() int {
	return len(mc.MenuItems)
}

// GetActiveItemCount returns the number of available menu items in the category
func (mc *MenuCategory) GetActiveItemCount() int {
	count := 0
	for _, item := range mc.MenuItems {
		if item.IsAvailable {
			count++
		}
	}
	return count
}
