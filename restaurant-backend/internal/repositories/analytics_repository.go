package repositories

import (
	"context"
	"time"

	"restaurant-backend/internal/models"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Analytics data structures
type SalesAnalytics struct {
	TotalRevenue float64 `json:"total_revenue"`
	TotalOrders  int     `json:"total_orders"`
}

type DailyBreakdown struct {
	Date          string  `json:"date"`
	Revenue       float64 `json:"revenue"`
	OrderCount    int     `json:"order_count"`
	CustomerCount int     `json:"customer_count"`
}

type HourlyTrend struct {
	Hour       int     `json:"hour"`
	Revenue    float64 `json:"revenue"`
	OrderCount int     `json:"order_count"`
}

type PaymentMethodStat struct {
	Method     string  `json:"method"`
	Count      int     `json:"count"`
	Revenue    float64 `json:"revenue"`
	Percentage float64 `json:"percentage"`
}

type OrderTypeStat struct {
	Type       string  `json:"type"`
	Count      int     `json:"count"`
	Revenue    float64 `json:"revenue"`
	Percentage float64 `json:"percentage"`
}

// AnalyticsRepository defines the interface for analytics data access
type AnalyticsRepository interface {
	// Sales metrics
	CreateSalesMetric(ctx context.Context, metric *models.SalesMetric) error
	UpsertSalesMetric(ctx context.Context, metric *models.SalesMetric) error
	GetSalesMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.SalesMetric, error)
	GetSalesMetricsByHour(ctx context.Context, branchID uuid.UUID, date time.Time) ([]*models.SalesMetric, error)

	// Customer metrics
	CreateCustomerMetric(ctx context.Context, metric *models.CustomerMetric) error
	UpsertCustomerMetric(ctx context.Context, metric *models.CustomerMetric) error
	GetCustomerMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.CustomerMetric, error)

	// Menu item metrics
	CreateMenuItemMetric(ctx context.Context, metric *models.MenuItemMetric) error
	UpsertMenuItemMetric(ctx context.Context, metric *models.MenuItemMetric) error
	GetMenuItemMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.MenuItemMetric, error)
	GetTopMenuItems(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time, limit int) ([]*models.MenuItemMetric, error)

	// Staff metrics
	CreateStaffMetric(ctx context.Context, metric *models.StaffMetric) error
	UpsertStaffMetric(ctx context.Context, metric *models.StaffMetric) error
	GetStaffMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.StaffMetric, error)

	// Table metrics
	CreateTableMetric(ctx context.Context, metric *models.TableMetric) error
	UpsertTableMetric(ctx context.Context, metric *models.TableMetric) error
	GetTableMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.TableMetric, error)

	// Reports
	CreateReport(ctx context.Context, report *models.AnalyticsReport) error
	GetReports(ctx context.Context, branchID uuid.UUID, reportType string) ([]*models.AnalyticsReport, error)
	GetReport(ctx context.Context, reportID uuid.UUID) (*models.AnalyticsReport, error)
	UpdateReport(ctx context.Context, report *models.AnalyticsReport) error

	// Additional analytics methods
	GetSalesAnalytics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) (*SalesAnalytics, error)
	GetDailySalesBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]DailyBreakdown, error)
	GetHourlySalesBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]HourlyTrend, error)
	GetPaymentMethodBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]PaymentMethodStat, error)
	GetOrderTypeBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]OrderTypeStat, error)
}

type analyticsRepository struct {
	db *gorm.DB
}

// NewAnalyticsRepository creates a new analytics repository
func NewAnalyticsRepository(db *gorm.DB) AnalyticsRepository {
	return &analyticsRepository{db: db}
}

// Sales metrics implementation
func (r *analyticsRepository) CreateSalesMetric(ctx context.Context, metric *models.SalesMetric) error {
	return r.db.WithContext(ctx).Create(metric).Error
}

func (r *analyticsRepository) UpsertSalesMetric(ctx context.Context, metric *models.SalesMetric) error {
	// Try to find existing metric for the same branch, date, hour, and type
	var existing models.SalesMetric
	err := r.db.WithContext(ctx).Where(
		"branch_id = ? AND date = ? AND hour = ? AND metric_type = ?",
		metric.BranchID, metric.Date, metric.Hour, metric.MetricType,
	).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		// Create new metric
		return r.db.WithContext(ctx).Create(metric).Error
	} else if err != nil {
		return err
	}

	// Update existing metric
	existing.Value += metric.Value
	existing.Count += metric.Count
	return r.db.WithContext(ctx).Save(&existing).Error
}

func (r *analyticsRepository) GetSalesMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.SalesMetric, error) {
	var metrics []*models.SalesMetric
	err := r.db.WithContext(ctx).
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("date ASC, hour ASC").
		Find(&metrics).Error
	return metrics, err
}

func (r *analyticsRepository) GetSalesMetricsByHour(ctx context.Context, branchID uuid.UUID, date time.Time) ([]*models.SalesMetric, error) {
	var metrics []*models.SalesMetric
	err := r.db.WithContext(ctx).
		Where("branch_id = ? AND date = ?", branchID, date).
		Order("hour ASC").
		Find(&metrics).Error
	return metrics, err
}

// Customer metrics implementation
func (r *analyticsRepository) CreateCustomerMetric(ctx context.Context, metric *models.CustomerMetric) error {
	return r.db.WithContext(ctx).Create(metric).Error
}

func (r *analyticsRepository) UpsertCustomerMetric(ctx context.Context, metric *models.CustomerMetric) error {
	var existing models.CustomerMetric
	err := r.db.WithContext(ctx).Where(
		"branch_id = ? AND date = ? AND customer_id = ?",
		metric.BranchID, metric.Date, metric.CustomerID,
	).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(metric).Error
	} else if err != nil {
		return err
	}

	// Update existing metric
	existing.VisitCount += metric.VisitCount
	existing.OrderCount += metric.OrderCount
	existing.TotalSpent += metric.TotalSpent
	existing.UpdateAverageOrderValue()
	existing.LastVisit = metric.LastVisit
	return r.db.WithContext(ctx).Save(&existing).Error
}

func (r *analyticsRepository) GetCustomerMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.CustomerMetric, error) {
	var metrics []*models.CustomerMetric
	err := r.db.WithContext(ctx).
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("date ASC").
		Find(&metrics).Error
	return metrics, err
}

// Menu item metrics implementation
func (r *analyticsRepository) CreateMenuItemMetric(ctx context.Context, metric *models.MenuItemMetric) error {
	return r.db.WithContext(ctx).Create(metric).Error
}

func (r *analyticsRepository) UpsertMenuItemMetric(ctx context.Context, metric *models.MenuItemMetric) error {
	var existing models.MenuItemMetric
	err := r.db.WithContext(ctx).Where(
		"branch_id = ? AND menu_item_id = ? AND date = ?",
		metric.BranchID, metric.MenuItemID, metric.Date,
	).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(metric).Error
	} else if err != nil {
		return err
	}

	// Update existing metric
	existing.OrderCount += metric.OrderCount
	existing.QuantitySold += metric.QuantitySold
	existing.Revenue += metric.Revenue
	existing.ViewCount += metric.ViewCount
	existing.CalculateConversionRate()
	return r.db.WithContext(ctx).Save(&existing).Error
}

func (r *analyticsRepository) GetMenuItemMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.MenuItemMetric, error) {
	var metrics []*models.MenuItemMetric
	err := r.db.WithContext(ctx).
		Preload("MenuItem").
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("date ASC").
		Find(&metrics).Error
	return metrics, err
}

func (r *analyticsRepository) GetTopMenuItems(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time, limit int) ([]*models.MenuItemMetric, error) {
	var metrics []*models.MenuItemMetric
	err := r.db.WithContext(ctx).
		Preload("MenuItem").
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("quantity_sold DESC").
		Limit(limit).
		Find(&metrics).Error
	return metrics, err
}

// Staff metrics implementation
func (r *analyticsRepository) CreateStaffMetric(ctx context.Context, metric *models.StaffMetric) error {
	return r.db.WithContext(ctx).Create(metric).Error
}

func (r *analyticsRepository) UpsertStaffMetric(ctx context.Context, metric *models.StaffMetric) error {
	var existing models.StaffMetric
	err := r.db.WithContext(ctx).Where(
		"branch_id = ? AND user_id = ? AND date = ?",
		metric.BranchID, metric.UserID, metric.Date,
	).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(metric).Error
	} else if err != nil {
		return err
	}

	// Update existing metric
	existing.OrdersProcessed += metric.OrdersProcessed
	existing.Revenue += metric.Revenue
	existing.HoursWorked += metric.HoursWorked
	existing.CalculateEfficiency()
	return r.db.WithContext(ctx).Save(&existing).Error
}

func (r *analyticsRepository) GetStaffMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.StaffMetric, error) {
	var metrics []*models.StaffMetric
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("date ASC").
		Find(&metrics).Error
	return metrics, err
}

// Table metrics implementation
func (r *analyticsRepository) CreateTableMetric(ctx context.Context, metric *models.TableMetric) error {
	return r.db.WithContext(ctx).Create(metric).Error
}

func (r *analyticsRepository) UpsertTableMetric(ctx context.Context, metric *models.TableMetric) error {
	var existing models.TableMetric
	err := r.db.WithContext(ctx).Where(
		"branch_id = ? AND table_id = ? AND date = ? AND hour = ?",
		metric.BranchID, metric.TableID, metric.Date, metric.Hour,
	).First(&existing).Error

	if err == gorm.ErrRecordNotFound {
		return r.db.WithContext(ctx).Create(metric).Error
	} else if err != nil {
		return err
	}

	// Update existing metric
	existing.OccupiedMinutes += metric.OccupiedMinutes
	existing.TurnoverCount += metric.TurnoverCount
	existing.Revenue += metric.Revenue
	existing.CalculateUtilizationRate()
	return r.db.WithContext(ctx).Save(&existing).Error
}

func (r *analyticsRepository) GetTableMetrics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]*models.TableMetric, error) {
	var metrics []*models.TableMetric
	err := r.db.WithContext(ctx).
		Preload("Table").
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Order("date ASC, hour ASC").
		Find(&metrics).Error
	return metrics, err
}

// Reports implementation
func (r *analyticsRepository) CreateReport(ctx context.Context, report *models.AnalyticsReport) error {
	return r.db.WithContext(ctx).Create(report).Error
}

func (r *analyticsRepository) GetReports(ctx context.Context, branchID uuid.UUID, reportType string) ([]*models.AnalyticsReport, error) {
	query := r.db.WithContext(ctx).Where("branch_id = ?", branchID)

	if reportType != "" {
		query = query.Where("report_type = ?", reportType)
	}

	var reports []*models.AnalyticsReport
	err := query.Order("created_at DESC").Find(&reports).Error
	return reports, err
}

func (r *analyticsRepository) GetReport(ctx context.Context, reportID uuid.UUID) (*models.AnalyticsReport, error) {
	var report models.AnalyticsReport
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Generator").
		First(&report, reportID).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &report, nil
}

func (r *analyticsRepository) UpdateReport(ctx context.Context, report *models.AnalyticsReport) error {
	return r.db.WithContext(ctx).Save(report).Error
}

// Additional analytics methods implementation
func (r *analyticsRepository) GetSalesAnalytics(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) (*SalesAnalytics, error) {
	var result struct {
		TotalRevenue float64
		TotalOrders  int64
	}

	err := r.db.WithContext(ctx).
		Table("sales_metrics").
		Select("COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_revenue, COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_orders",
			models.SalesMetricRevenue, models.SalesMetricOrderCount).
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Scan(&result).Error
	if err != nil {
		return nil, err
	}

	return &SalesAnalytics{
		TotalRevenue: result.TotalRevenue,
		TotalOrders:  int(result.TotalOrders),
	}, nil
}

func (r *analyticsRepository) GetDailySalesBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]DailyBreakdown, error) {
	var results []struct {
		Date           time.Time
		TotalRevenue   float64
		TotalOrders    int64
		TotalCustomers int64
	}

	err := r.db.WithContext(ctx).
		Table("sales_metrics").
		Select(`date,
			COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_revenue,
			COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_orders,
			COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_customers`,
			models.SalesMetricRevenue, models.SalesMetricOrderCount, models.SalesMetricCustomerCount).
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Group("date").
		Order("date ASC").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	var breakdown []DailyBreakdown
	for _, result := range results {
		breakdown = append(breakdown, DailyBreakdown{
			Date:          result.Date.Format("2006-01-02"),
			Revenue:       result.TotalRevenue,
			OrderCount:    int(result.TotalOrders),
			CustomerCount: int(result.TotalCustomers),
		})
	}

	return breakdown, nil
}

func (r *analyticsRepository) GetHourlySalesBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]HourlyTrend, error) {
	var results []struct {
		Hour         int
		TotalRevenue float64
		TotalOrders  int64
	}

	err := r.db.WithContext(ctx).
		Table("sales_metrics").
		Select(`hour,
			COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_revenue,
			COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_orders`,
			models.SalesMetricRevenue, models.SalesMetricOrderCount).
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Group("hour").
		Order("hour ASC").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// Create trends for all 24 hours
	hourlyMap := make(map[int]struct {
		Revenue float64
		Orders  int64
	})

	for _, result := range results {
		hourlyMap[result.Hour] = struct {
			Revenue float64
			Orders  int64
		}{
			Revenue: result.TotalRevenue,
			Orders:  result.TotalOrders,
		}
	}

	var trends []HourlyTrend
	for hour := 0; hour < 24; hour++ {
		data := hourlyMap[hour]
		trends = append(trends, HourlyTrend{
			Hour:       hour,
			Revenue:    data.Revenue,
			OrderCount: int(data.Orders),
		})
	}

	return trends, nil
}

func (r *analyticsRepository) GetPaymentMethodBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]PaymentMethodStat, error) {
	// For now, return mock data since we don't have payment method tracking in sales metrics
	// This would need to be implemented by querying actual order payment data
	var totalRevenue float64
	var totalOrders int64

	err := r.db.WithContext(ctx).
		Table("sales_metrics").
		Select("COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_revenue, COALESCE(SUM(CASE WHEN metric_type = ? THEN value END), 0) as total_orders",
			models.SalesMetricRevenue, models.SalesMetricOrderCount).
		Where("branch_id = ? AND date >= ? AND date <= ?", branchID, startDate, endDate).
		Scan(&struct {
			TotalRevenue float64
			TotalOrders  int64
		}{TotalRevenue: totalRevenue, TotalOrders: totalOrders}).Error
	if err != nil {
		return nil, err
	}

	// Mock payment method distribution
	stats := []PaymentMethodStat{
		{
			Method:     "cash",
			Count:      int(totalOrders * 40 / 100),
			Revenue:    totalRevenue * 0.4,
			Percentage: 40.0,
		},
		{
			Method:     "card",
			Count:      int(totalOrders * 45 / 100),
			Revenue:    totalRevenue * 0.45,
			Percentage: 45.0,
		},
		{
			Method:     "digital",
			Count:      int(totalOrders * 15 / 100),
			Revenue:    totalRevenue * 0.15,
			Percentage: 15.0,
		},
	}

	return stats, nil
}

func (r *analyticsRepository) GetOrderTypeBreakdown(ctx context.Context, branchID uuid.UUID, startDate, endDate time.Time) ([]OrderTypeStat, error) {
	// Query actual order data for order type breakdown
	var results []struct {
		OrderType string
		Count     int64
		Revenue   float64
	}

	err := r.db.WithContext(ctx).
		Table("orders").
		Select("order_type, COUNT(*) as count, COALESCE(SUM(total), 0) as revenue").
		Where("branch_id = ? AND created_at >= ? AND created_at <= ? AND status = ?",
			branchID, startDate, endDate, "completed").
		Group("order_type").
		Scan(&results).Error
	if err != nil {
		return nil, err
	}

	// Calculate totals for percentage calculation
	var totalOrders int64
	var totalRevenue float64
	for _, result := range results {
		totalOrders += result.Count
		totalRevenue += result.Revenue
	}

	var stats []OrderTypeStat
	for _, result := range results {
		percentage := 0.0
		if totalOrders > 0 {
			percentage = (float64(result.Count) / float64(totalOrders)) * 100
		}

		stats = append(stats, OrderTypeStat{
			Type:       result.OrderType,
			Count:      int(result.Count),
			Revenue:    result.Revenue,
			Percentage: percentage,
		})
	}

	return stats, nil
}
