package repositories

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CampaignRepository struct {
	db *gorm.DB
}

func NewCampaignRepository(db *gorm.DB) *CampaignRepository {
	return &CampaignRepository{db: db}
}

// Campaign methods
func (r *CampaignRepository) GetCampaigns(ctx context.Context, merchantID uuid.UUID, filters types.CampaignFilters) ([]models.CommunicationCampaign, int64, error) {
	var campaigns []models.CommunicationCampaign
	var total int64

	query := r.db.WithContext(ctx).Model(&models.CommunicationCampaign{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.TargetAudience != "" {
		query = query.Where("target_audience = ?", filters.TargetAudience)
	}
	if filters.TemplateID != uuid.Nil {
		query = query.Where("template_id = ?", filters.TemplateID)
	}
	if filters.SegmentID != uuid.Nil {
		query = query.Where("segment_id = ?", filters.SegmentID)
	}
	if filters.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ? OR subject ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and preload relationships
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Preload("Template").Preload("Segment").
		Offset(offset).Limit(filters.Limit).
		Order("created_at DESC").
		Find(&campaigns).Error; err != nil {
		return nil, 0, err
	}

	return campaigns, total, nil
}

func (r *CampaignRepository) GetCampaignByID(ctx context.Context, campaignID uuid.UUID) (*models.CommunicationCampaign, error) {
	var campaign models.CommunicationCampaign
	if err := r.db.WithContext(ctx).Preload("Template").Preload("Segment").
		First(&campaign, "id = ?", campaignID).Error; err != nil {
		return nil, err
	}
	return &campaign, nil
}

func (r *CampaignRepository) CreateCampaign(ctx context.Context, campaign *models.CommunicationCampaign) error {
	return r.db.WithContext(ctx).Create(campaign).Error
}

func (r *CampaignRepository) UpdateCampaign(ctx context.Context, campaignID uuid.UUID, updates map[string]interface{}) (*models.CommunicationCampaign, error) {
	var campaign models.CommunicationCampaign
	if err := r.db.WithContext(ctx).First(&campaign, "id = ?", campaignID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&campaign).Updates(updates).Error; err != nil {
		return nil, err
	}

	// Reload with relationships
	if err := r.db.WithContext(ctx).Preload("Template").Preload("Segment").
		First(&campaign, "id = ?", campaignID).Error; err != nil {
		return nil, err
	}

	return &campaign, nil
}

func (r *CampaignRepository) DeleteCampaign(ctx context.Context, campaignID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.CommunicationCampaign{}, "id = ?", campaignID).Error
}

func (r *CampaignRepository) ExecuteCampaign(ctx context.Context, campaignID uuid.UUID) (*models.CommunicationCampaign, error) {
	now := time.Now()
	updates := map[string]interface{}{
		"status":     "running",
		"started_at": &now,
	}

	return r.UpdateCampaign(ctx, campaignID, updates)
}

// Template methods
func (r *CampaignRepository) GetTemplates(ctx context.Context, merchantID uuid.UUID, filters types.TemplateFilters) ([]models.CommunicationTemplate, int64, error) {
	var templates []models.CommunicationTemplate
	var total int64

	query := r.db.WithContext(ctx).Model(&models.CommunicationTemplate{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.Category != "" {
		query = query.Where("category = ?", filters.Category)
	}
	if filters.IsDefault != nil {
		query = query.Where("is_default = ?", *filters.IsDefault)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ? OR content ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).
		Order("name").
		Find(&templates).Error; err != nil {
		return nil, 0, err
	}

	return templates, total, nil
}

func (r *CampaignRepository) GetTemplateByID(ctx context.Context, templateID uuid.UUID) (*models.CommunicationTemplate, error) {
	var template models.CommunicationTemplate
	if err := r.db.WithContext(ctx).First(&template, "id = ?", templateID).Error; err != nil {
		return nil, err
	}
	return &template, nil
}

func (r *CampaignRepository) CreateTemplate(ctx context.Context, template *models.CommunicationTemplate) error {
	return r.db.WithContext(ctx).Create(template).Error
}

func (r *CampaignRepository) UpdateTemplate(ctx context.Context, templateID uuid.UUID, updates map[string]interface{}) (*models.CommunicationTemplate, error) {
	var template models.CommunicationTemplate
	if err := r.db.WithContext(ctx).First(&template, "id = ?", templateID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&template).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &template, nil
}

func (r *CampaignRepository) DeleteTemplate(ctx context.Context, templateID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.CommunicationTemplate{}, "id = ?", templateID).Error
}

// Segment methods
func (r *CampaignRepository) GetSegments(ctx context.Context, merchantID uuid.UUID, filters types.SegmentFilters) ([]models.CampaignSegment, int64, error) {
	var segments []models.CampaignSegment
	var total int64

	query := r.db.WithContext(ctx).Model(&models.CampaignSegment{}).Where("merchant_id = ?", merchantID)

	// Apply filters
	if filters.Type != "" {
		query = query.Where("type = ?", filters.Type)
	}
	if filters.IsActive != nil {
		query = query.Where("is_active = ?", *filters.IsActive)
	}
	if filters.Search != "" {
		query = query.Where("name ILIKE ? OR description ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	if err := query.Offset(offset).Limit(filters.Limit).
		Order("name").
		Find(&segments).Error; err != nil {
		return nil, 0, err
	}

	return segments, total, nil
}

func (r *CampaignRepository) GetSegmentByID(ctx context.Context, segmentID uuid.UUID) (*models.CampaignSegment, error) {
	var segment models.CampaignSegment
	if err := r.db.WithContext(ctx).First(&segment, "id = ?", segmentID).Error; err != nil {
		return nil, err
	}
	return &segment, nil
}

func (r *CampaignRepository) CreateSegment(ctx context.Context, segment *models.CampaignSegment) error {
	return r.db.WithContext(ctx).Create(segment).Error
}

func (r *CampaignRepository) UpdateSegment(ctx context.Context, segmentID uuid.UUID, updates map[string]interface{}) (*models.CampaignSegment, error) {
	var segment models.CampaignSegment
	if err := r.db.WithContext(ctx).First(&segment, "id = ?", segmentID).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).Model(&segment).Updates(updates).Error; err != nil {
		return nil, err
	}

	return &segment, nil
}

func (r *CampaignRepository) DeleteSegment(ctx context.Context, segmentID uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.CampaignSegment{}, "id = ?", segmentID).Error
}

// Segment data methods (simplified implementations)
func (r *CampaignRepository) GetSegmentCustomers(ctx context.Context, segmentID uuid.UUID) ([]map[string]interface{}, error) {
	// This would implement the actual customer segmentation logic
	// For now, return empty slice
	return []map[string]interface{}{}, nil
}

func (r *CampaignRepository) GetSegmentEmails(ctx context.Context, segmentID uuid.UUID) ([]string, error) {
	// This would extract emails from the segmented customers
	// For now, return empty slice
	return []string{}, nil
}

func (r *CampaignRepository) GetSegmentPhones(ctx context.Context, segmentID uuid.UUID) ([]string, error) {
	// This would extract phone numbers from the segmented customers
	// For now, return empty slice
	return []string{}, nil
}

// Analytics methods
func (r *CampaignRepository) GetCommunicationAnalyticsOverview(ctx context.Context, merchantID uuid.UUID) (*types.CommunicationAnalyticsOverview, error) {
	var overview types.CommunicationAnalyticsOverview

	// Get total campaigns
	var totalCampaigns int64
	r.db.WithContext(ctx).Model(&models.CommunicationCampaign{}).
		Where("merchant_id = ?", merchantID).
		Count(&totalCampaigns)
	overview.TotalCampaigns = int(totalCampaigns)

	// Get active campaigns
	var activeCampaigns int64
	r.db.WithContext(ctx).Model(&models.CommunicationCampaign{}).
		Where("merchant_id = ? AND status = ?", merchantID, "running").
		Count(&activeCampaigns)
	overview.ActiveCampaigns = int(activeCampaigns)

	// Get aggregated stats
	var stats struct {
		TotalSent      int64
		TotalDelivered int64
		TotalOpened    int64
		TotalClicked   int64
	}

	r.db.WithContext(ctx).Model(&models.CommunicationCampaign{}).
		Where("merchant_id = ?", merchantID).
		Select("SUM(sent_count) as total_sent, SUM(delivered_count) as total_delivered, SUM(opened_count) as total_opened, SUM(clicked_count) as total_clicked").
		Scan(&stats)

	overview.TotalSent = int(stats.TotalSent)
	overview.TotalDelivered = int(stats.TotalDelivered)
	overview.TotalOpened = int(stats.TotalOpened)
	overview.TotalClicked = int(stats.TotalClicked)

	// Calculate rates
	if overview.TotalDelivered > 0 {
		overview.AverageOpenRate = float64(overview.TotalOpened) / float64(overview.TotalDelivered) * 100
	}
	if overview.TotalOpened > 0 {
		overview.AverageClickRate = float64(overview.TotalClicked) / float64(overview.TotalOpened) * 100
	}

	return &overview, nil
}

func (r *CampaignRepository) GetCampaignAnalytics(ctx context.Context, campaignID uuid.UUID) (*types.CampaignAnalyticsResponse, error) {
	var campaign models.CommunicationCampaign
	if err := r.db.WithContext(ctx).First(&campaign, "id = ?", campaignID).Error; err != nil {
		return nil, err
	}

	analytics := &types.CampaignAnalyticsResponse{
		CampaignID:       campaign.ID,
		CampaignName:     campaign.Name,
		TotalRecipients:  campaign.TotalRecipients,
		SentCount:        campaign.SentCount,
		DeliveredCount:   campaign.DeliveredCount,
		OpenedCount:      campaign.OpenedCount,
		ClickedCount:     campaign.ClickedCount,
		UnsubscribeCount: campaign.UnsubscribeCount,
		BounceCount:      campaign.BounceCount,
		CreatedAt:        campaign.CreatedAt,
		CompletedAt:      campaign.CompletedAt,
	}

	// Calculate rates
	if analytics.DeliveredCount > 0 {
		analytics.OpenRate = float64(analytics.OpenedCount) / float64(analytics.DeliveredCount) * 100
	}
	if analytics.OpenedCount > 0 {
		analytics.ClickRate = float64(analytics.ClickedCount) / float64(analytics.OpenedCount) * 100
	}
	if analytics.TotalRecipients > 0 {
		analytics.UnsubscribeRate = float64(analytics.UnsubscribeCount) / float64(analytics.TotalRecipients) * 100
		analytics.BounceRate = float64(analytics.BounceCount) / float64(analytics.TotalRecipients) * 100
	}

	return analytics, nil
}
