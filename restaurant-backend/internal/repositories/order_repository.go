package repositories

import (
	"context"
	"fmt"
	"strings"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// OrderRepository defines the interface for order data access
type OrderRepository interface {
	Create(ctx context.Context, order *models.Order) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Order, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) ([]models.Order, int64, error)
	Update(ctx context.Context, order *models.Order) error
	UpdateStatus(ctx context.Context, id uuid.UUID, status string) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetByOrderNumber(ctx context.Context, branchID uuid.UUID, orderNumber string) (*models.Order, error)
	GetActiveOrders(ctx context.Context, branchID uuid.UUID) ([]models.Order, error)
	GetOrderStats(ctx context.Context, branchID uuid.UUID, dateFrom, dateTo time.Time) (*types.OrderStatsResponse, error)
	GetDashboardStats(ctx context.Context, branchID uuid.UUID) (*types.DashboardStatsResponse, error)
	GetRecentActivity(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Order, error)
}

type orderRepository struct {
	db *gorm.DB
}

func NewOrderRepository(db *gorm.DB) OrderRepository {
	return &orderRepository{db: db}
}

func (r *orderRepository) Create(ctx context.Context, order *models.Order) error {
	return r.db.WithContext(ctx).Create(order).Error
}

func (r *orderRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Order, error) {
	var order models.Order
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Preload("Items").
		Preload("Items.MenuItem").
		First(&order, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order not found")
		}
		return nil, err
	}
	return &order, nil
}

func (r *orderRepository) GetByOrderNumber(ctx context.Context, branchID uuid.UUID, orderNumber string) (*models.Order, error) {
	var order models.Order
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Preload("Items").
		Preload("Items.MenuItem").
		Where("branch_id = ? AND order_number = ?", branchID, orderNumber).
		First(&order).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("order not found")
		}
		return nil, err
	}
	return &order, nil
}

func (r *orderRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) ([]models.Order, int64, error) {
	var orders []models.Order
	var total int64

	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	query := r.db.WithContext(ctx).Model(&models.Order{}).Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.OrderType != "" {
		query = query.Where("type = ?", filters.OrderType)
	}
	if filters.TableID != nil {
		query = query.Where("table_id = ?", *filters.TableID)
	}
	if filters.CustomerName != "" {
		searchTerm := "%" + strings.ToLower(filters.CustomerName) + "%"
		query = query.Where("LOWER(customer_name) LIKE ?", searchTerm)
	}
	if filters.PaymentStatus != "" {
		query = query.Where("payment_status = ?", filters.PaymentStatus)
	}
	if filters.PaymentMethod != "" {
		query = query.Where("payment_method = ?", filters.PaymentMethod)
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", *filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", *filters.DateTo)
	}
	if filters.MinAmount > 0 {
		query = query.Where("total >= ?", filters.MinAmount)
	}
	if filters.MaxAmount > 0 {
		query = query.Where("total <= ?", filters.MaxAmount)
	}
	if filters.Search != "" {
		searchTerm := "%" + strings.ToLower(filters.Search) + "%"
		query = query.Where(
			"LOWER(customer_name) LIKE ? OR LOWER(customer_phone) LIKE ? OR LOWER(order_number) LIKE ?",
			searchTerm, searchTerm, searchTerm,
		)
	}

	// Get total count
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "created_at DESC"
	if filters.SortBy != "" {
		sortOrder := "ASC"
		if filters.SortOrder == "desc" {
			sortOrder = "DESC"
		}

		switch filters.SortBy {
		case "order_number", "customer_name", "total_amount", "status", "created_at", "updated_at":
			// Map total_amount to total for database column
			sortColumn := filters.SortBy
			if sortColumn == "total_amount" {
				sortColumn = "total"
			}
			orderBy = sortColumn + " " + sortOrder
		default:
			orderBy = "created_at DESC"
		}
	}

	// Apply pagination and get results
	offset := (filters.Page - 1) * filters.Limit
	err := query.
		Preload("Branch").
		Preload("Table").
		Preload("Items").
		Preload("Items.MenuItem").
		Offset(offset).Limit(filters.Limit).
		Order(orderBy).
		Find(&orders).Error
	if err != nil {
		return nil, 0, err
	}

	return orders, total, nil
}

func (r *orderRepository) GetActiveOrders(ctx context.Context, branchID uuid.UUID) ([]models.Order, error) {
	var orders []models.Order
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Preload("Items").
		Preload("Items.MenuItem").
		Where("branch_id = ? AND status IN (?)", branchID, []string{
			types.OrderStatusPending,
			types.OrderStatusConfirmed,
			types.OrderStatusPreparing,
			types.OrderStatusReady,
		}).
		Order("created_at ASC").
		Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}

func (r *orderRepository) Update(ctx context.Context, order *models.Order) error {
	return r.db.WithContext(ctx).Save(order).Error
}

func (r *orderRepository) UpdateStatus(ctx context.Context, id uuid.UUID, status string) error {
	updates := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// Set completed_at if status is completed
	if status == types.OrderStatusCompleted {
		updates["completed_at"] = time.Now()
	}

	return r.db.WithContext(ctx).Model(&models.Order{}).
		Where("id = ?", id).Updates(updates).Error
}

func (r *orderRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Order{}, id).Error
}

func (r *orderRepository) GetOrderStats(ctx context.Context, branchID uuid.UUID, dateFrom, dateTo time.Time) (*types.OrderStatsResponse, error) {
	var stats types.OrderStatsResponse

	// Get total orders and revenue
	var totalOrders int64
	var totalRevenue float64
	err := r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND created_at BETWEEN ? AND ?", branchID, dateFrom, dateTo).
		Count(&totalOrders).Error
	if err != nil {
		return nil, err
	}

	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND created_at BETWEEN ? AND ? AND status = ?", branchID, dateFrom, dateTo, types.OrderStatusCompleted).
		Select("COALESCE(SUM(total), 0)").Scan(&totalRevenue).Error
	if err != nil {
		return nil, err
	}

	stats.TotalOrders = totalOrders
	stats.TotalRevenue = totalRevenue
	if totalOrders > 0 {
		stats.AverageOrderValue = totalRevenue / float64(totalOrders)
	}

	// Get orders by status
	var statusCounts []struct {
		Status string
		Count  int64
	}
	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Select("status, COUNT(*) as count").
		Where("branch_id = ? AND created_at BETWEEN ? AND ?", branchID, dateFrom, dateTo).
		Group("status").Scan(&statusCounts).Error
	if err != nil {
		return nil, err
	}

	stats.OrdersByStatus = make(map[string]int64)
	for _, sc := range statusCounts {
		stats.OrdersByStatus[sc.Status] = sc.Count
	}

	// Get orders by type
	var typeCounts []struct {
		OrderType string
		Count     int64
	}
	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Select("type, COUNT(*) as count").
		Where("branch_id = ? AND created_at BETWEEN ? AND ?", branchID, dateFrom, dateTo).
		Group("type").Scan(&typeCounts).Error
	if err != nil {
		return nil, err
	}

	stats.OrdersByType = make(map[string]int64)
	for _, tc := range typeCounts {
		stats.OrdersByType[tc.OrderType] = tc.Count
	}

	return &stats, nil
}

func (r *orderRepository) GetDashboardStats(ctx context.Context, branchID uuid.UUID) (*types.DashboardStatsResponse, error) {
	var stats types.DashboardStatsResponse
	today := time.Now().Truncate(24 * time.Hour)
	tomorrow := today.Add(24 * time.Hour)

	// Today's orders and revenue
	err := r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND created_at >= ? AND created_at < ?", branchID, today, tomorrow).
		Count(&stats.TodayOrders).Error
	if err != nil {
		return nil, err
	}

	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND created_at >= ? AND created_at < ? AND status = ?",
			branchID, today, tomorrow, types.OrderStatusCompleted).
		Select("COALESCE(SUM(total), 0)").Scan(&stats.TodayRevenue).Error
	if err != nil {
		return nil, err
	}

	// Order counts by status
	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND status = ?", branchID, types.OrderStatusPending).
		Count(&stats.PendingOrders).Error
	if err != nil {
		return nil, err
	}

	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND status IN (?)", branchID, []string{
			types.OrderStatusConfirmed, types.OrderStatusPreparing, types.OrderStatusReady,
		}).
		Count(&stats.ActiveOrders).Error
	if err != nil {
		return nil, err
	}

	err = r.db.WithContext(ctx).Model(&models.Order{}).
		Where("branch_id = ? AND created_at >= ? AND created_at < ? AND status = ?",
			branchID, today, tomorrow, types.OrderStatusCompleted).
		Count(&stats.CompletedOrders).Error
	if err != nil {
		return nil, err
	}

	return &stats, nil
}

func (r *orderRepository) GetRecentActivity(ctx context.Context, branchID uuid.UUID, limit int) ([]models.Order, error) {
	var orders []models.Order
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Preload("Items").
		Preload("Items.MenuItem").
		Where("branch_id = ?", branchID).
		Order("created_at DESC").
		Limit(limit).
		Find(&orders).Error
	if err != nil {
		return nil, err
	}

	return orders, nil
}
