package repositories

import (
	"context"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ReservationRepository defines the interface for reservation data access
type ReservationRepository interface {
	Create(ctx context.Context, reservation *models.Reservation) error
	GetByID(ctx context.Context, id uuid.UUID) (*models.Reservation, error)
	GetBySlug(ctx context.Context, slug string) (*models.Reservation, error)
	GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.ReservationFilters) ([]models.Reservation, int64, error)
	GetByBranchSlug(ctx context.Context, shopSlug, branchSlug string, filters types.ReservationFilters) ([]models.Reservation, int64, error)
	Update(ctx context.Context, reservation *models.Reservation) error
	UpdateBySlug(ctx context.Context, slug string, reservation *models.Reservation) error
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteBySlug(ctx context.Context, slug string) error
	GetTodayReservations(ctx context.Context, branchID uuid.UUID) ([]models.Reservation, error)
	GetTodayReservationsBySlug(ctx context.Context, shopSlug, branchSlug string) ([]models.Reservation, error)
	GetAvailability(ctx context.Context, branchID uuid.UUID, date time.Time) ([]types.TimeSlot, error)
	GetAvailabilityBySlug(ctx context.Context, shopSlug, branchSlug string, date time.Time) ([]types.TimeSlot, error)
	GetReservationStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReservationStats, error)
	GetReservationStatsBySlug(ctx context.Context, shopSlug, branchSlug string, period string) (*types.ReservationStats, error)
}

type reservationRepository struct {
	db *gorm.DB
}

func NewReservationRepository(db *gorm.DB) ReservationRepository {
	return &reservationRepository{db: db}
}

func (r *reservationRepository) Create(ctx context.Context, reservation *models.Reservation) error {
	return r.db.WithContext(ctx).Create(reservation).Error
}

func (r *reservationRepository) GetByID(ctx context.Context, id uuid.UUID) (*models.Reservation, error) {
	var reservation models.Reservation
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		First(&reservation, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &reservation, nil
}

func (r *reservationRepository) GetBySlug(ctx context.Context, slug string) (*models.Reservation, error) {
	var reservation models.Reservation
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Where("slug = ?", slug).
		First(&reservation).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &reservation, nil
}

func (r *reservationRepository) GetByBranchID(ctx context.Context, branchID uuid.UUID, filters types.ReservationFilters) ([]models.Reservation, int64, error) {
	query := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Where("branch_id = ?", branchID)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Date != "" {
		query = query.Where("DATE(reservation_date) = ?", filters.Date)
	}
	if filters.StartDate != "" && filters.EndDate != "" {
		query = query.Where("DATE(reservation_date) BETWEEN ? AND ?", filters.StartDate, filters.EndDate)
	}
	if filters.TableID != nil {
		query = query.Where("table_id = ?", *filters.TableID)
	}
	if filters.Search != "" {
		query = query.Where("customer_name ILIKE ? OR customer_phone ILIKE ? OR customer_email ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	var total int64
	if err := query.Model(&models.Reservation{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Order by reservation date and time
	query = query.Order("reservation_date ASC, reservation_time ASC")

	var reservations []models.Reservation
	if err := query.Find(&reservations).Error; err != nil {
		return nil, 0, err
	}

	return reservations, total, nil
}

func (r *reservationRepository) GetByBranchSlug(ctx context.Context, shopSlug, branchSlug string, filters types.ReservationFilters) ([]models.Reservation, int64, error) {
	query := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Joins("JOIN shop_branches ON reservations.branch_id = shop_branches.id").
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ? AND shop_branches.slug = ?", shopSlug, branchSlug)

	// Apply filters
	if filters.Status != "" {
		query = query.Where("reservations.status = ?", filters.Status)
	}
	if filters.Date != "" {
		query = query.Where("DATE(reservations.reservation_date) = ?", filters.Date)
	}
	if filters.StartDate != "" && filters.EndDate != "" {
		query = query.Where("DATE(reservations.reservation_date) BETWEEN ? AND ?", filters.StartDate, filters.EndDate)
	}
	if filters.TableID != nil {
		query = query.Where("reservations.table_id = ?", *filters.TableID)
	}
	if filters.Search != "" {
		query = query.Where("reservations.customer_name ILIKE ? OR reservations.customer_phone ILIKE ? OR reservations.customer_email ILIKE ?",
			"%"+filters.Search+"%", "%"+filters.Search+"%", "%"+filters.Search+"%")
	}

	// Count total
	var total int64
	if err := query.Model(&models.Reservation{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filters.Page - 1) * filters.Limit
	query = query.Offset(offset).Limit(filters.Limit)

	// Order by reservation date and time
	query = query.Order("reservations.reservation_date ASC, reservations.reservation_time ASC")

	var reservations []models.Reservation
	if err := query.Find(&reservations).Error; err != nil {
		return nil, 0, err
	}

	return reservations, total, nil
}

func (r *reservationRepository) Update(ctx context.Context, reservation *models.Reservation) error {
	return r.db.WithContext(ctx).Save(reservation).Error
}

func (r *reservationRepository) UpdateBySlug(ctx context.Context, slug string, reservation *models.Reservation) error {
	return r.db.WithContext(ctx).Model(&models.Reservation{}).Where("slug = ?", slug).Updates(reservation).Error
}

func (r *reservationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Delete(&models.Reservation{}, id).Error
}

func (r *reservationRepository) DeleteBySlug(ctx context.Context, slug string) error {
	return r.db.WithContext(ctx).Where("slug = ?", slug).Delete(&models.Reservation{}).Error
}

func (r *reservationRepository) GetTodayReservations(ctx context.Context, branchID uuid.UUID) ([]models.Reservation, error) {
	today := time.Now().Format("2006-01-02")

	var reservations []models.Reservation
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Where("branch_id = ? AND DATE(reservation_date) = ?", branchID, today).
		Order("reservation_time ASC").
		Find(&reservations).Error

	return reservations, err
}

func (r *reservationRepository) GetTodayReservationsBySlug(ctx context.Context, shopSlug, branchSlug string) ([]models.Reservation, error) {
	today := time.Now().Format("2006-01-02")

	var reservations []models.Reservation
	err := r.db.WithContext(ctx).
		Preload("Branch").
		Preload("Table").
		Joins("JOIN shop_branches ON reservations.branch_id = shop_branches.id").
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ? AND shop_branches.slug = ? AND DATE(reservations.reservation_date) = ?", shopSlug, branchSlug, today).
		Order("reservations.reservation_time ASC").
		Find(&reservations).Error

	return reservations, err
}

func (r *reservationRepository) GetAvailability(ctx context.Context, branchID uuid.UUID, date time.Time) ([]types.TimeSlot, error) {
	// This is a simplified implementation
	// In a real system, you'd calculate availability based on table capacity, existing reservations, etc.
	timeSlots := []types.TimeSlot{
		{Time: "11:00", Available: true},
		{Time: "11:30", Available: true},
		{Time: "12:00", Available: false},
		{Time: "12:30", Available: true},
		{Time: "13:00", Available: true},
		{Time: "13:30", Available: false},
		{Time: "14:00", Available: true},
		{Time: "18:00", Available: true},
		{Time: "18:30", Available: true},
		{Time: "19:00", Available: false},
		{Time: "19:30", Available: true},
		{Time: "20:00", Available: true},
		{Time: "20:30", Available: true},
		{Time: "21:00", Available: true},
	}

	return timeSlots, nil
}

func (r *reservationRepository) GetAvailabilityBySlug(ctx context.Context, shopSlug, branchSlug string, date time.Time) ([]types.TimeSlot, error) {
	// This is a simplified implementation
	// In a real system, you'd calculate availability based on table capacity, existing reservations, etc.
	timeSlots := []types.TimeSlot{
		{Time: "11:00", Available: true},
		{Time: "11:30", Available: true},
		{Time: "12:00", Available: false},
		{Time: "12:30", Available: true},
		{Time: "13:00", Available: true},
		{Time: "13:30", Available: false},
		{Time: "14:00", Available: true},
		{Time: "18:00", Available: true},
		{Time: "18:30", Available: true},
		{Time: "19:00", Available: false},
		{Time: "19:30", Available: true},
		{Time: "20:00", Available: true},
		{Time: "20:30", Available: true},
		{Time: "21:00", Available: true},
	}

	return timeSlots, nil
}

func (r *reservationRepository) GetReservationStats(ctx context.Context, branchID uuid.UUID, period string) (*types.ReservationStats, error) {
	var stats types.ReservationStats

	// Calculate date range based on period
	var dateFrom time.Time
	now := time.Now()

	switch period {
	case "7d":
		dateFrom = now.AddDate(0, 0, -7)
	case "30d":
		dateFrom = now.AddDate(0, 0, -30)
	case "90d":
		dateFrom = now.AddDate(0, 0, -90)
	case "1y":
		dateFrom = now.AddDate(-1, 0, 0)
	default:
		dateFrom = now.AddDate(0, 0, -30) // Default to 30 days
	}

	// Get total reservations count
	var totalReservations int64
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Where("branch_id = ? AND created_at >= ?", branchID, dateFrom).
		Count(&totalReservations).Error; err != nil {
		return nil, err
	}
	stats.TotalReservations = int(totalReservations)

	// Get today's reservations
	today := time.Now().Format("2006-01-02")
	var todayReservations int64
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Where("branch_id = ? AND DATE(reservation_date) = ?", branchID, today).
		Count(&todayReservations).Error; err != nil {
		return nil, err
	}
	stats.TodayReservations = int(todayReservations)

	// Get upcoming reservations (future dates)
	var upcomingReservations int64
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Where("branch_id = ? AND reservation_date > CURRENT_DATE", branchID).
		Count(&upcomingReservations).Error; err != nil {
		return nil, err
	}
	stats.UpcomingReservations = int(upcomingReservations)

	// Get completed reservations
	var completedReservations int64
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Where("branch_id = ? AND status = ? AND created_at >= ?", branchID, types.ReservationStatusCompleted, dateFrom).
		Count(&completedReservations).Error; err != nil {
		return nil, err
	}
	stats.CompletedReservations = int(completedReservations)

	// Get cancelled reservations
	var cancelledReservations int64
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Where("branch_id = ? AND status = ? AND created_at >= ?", branchID, types.ReservationStatusCancelled, dateFrom).
		Count(&cancelledReservations).Error; err != nil {
		return nil, err
	}
	stats.CancelledReservations = int(cancelledReservations)

	// Calculate no-show rate
	var noShowReservations int64
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Where("branch_id = ? AND status = ? AND created_at >= ?", branchID, types.ReservationStatusNoShow, dateFrom).
		Count(&noShowReservations).Error; err != nil {
		return nil, err
	}

	if totalReservations > 0 {
		stats.NoShowRate = float64(noShowReservations) / float64(totalReservations) * 100
	}

	// Get status counts
	type StatusCount struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	var statusCounts []StatusCount
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Select("status, COUNT(*) as count").
		Where("branch_id = ? AND created_at >= ?", branchID, dateFrom).
		Group("status").
		Scan(&statusCounts).Error; err != nil {
		return nil, err
	}

	stats.StatusCounts = make(map[string]int)
	for _, sc := range statusCounts {
		stats.StatusCounts[sc.Status] = int(sc.Count)
	}

	// Get monthly stats for the last 12 months
	type MonthlyCount struct {
		Month string `json:"month"`
		Count int64  `json:"count"`
	}

	var monthlyCounts []MonthlyCount
	if err := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Select("TO_CHAR(created_at, 'YYYY-MM') as month, COUNT(*) as count").
		Where("branch_id = ? AND created_at >= ?", branchID, now.AddDate(-1, 0, 0)).
		Group("TO_CHAR(created_at, 'YYYY-MM')").
		Order("month ASC").
		Scan(&monthlyCounts).Error; err != nil {
		return nil, err
	}

	stats.MonthlyStats = make([]types.MonthlyReservationStat, len(monthlyCounts))
	for i, mc := range monthlyCounts {
		stats.MonthlyStats[i] = types.MonthlyReservationStat{
			Month: mc.Month,
			Count: int(mc.Count),
		}
	}

	return &stats, nil
}

func (r *reservationRepository) GetReservationStatsBySlug(ctx context.Context, shopSlug, branchSlug string, period string) (*types.ReservationStats, error) {
	var stats types.ReservationStats

	// Calculate date range based on period
	var dateFrom time.Time
	now := time.Now()

	switch period {
	case "7d":
		dateFrom = now.AddDate(0, 0, -7)
	case "30d":
		dateFrom = now.AddDate(0, 0, -30)
	case "90d":
		dateFrom = now.AddDate(0, 0, -90)
	case "1y":
		dateFrom = now.AddDate(-1, 0, 0)
	default:
		dateFrom = now.AddDate(0, 0, -30) // Default to 30 days
	}

	// Base query with shop and branch slug joins
	baseQuery := r.db.WithContext(ctx).Model(&models.Reservation{}).
		Joins("JOIN shop_branches ON reservations.branch_id = shop_branches.id").
		Joins("JOIN shops ON shop_branches.shop_id = shops.id").
		Where("shops.slug = ? AND shop_branches.slug = ?", shopSlug, branchSlug)

	// Get total reservations count
	var totalReservations int64
	if err := baseQuery.Where("reservations.created_at >= ?", dateFrom).Count(&totalReservations).Error; err != nil {
		return nil, err
	}
	stats.TotalReservations = int(totalReservations)

	// Get today's reservations
	today := time.Now().Format("2006-01-02")
	var todayReservations int64
	if err := baseQuery.Where("DATE(reservations.reservation_date) = ?", today).Count(&todayReservations).Error; err != nil {
		return nil, err
	}
	stats.TodayReservations = int(todayReservations)

	// Get upcoming reservations (future dates)
	var upcomingReservations int64
	if err := baseQuery.Where("reservations.reservation_date > CURRENT_DATE").Count(&upcomingReservations).Error; err != nil {
		return nil, err
	}
	stats.UpcomingReservations = int(upcomingReservations)

	// Get completed reservations
	var completedReservations int64
	if err := baseQuery.Where("reservations.status = ? AND reservations.created_at >= ?", types.ReservationStatusCompleted, dateFrom).Count(&completedReservations).Error; err != nil {
		return nil, err
	}
	stats.CompletedReservations = int(completedReservations)

	// Get cancelled reservations
	var cancelledReservations int64
	if err := baseQuery.Where("reservations.status = ? AND reservations.created_at >= ?", types.ReservationStatusCancelled, dateFrom).Count(&cancelledReservations).Error; err != nil {
		return nil, err
	}
	stats.CancelledReservations = int(cancelledReservations)

	// Calculate no-show rate
	var noShowReservations int64
	if err := baseQuery.Where("reservations.status = ? AND reservations.created_at >= ?", types.ReservationStatusNoShow, dateFrom).Count(&noShowReservations).Error; err != nil {
		return nil, err
	}

	if totalReservations > 0 {
		stats.NoShowRate = float64(noShowReservations) / float64(totalReservations) * 100
	}

	// Get status counts
	type StatusCount struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	var statusCounts []StatusCount
	if err := baseQuery.Select("reservations.status, COUNT(*) as count").
		Where("reservations.created_at >= ?", dateFrom).
		Group("reservations.status").
		Scan(&statusCounts).Error; err != nil {
		return nil, err
	}

	stats.StatusCounts = make(map[string]int)
	for _, sc := range statusCounts {
		stats.StatusCounts[sc.Status] = int(sc.Count)
	}

	// Get monthly stats for the last 12 months
	type MonthlyCount struct {
		Month string `json:"month"`
		Count int64  `json:"count"`
	}

	var monthlyCounts []MonthlyCount
	if err := baseQuery.Select("TO_CHAR(reservations.created_at, 'YYYY-MM') as month, COUNT(*) as count").
		Where("reservations.created_at >= ?", now.AddDate(-1, 0, 0)).
		Group("TO_CHAR(reservations.created_at, 'YYYY-MM')").
		Order("month ASC").
		Scan(&monthlyCounts).Error; err != nil {
		return nil, err
	}

	stats.MonthlyStats = make([]types.MonthlyReservationStat, len(monthlyCounts))
	for i, mc := range monthlyCounts {
		stats.MonthlyStats[i] = types.MonthlyReservationStat{
			Month: mc.Month,
			Count: int(mc.Count),
		}
	}

	return &stats, nil
}
