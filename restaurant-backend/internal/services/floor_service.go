package services

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/types"
)

type FloorService struct {
	db *gorm.DB
}

func NewFloorService(db *gorm.DB) *FloorService {
	return &FloorService{
		db: db,
	}
}

// GetFloors retrieves all floors for a branch with pagination
func (s *FloorService) GetFloors(ctx context.Context, branchID uuid.UUID, filters types.FloorFilters) (*types.FloorsResponse, error) {
	var floors []models.Floor
	var total int64

	// Base query
	baseQuery := s.db.WithContext(ctx).Where("branch_id = ? AND is_active = ?", branchID, true)

	// Apply filters
	if filters.Name != "" {
		baseQuery = baseQuery.Where("name ILIKE ?", "%"+filters.Name+"%")
	}

	// Count total records
	if err := baseQuery.Model(&models.Floor{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count floors: %w", err)
	}

	// Apply pagination
	query := baseQuery
	if filters.Page > 0 && filters.Limit > 0 {
		offset := (filters.Page - 1) * filters.Limit
		query = query.Offset(offset).Limit(filters.Limit)
	}

	// Apply sorting
	sortBy := filters.SortBy
	if sortBy == "" {
		sortBy = "order"
	}
	sortOrder := filters.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}

	// Handle special case for "order" field (reserved keyword)
	if sortBy == "order" {
		query = query.Order(fmt.Sprintf("\"order\" %s, created_at ASC", sortOrder))
	} else {
		query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	}

	// Include areas if requested
	if filters.IncludeAreas {
		query = query.Preload("Areas", "is_active = ?", true)
	}

	if err := query.Find(&floors).Error; err != nil {
		return nil, fmt.Errorf("failed to get floors: %w", err)
	}

	// Convert to response format
	floorResponses := make([]types.FloorResponse, len(floors))
	for i, floor := range floors {
		floorResponses[i] = s.convertToFloorResponse(floor)
	}

	// Calculate pagination info
	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))
	if filters.Limit == 0 {
		totalPages = 1
	}

	return &types.FloorsResponse{
		StandardResponse: types.StandardResponse{
			Total:      total,
			Page:       filters.Page,
			Limit:      filters.Limit,
			TotalPages: totalPages,
		},
		Data: floorResponses,
	}, nil
}

// convertToFloorResponse converts a Floor model to FloorResponse
func (s *FloorService) convertToFloorResponse(floor models.Floor) types.FloorResponse {
	response := types.FloorResponse{
		ID:          floor.ID.String(),
		BranchID:    floor.BranchID.String(),
		Name:        floor.Name,
		Description: floor.Description,
		Order:       floor.Order,
		Layout:      floor.Layout,
		IsActive:    floor.IsActive,
		CreatedAt:   floor.CreatedAt.Format("2006-01-02T15:04:05Z07:00"),
		UpdatedAt:   floor.UpdatedAt.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Convert areas if present
	if len(floor.Areas) > 0 {
		areas := make([]types.AreaResponse, len(floor.Areas))
		for i, area := range floor.Areas {
			areas[i] = types.AreaResponse{
				ID:          area.ID,
				BranchID:    area.BranchID,
				FloorID:     area.FloorID,
				Name:        area.Name,
				Description: area.Description,
				Color:       area.Color,
				Position: types.PositionResponse{
					X:      0, // TableArea doesn't have position in model
					Y:      0,
					Width:  0,
					Height: 0,
					Angle:  0,
				},
				IsActive:   area.IsActive,
				TableCount: len(area.Tables), // Count tables in area
				CreatedAt:  area.CreatedAt,
				UpdatedAt:  area.UpdatedAt,
			}
		}
		response.Areas = areas
	}

	return response
}

// GetFloor retrieves a single floor by ID
func (s *FloorService) GetFloor(ctx context.Context, branchID, floorID uuid.UUID) (*models.Floor, error) {
	var floor models.Floor

	if err := s.db.WithContext(ctx).
		Where("id = ? AND branch_id = ? AND is_active = ?", floorID, branchID, true).
		Preload("Areas", "is_active = ?", true).
		First(&floor).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("floor not found")
		}
		return nil, fmt.Errorf("failed to get floor: %w", err)
	}

	return &floor, nil
}

// CreateFloor creates a new floor
func (s *FloorService) CreateFloor(ctx context.Context, branchID uuid.UUID, req types.CreateFloorRequest) (*models.Floor, error) {
	// Validate branch exists
	var branch models.ShopBranch
	if err := s.db.WithContext(ctx).Where("id = ?", branchID).First(&branch).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("branch not found")
		}
		return nil, fmt.Errorf("failed to validate branch: %w", err)
	}

	// Check if floor name already exists for this branch
	var existingFloor models.Floor
	if err := s.db.WithContext(ctx).
		Where("branch_id = ? AND name = ? AND is_active = ?", branchID, req.Name, true).
		First(&existingFloor).Error; err == nil {
		return nil, fmt.Errorf("floor with name '%s' already exists", req.Name)
	}

	// Set default layout if not provided
	layout := models.FloorLayout{
		Width:    800,
		Height:   600,
		GridSize: 20,
		ShowGrid: true,
	}
	if req.Layout != nil {
		layout = *req.Layout
	}

	// Create floor
	floor := models.Floor{
		BranchID:    branchID,
		Name:        req.Name,
		Description: req.Description,
		Order:       req.Order,
		Layout:      layout,
		IsActive:    true,
	}

	if err := s.db.WithContext(ctx).Create(&floor).Error; err != nil {
		return nil, fmt.Errorf("failed to create floor: %w", err)
	}

	return &floor, nil
}

// UpdateFloor updates an existing floor
func (s *FloorService) UpdateFloor(ctx context.Context, branchID, floorID uuid.UUID, req types.UpdateFloorRequest) (*models.Floor, error) {
	var floor models.Floor

	// Get existing floor
	if err := s.db.WithContext(ctx).
		Where("id = ? AND branch_id = ? AND is_active = ?", floorID, branchID, true).
		First(&floor).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("floor not found")
		}
		return nil, fmt.Errorf("failed to get floor: %w", err)
	}

	// Check if new name conflicts with existing floors
	if req.Name != "" && req.Name != floor.Name {
		var existingFloor models.Floor
		if err := s.db.WithContext(ctx).
			Where("branch_id = ? AND name = ? AND id != ? AND is_active = ?", branchID, req.Name, floorID, true).
			First(&existingFloor).Error; err == nil {
			return nil, fmt.Errorf("floor with name '%s' already exists", req.Name)
		}
	}

	// Update fields
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.Order != 0 {
		updates["order"] = req.Order
	}
	if req.Layout != nil {
		updates["layout"] = *req.Layout
	}

	if err := s.db.WithContext(ctx).Model(&floor).Updates(updates).Error; err != nil {
		return nil, fmt.Errorf("failed to update floor: %w", err)
	}

	// Reload floor with relationships
	if err := s.db.WithContext(ctx).
		Where("id = ?", floorID).
		Preload("Areas", "is_active = ?", true).
		First(&floor).Error; err != nil {
		return nil, fmt.Errorf("failed to reload floor: %w", err)
	}

	return &floor, nil
}

// DeleteFloor soft deletes a floor
func (s *FloorService) DeleteFloor(ctx context.Context, branchID, floorID uuid.UUID) error {
	var floor models.Floor

	// Get existing floor
	if err := s.db.WithContext(ctx).
		Where("id = ? AND branch_id = ? AND is_active = ?", floorID, branchID, true).
		First(&floor).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("floor not found")
		}
		return fmt.Errorf("failed to get floor: %w", err)
	}

	// Check if floor has active areas
	var areaCount int64
	if err := s.db.WithContext(ctx).
		Model(&models.TableArea{}).
		Where("floor_id = ? AND is_active = ?", floorID, true).
		Count(&areaCount).Error; err != nil {
		return fmt.Errorf("failed to check areas: %w", err)
	}

	if areaCount > 0 {
		return fmt.Errorf("cannot delete floor with active areas")
	}

	// Soft delete floor
	if err := s.db.WithContext(ctx).Model(&floor).Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to delete floor: %w", err)
	}

	return nil
}
