package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"restaurant-backend/internal/models"
)

// NotificationService handles notification business logic
type NotificationService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewNotificationService creates a new notification service
func NewNotificationService(db *gorm.DB, logger *logrus.Logger) *NotificationService {
	return &NotificationService{
		db:     db,
		logger: logger,
	}
}

// GetNotifications retrieves notifications with filtering, sorting, and pagination
func (s *NotificationService) GetNotifications(ctx context.Context, shopID, branchID uuid.UUID, filters models.NotificationFilters, page, limit int, sortBy, sortOrder string) (*models.PaginatedResponse[models.Notification], error) {
	var notifications []models.Notification
	var total int64

	// Build base query
	query := s.db.WithContext(ctx).Model(&models.Notification{}).
		Where("shop_id = ? AND branch_id = ?", shopID, branchID)

	// Apply filters
	if filters.Type != nil {
		query = query.Where("type = ?", *filters.Type)
	}
	if filters.Priority != nil {
		query = query.Where("priority = ?", *filters.Priority)
	}
	if filters.IsRead != nil {
		query = query.Where("is_read = ?", *filters.IsRead)
	}
	if filters.Search != nil && *filters.Search != "" {
		searchTerm := "%" + strings.ToLower(*filters.Search) + "%"
		query = query.Where("LOWER(title) LIKE ? OR LOWER(message) LIKE ?", searchTerm, searchTerm)
	}

	// Apply date filters
	if filters.StartDate != nil {
		query = query.Where("timestamp >= ?", *filters.StartDate)
	}
	if filters.EndDate != nil {
		query = query.Where("timestamp <= ?", *filters.EndDate)
	}
	if filters.DateRange != nil {
		now := time.Now()
		switch *filters.DateRange {
		case "today":
			startOfDay := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
			query = query.Where("timestamp >= ?", startOfDay)
		case "yesterday":
			yesterday := now.AddDate(0, 0, -1)
			startOfYesterday := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 0, 0, 0, 0, yesterday.Location())
			endOfYesterday := startOfYesterday.Add(24 * time.Hour)
			query = query.Where("timestamp >= ? AND timestamp < ?", startOfYesterday, endOfYesterday)
		case "last_7_days":
			sevenDaysAgo := now.AddDate(0, 0, -7)
			query = query.Where("timestamp >= ?", sevenDaysAgo)
		case "last_30_days":
			thirtyDaysAgo := now.AddDate(0, 0, -30)
			query = query.Where("timestamp >= ?", thirtyDaysAgo)
		}
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		s.logger.WithError(err).Error("Failed to count notifications")
		return nil, fmt.Errorf("failed to count notifications: %w", err)
	}

	// Apply sorting
	orderClause := "timestamp DESC" // default
	if sortBy != "" {
		validSortFields := map[string]bool{
			"timestamp": true,
			"priority":  true,
			"type":      true,
			"is_read":   true,
		}
		if validSortFields[sortBy] {
			if sortOrder == "asc" {
				orderClause = fmt.Sprintf("%s ASC", sortBy)
			} else {
				orderClause = fmt.Sprintf("%s DESC", sortBy)
			}
		}
	}

	// Apply pagination and get results
	offset := (page - 1) * limit
	if err := query.Order(orderClause).Offset(offset).Limit(limit).Find(&notifications).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get notifications")
		return nil, fmt.Errorf("failed to get notifications: %w", err)
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	return &models.PaginatedResponse[models.Notification]{
		Data:       notifications,
		Total:      int(total),
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}, nil
}

// GetNotificationByID retrieves a notification by ID
func (s *NotificationService) GetNotificationByID(ctx context.Context, shopID, branchID, notificationID uuid.UUID) (*models.Notification, error) {
	var notification models.Notification

	if err := s.db.WithContext(ctx).
		Where("id = ? AND shop_id = ? AND branch_id = ?", notificationID, shopID, branchID).
		First(&notification).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("notification not found")
		}
		s.logger.WithError(err).Error("Failed to get notification by ID")
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	return &notification, nil
}

// CreateNotification creates a new notification
func (s *NotificationService) CreateNotification(ctx context.Context, shopID, branchID uuid.UUID, req models.NotificationRequest) (*models.Notification, error) {
	notification := models.Notification{
		ShopID:      shopID,
		BranchID:    branchID,
		UserID:      req.UserID,
		Title:       req.Title,
		Message:     req.Message,
		Type:        req.Type,
		Priority:    "medium", // default
		Link:        req.Link,
		ActionLabel: req.ActionLabel,
		Data:        req.Data,
		Timestamp:   time.Now(),
		IsRead:      false,
	}

	// Set priority if provided
	if req.Priority != nil {
		notification.Priority = *req.Priority
	}

	// Validate type and priority
	if !notification.ValidateType() {
		return nil, fmt.Errorf("invalid notification type: %s", notification.Type)
	}
	if !notification.ValidatePriority() {
		return nil, fmt.Errorf("invalid notification priority: %s", notification.Priority)
	}

	if err := s.db.WithContext(ctx).Create(&notification).Error; err != nil {
		s.logger.WithError(err).Error("Failed to create notification")
		return nil, fmt.Errorf("failed to create notification: %w", err)
	}

	return &notification, nil
}

// UpdateNotification updates a notification
func (s *NotificationService) UpdateNotification(ctx context.Context, shopID, branchID, notificationID uuid.UUID, updates models.NotificationUpdateRequest) (*models.Notification, error) {
	var notification models.Notification

	// First, get the notification to ensure it exists and belongs to the shop/branch
	if err := s.db.WithContext(ctx).
		Where("id = ? AND shop_id = ? AND branch_id = ?", notificationID, shopID, branchID).
		First(&notification).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("notification not found")
		}
		s.logger.WithError(err).Error("Failed to get notification for update")
		return nil, fmt.Errorf("failed to get notification: %w", err)
	}

	// Apply updates
	updateData := make(map[string]interface{})
	if updates.IsRead != nil {
		updateData["is_read"] = *updates.IsRead
	}

	if len(updateData) > 0 {
		if err := s.db.WithContext(ctx).Model(&notification).Updates(updateData).Error; err != nil {
			s.logger.WithError(err).Error("Failed to update notification")
			return nil, fmt.Errorf("failed to update notification: %w", err)
		}
	}

	return &notification, nil
}

// BulkUpdateNotifications updates multiple notifications
func (s *NotificationService) BulkUpdateNotifications(ctx context.Context, shopID, branchID uuid.UUID, req models.BulkNotificationUpdateRequest) error {
	updateData := make(map[string]interface{})
	if req.Updates.IsRead != nil {
		updateData["is_read"] = *req.Updates.IsRead
	}

	if len(updateData) > 0 {
		if err := s.db.WithContext(ctx).Model(&models.Notification{}).
			Where("id IN ? AND shop_id = ? AND branch_id = ?", req.NotificationIDs, shopID, branchID).
			Updates(updateData).Error; err != nil {
			s.logger.WithError(err).Error("Failed to bulk update notifications")
			return fmt.Errorf("failed to bulk update notifications: %w", err)
		}
	}

	return nil
}

// DeleteNotification deletes a notification
func (s *NotificationService) DeleteNotification(ctx context.Context, shopID, branchID, notificationID uuid.UUID) error {
	result := s.db.WithContext(ctx).
		Where("id = ? AND shop_id = ? AND branch_id = ?", notificationID, shopID, branchID).
		Delete(&models.Notification{})

	if result.Error != nil {
		s.logger.WithError(result.Error).Error("Failed to delete notification")
		return fmt.Errorf("failed to delete notification: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("notification not found")
	}

	return nil
}

// MarkAllAsRead marks all notifications as read for a shop/branch
func (s *NotificationService) MarkAllAsRead(ctx context.Context, shopID, branchID uuid.UUID) error {
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Where("shop_id = ? AND branch_id = ? AND is_read = false", shopID, branchID).
		Update("is_read", true).Error; err != nil {
		s.logger.WithError(err).Error("Failed to mark all notifications as read")
		return fmt.Errorf("failed to mark all notifications as read: %w", err)
	}

	return nil
}

// ClearAllNotifications deletes all notifications for a shop/branch
func (s *NotificationService) ClearAllNotifications(ctx context.Context, shopID, branchID uuid.UUID) error {
	if err := s.db.WithContext(ctx).
		Where("shop_id = ? AND branch_id = ?", shopID, branchID).
		Delete(&models.Notification{}).Error; err != nil {
		s.logger.WithError(err).Error("Failed to clear all notifications")
		return fmt.Errorf("failed to clear all notifications: %w", err)
	}

	return nil
}

// GetNotificationStats retrieves notification statistics for a shop/branch
func (s *NotificationService) GetNotificationStats(ctx context.Context, shopID, branchID uuid.UUID) (*models.NotificationStats, error) {
	stats := &models.NotificationStats{
		ByType:     make(map[string]int),
		ByPriority: make(map[string]int),
	}

	// Get total count
	var totalCount int64
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Where("shop_id = ? AND branch_id = ?", shopID, branchID).
		Count(&totalCount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get total notification count")
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}
	stats.TotalNotifications = int(totalCount)

	// Get unread count
	var unreadCount int64
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Where("shop_id = ? AND branch_id = ? AND is_read = false", shopID, branchID).
		Count(&unreadCount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get unread notification count")
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}
	stats.UnreadNotifications = int(unreadCount)

	// Calculate read count
	stats.ReadNotifications = stats.TotalNotifications - stats.UnreadNotifications

	// Get urgent count
	var urgentCount int64
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Where("shop_id = ? AND branch_id = ? AND priority = 'urgent'", shopID, branchID).
		Count(&urgentCount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get urgent notification count")
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}
	stats.UrgentNotifications = int(urgentCount)

	// Get high priority count (high + urgent)
	var highPriorityCount int64
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Where("shop_id = ? AND branch_id = ? AND priority IN ('high', 'urgent')", shopID, branchID).
		Count(&highPriorityCount).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get high priority notification count")
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}
	stats.HighPriorityNotifications = int(highPriorityCount)

	// Get counts by type
	var typeResults []struct {
		Type  string `json:"type"`
		Count int    `json:"count"`
	}
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Select("type, COUNT(*) as count").
		Where("shop_id = ? AND branch_id = ?", shopID, branchID).
		Group("type").
		Scan(&typeResults).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get notification counts by type")
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}
	for _, result := range typeResults {
		stats.ByType[result.Type] = result.Count
	}

	// Get counts by priority
	var priorityResults []struct {
		Priority string `json:"priority"`
		Count    int    `json:"count"`
	}
	if err := s.db.WithContext(ctx).Model(&models.Notification{}).
		Select("priority, COUNT(*) as count").
		Where("shop_id = ? AND branch_id = ?", shopID, branchID).
		Group("priority").
		Scan(&priorityResults).Error; err != nil {
		s.logger.WithError(err).Error("Failed to get notification counts by priority")
		return nil, fmt.Errorf("failed to get notification stats: %w", err)
	}
	for _, result := range priorityResults {
		stats.ByPriority[result.Priority] = result.Count
	}

	return stats, nil
}

// Slug-based methods that resolve shop and branch IDs from slugs

// GetNotificationsBySlug retrieves notifications using shop and branch slugs
func (s *NotificationService) GetNotificationsBySlug(ctx context.Context, shopSlug, branchSlug string, filters models.NotificationFilters, page, limit int, sortBy, sortOrder string) (*models.PaginatedResponse[models.Notification], error) {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return nil, err
	}

	// Call the existing method with IDs
	return s.GetNotifications(ctx, shopID, branchID, filters, page, limit, sortBy, sortOrder)
}

// GetNotificationByIDAndSlug retrieves a notification by ID using shop and branch slugs
func (s *NotificationService) GetNotificationByIDAndSlug(ctx context.Context, shopSlug, branchSlug string, notificationID uuid.UUID) (*models.Notification, error) {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return nil, err
	}

	// Call the existing method with IDs
	return s.GetNotificationByID(ctx, shopID, branchID, notificationID)
}

// CreateNotificationBySlug creates a notification using shop and branch slugs
func (s *NotificationService) CreateNotificationBySlug(ctx context.Context, shopSlug, branchSlug string, req models.NotificationRequest) (*models.Notification, error) {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return nil, err
	}

	// Call the existing method with IDs
	return s.CreateNotification(ctx, shopID, branchID, req)
}

// UpdateNotificationBySlug updates a notification using shop and branch slugs
func (s *NotificationService) UpdateNotificationBySlug(ctx context.Context, shopSlug, branchSlug string, notificationID uuid.UUID, updates models.NotificationUpdateRequest) (*models.Notification, error) {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return nil, err
	}

	// Call the existing method with IDs
	return s.UpdateNotification(ctx, shopID, branchID, notificationID, updates)
}

// BulkUpdateNotificationsBySlug updates multiple notifications using shop and branch slugs
func (s *NotificationService) BulkUpdateNotificationsBySlug(ctx context.Context, shopSlug, branchSlug string, req models.BulkNotificationUpdateRequest) error {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return err
	}

	// Call the existing method with IDs
	return s.BulkUpdateNotifications(ctx, shopID, branchID, req)
}

// DeleteNotificationBySlug deletes a notification using shop and branch slugs
func (s *NotificationService) DeleteNotificationBySlug(ctx context.Context, shopSlug, branchSlug string, notificationID uuid.UUID) error {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return err
	}

	// Call the existing method with IDs
	return s.DeleteNotification(ctx, shopID, branchID, notificationID)
}

// MarkAllAsReadBySlug marks all notifications as read using shop and branch slugs
func (s *NotificationService) MarkAllAsReadBySlug(ctx context.Context, shopSlug, branchSlug string) error {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return err
	}

	// Call the existing method with IDs
	return s.MarkAllAsRead(ctx, shopID, branchID)
}

// ClearAllNotificationsBySlug deletes all notifications using shop and branch slugs
func (s *NotificationService) ClearAllNotificationsBySlug(ctx context.Context, shopSlug, branchSlug string) error {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return err
	}

	// Call the existing method with IDs
	return s.ClearAllNotifications(ctx, shopID, branchID)
}

// GetNotificationStatsBySlug retrieves notification statistics using shop and branch slugs
func (s *NotificationService) GetNotificationStatsBySlug(ctx context.Context, shopSlug, branchSlug string) (*models.NotificationStats, error) {
	// Get shop and branch IDs from slugs
	shopID, branchID, err := s.getShopAndBranchIDsFromSlugs(ctx, shopSlug, branchSlug)
	if err != nil {
		return nil, err
	}

	// Call the existing method with IDs
	return s.GetNotificationStats(ctx, shopID, branchID)
}

// Helper method to get shop and branch IDs from slugs
func (s *NotificationService) getShopAndBranchIDsFromSlugs(ctx context.Context, shopSlug, branchSlug string) (uuid.UUID, uuid.UUID, error) {
	var shop models.Shop
	var branch models.ShopBranch

	// Get shop by slug
	if err := s.db.WithContext(ctx).Where("slug = ?", shopSlug).First(&shop).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.WithField("shopSlug", shopSlug).Error("Shop not found by slug")
			return uuid.Nil, uuid.Nil, fmt.Errorf("shop not found")
		}
		s.logger.WithError(err).WithField("shopSlug", shopSlug).Error("Failed to get shop by slug")
		return uuid.Nil, uuid.Nil, fmt.Errorf("failed to get shop: %w", err)
	}

	// Get branch by slug within the shop
	if err := s.db.WithContext(ctx).
		Where("slug = ? AND shop_id = ?", branchSlug, shop.ID).
		First(&branch).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			s.logger.WithFields(logrus.Fields{
				"shopSlug":   shopSlug,
				"branchSlug": branchSlug,
			}).Error("Branch not found by slug")
			return uuid.Nil, uuid.Nil, fmt.Errorf("branch not found")
		}
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shopSlug":   shopSlug,
			"branchSlug": branchSlug,
		}).Error("Failed to get branch by slug")
		return uuid.Nil, uuid.Nil, fmt.Errorf("failed to get branch: %w", err)
	}

	return shop.ID, branch.ID, nil
}
