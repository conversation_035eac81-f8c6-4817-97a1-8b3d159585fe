package services

import (
	"context"
	"fmt"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// OrderService handles order business logic
type OrderService struct {
	orderRepo    repositories.OrderRepository
	menuItemRepo repositories.MenuItemRepository
	tableRepo    repositories.TableRepository
	shopRepo     *repositories.ShopRepository
	logger       *logrus.Logger
}

func NewOrderService(
	orderRepo repositories.OrderRepository,
	menuItemRepo repositories.MenuItemRepository,
	tableRepo repositories.TableRepository,
	shopRepo *repositories.ShopRepository,
	logger *logrus.Logger,
) *OrderService {
	return &OrderService{
		orderRepo:    orderRepo,
		menuItemRepo: menuItemRepo,
		tableRepo:    tableRepo,
		shopRepo:     shopRepo,
		logger:       logger,
	}
}

// Order methods
func (s *OrderService) GetOrders(ctx context.Context, branchID uuid.UUID, filters types.OrderFilters) (*types.OrdersResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	// Set default sorting
	if filters.SortBy == "" {
		filters.SortBy = "created_at"
	}
	if filters.SortOrder == "" {
		filters.SortOrder = "desc"
	}

	orders, total, err := s.orderRepo.GetByBranchID(ctx, branchID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	// Convert to response format
	orderResponses := make([]types.OrderResponse, len(orders))
	for i, order := range orders {
		orderResponses[i] = s.convertOrderToResponse(order)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.OrdersResponse{
		Data:       orderResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *OrderService) GetOrderByID(ctx context.Context, branchID, orderID uuid.UUID) (*types.OrderResponse, error) {
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	// Verify order belongs to branch
	if order.BranchID != branchID {
		return nil, fmt.Errorf("order not found")
	}

	response := s.convertOrderToResponse(*order)
	return &response, nil
}

func (s *OrderService) CreateOrder(ctx context.Context, branchID uuid.UUID, req types.CreateOrderRequest) (*types.OrderResponse, error) {
	// Validate table if provided
	if req.TableID != nil {
		table, err := s.tableRepo.GetByID(ctx, *req.TableID)
		if err != nil || table.BranchID != branchID {
			return nil, fmt.Errorf("invalid table")
		}
	}

	// Validate menu items and calculate totals
	var orderItems []models.OrderItem
	var subtotal float64

	for _, itemReq := range req.Items {
		menuItem, err := s.menuItemRepo.GetByID(ctx, itemReq.MenuItemID)
		if err != nil || menuItem.BranchID != branchID {
			return nil, fmt.Errorf("invalid menu item: %s", itemReq.MenuItemID)
		}

		if !menuItem.IsAvailable {
			return nil, fmt.Errorf("menu item %s is not available", menuItem.Name)
		}

		// Calculate item total with options
		itemTotal := itemReq.Price * float64(itemReq.Quantity)
		for _, option := range itemReq.Options {
			itemTotal += option.PriceAdjust * float64(itemReq.Quantity)
		}

		orderItem := models.OrderItem{
			MenuItemID:    &itemReq.MenuItemID,
			Name:          menuItem.Name,
			Price:         itemReq.Price,
			Quantity:      itemReq.Quantity,
			Modifications: s.convertOrderItemOptionsToModel(itemReq.Options),
			Notes:         itemReq.Notes,
			Total:         itemTotal,
			Status:        types.OrderStatusPending,
		}

		orderItems = append(orderItems, orderItem)
		subtotal += itemTotal
	}

	// Calculate tax and total
	taxRate := 0.1 // 10% tax rate - should be configurable
	taxAmount := subtotal * taxRate
	totalAmount := subtotal + taxAmount

	// Generate order number
	orderNumber := s.generateOrderNumber()

	// Create order
	order := &models.Order{
		BranchID:      branchID,
		TableID:       req.TableID,
		OrderNumber:   orderNumber,
		CustomerName:  req.CustomerName,
		CustomerPhone: req.CustomerPhone,
		CustomerEmail: req.CustomerEmail,
		Type:          req.OrderType,
		Status:        types.OrderStatusPending,
		Items:         orderItems,
		Subtotal:      subtotal,
		Tax:           taxAmount,
		Total:         totalAmount,
		Notes:         req.Notes,
		PaymentStatus: types.PaymentStatusPending,
	}

	if err := s.orderRepo.Create(ctx, order); err != nil {
		s.logger.WithError(err).Error("Failed to create order")
		return nil, fmt.Errorf("failed to create order: %w", err)
	}

	// Reload order with relationships
	createdOrder, err := s.orderRepo.GetByID(ctx, order.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get created order")
		return nil, fmt.Errorf("failed to get created order")
	}

	response := s.convertOrderToResponse(*createdOrder)
	return &response, nil
}

func (s *OrderService) UpdateOrder(ctx context.Context, branchID, orderID uuid.UUID, req types.UpdateOrderRequest) (*types.OrderResponse, error) {
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	// Verify order belongs to branch
	if order.BranchID != branchID {
		return nil, fmt.Errorf("order not found")
	}

	// Update fields
	if req.Status != nil {
		order.Status = *req.Status
		if *req.Status == types.OrderStatusCompleted {
			now := time.Now()
			order.CompletedAt = &now
		}
	}

	if req.Notes != nil {
		order.Notes = *req.Notes
	}

	if req.EstimatedTime != nil {
		order.EstimatedTime = req.EstimatedTime
	}

	// Update items if provided
	if len(req.Items) > 0 {
		// Recalculate totals
		var orderItems []models.OrderItem
		var subtotal float64

		for _, itemReq := range req.Items {
			menuItem, err := s.menuItemRepo.GetByID(ctx, itemReq.MenuItemID)
			if err != nil || menuItem.BranchID != branchID {
				return nil, fmt.Errorf("invalid menu item: %s", itemReq.MenuItemID)
			}

			// Calculate item total with options
			itemTotal := itemReq.Price * float64(itemReq.Quantity)
			for _, option := range itemReq.Options {
				itemTotal += option.PriceAdjust * float64(itemReq.Quantity)
			}

			orderItem := models.OrderItem{
				MenuItemID:    &itemReq.MenuItemID,
				Name:          menuItem.Name,
				Price:         itemReq.Price,
				Quantity:      itemReq.Quantity,
				Modifications: s.convertOrderItemOptionsToModel(itemReq.Options),
				Notes:         itemReq.Notes,
				Total:         itemTotal,
				Status:        order.Status,
			}

			orderItems = append(orderItems, orderItem)
			subtotal += itemTotal
		}

		// Update totals
		taxRate := 0.1 // 10% tax rate
		taxAmount := subtotal * taxRate
		totalAmount := subtotal + taxAmount

		order.Items = orderItems
		order.Subtotal = subtotal
		order.Tax = taxAmount
		order.Total = totalAmount
	}

	if err := s.orderRepo.Update(ctx, order); err != nil {
		s.logger.WithError(err).Error("Failed to update order")
		return nil, fmt.Errorf("failed to update order: %w", err)
	}

	response := s.convertOrderToResponse(*order)
	return &response, nil
}

func (s *OrderService) UpdateOrderStatus(ctx context.Context, branchID, orderID uuid.UUID, req types.UpdateOrderStatusRequest) (*types.OrderResponse, error) {
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	// Verify order belongs to branch
	if order.BranchID != branchID {
		return nil, fmt.Errorf("order not found")
	}

	// Update status
	order.Status = req.Status
	if req.EstimatedTime != nil {
		order.EstimatedTime = req.EstimatedTime
	}
	if req.Notes != "" {
		order.Notes = req.Notes
	}

	if req.Status == types.OrderStatusCompleted {
		now := time.Now()
		order.CompletedAt = &now
	}

	if err := s.orderRepo.Update(ctx, order); err != nil {
		s.logger.WithError(err).Error("Failed to update order status")
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}

	response := s.convertOrderToResponse(*order)
	return &response, nil
}

func (s *OrderService) DeleteOrder(ctx context.Context, branchID, orderID uuid.UUID) error {
	order, err := s.orderRepo.GetByID(ctx, orderID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order")
		return fmt.Errorf("failed to get order: %w", err)
	}

	// Verify order belongs to branch
	if order.BranchID != branchID {
		return fmt.Errorf("order not found")
	}

	// Only allow deletion of pending or cancelled orders
	if order.Status != types.OrderStatusPending && order.Status != types.OrderStatusCancelled {
		return fmt.Errorf("cannot delete order with status: %s", order.Status)
	}

	if err := s.orderRepo.Delete(ctx, orderID); err != nil {
		s.logger.WithError(err).Error("Failed to delete order")
		return fmt.Errorf("failed to delete order: %w", err)
	}

	return nil
}

// Analytics methods
func (s *OrderService) GetActiveOrders(ctx context.Context, branchID uuid.UUID) ([]types.OrderResponse, error) {
	orders, err := s.orderRepo.GetActiveOrders(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get active orders")
		return nil, fmt.Errorf("failed to get active orders: %w", err)
	}

	responses := make([]types.OrderResponse, len(orders))
	for i, order := range orders {
		responses[i] = s.convertOrderToResponse(order)
	}

	return responses, nil
}

func (s *OrderService) GetOrderStats(ctx context.Context, branchID uuid.UUID, dateFrom, dateTo time.Time) (*types.OrderStatsResponse, error) {
	stats, err := s.orderRepo.GetOrderStats(ctx, branchID, dateFrom, dateTo)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order stats")
		return nil, fmt.Errorf("failed to get order stats: %w", err)
	}

	return stats, nil
}

func (s *OrderService) GetDashboardStats(ctx context.Context, branchID uuid.UUID) (*types.DashboardStatsResponse, error) {
	stats, err := s.orderRepo.GetDashboardStats(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get dashboard stats")
		return nil, fmt.Errorf("failed to get dashboard stats: %w", err)
	}

	return stats, nil
}

func (s *OrderService) GetRecentActivity(ctx context.Context, branchID uuid.UUID, limit int) (*types.RecentActivityResponse, error) {
	if limit == 0 {
		limit = 10
	}

	orders, err := s.orderRepo.GetRecentActivity(ctx, branchID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get recent activity")
		return nil, fmt.Errorf("failed to get recent activity: %w", err)
	}

	responses := make([]types.OrderResponse, len(orders))
	for i, order := range orders {
		responses[i] = s.convertOrderToResponse(order)
	}

	return &types.RecentActivityResponse{
		Orders: responses,
		Limit:  limit,
	}, nil
}

// Helper methods
func (s *OrderService) generateOrderNumber() string {
	// Simple order number generation - in production, this should be more sophisticated
	timestamp := time.Now().Unix()
	return fmt.Sprintf("ORD-%d", timestamp)
}

func (s *OrderService) convertOrderItemOptionsToModel(options []types.OrderItemOptionRequest) models.ModificationsData {
	modelOptions := make([]models.OrderModification, len(options))
	for i, option := range options {
		modelOptions[i] = models.OrderModification{
			Name:  option.OptionName + ": " + option.ChoiceName,
			Price: option.PriceAdjust,
		}
	}
	return models.ModificationsData(modelOptions)
}

func (s *OrderService) convertOrderToResponse(order models.Order) types.OrderResponse {
	var tableResponse *types.TableSummary
	if order.Table != nil {
		tableResponse = &types.TableSummary{
			ID:     order.Table.ID,
			Number: convertIntToString(order.Table.Number),
			Name:   order.Table.Name,
		}
	}

	// Delivery address is not supported in the current model
	var deliveryAddress *types.DeliveryAddressResponse

	items := make([]types.OrderItemResponse, len(order.Items))
	for i, item := range order.Items {
		var menuItemSummary *types.MenuItemSummary
		if item.MenuItem != nil {
			menuItemSummary = &types.MenuItemSummary{
				ID:          item.MenuItem.ID,
				Name:        item.MenuItem.Name,
				Description: item.MenuItem.Description,
				Price:       item.MenuItem.Price,
				ImageURL:    "", // ImageURL not available in current model
			}
		}

		// Convert modifications to options
		options := make([]types.OrderItemOptionResponse, len(item.Modifications))
		for j, mod := range item.Modifications {
			options[j] = types.OrderItemOptionResponse{
				OptionName:  mod.Name,
				ChoiceName:  "",
				PriceAdjust: mod.Price,
			}
		}

		var menuItemID uuid.UUID
		if item.MenuItemID != nil {
			menuItemID = *item.MenuItemID
		}

		items[i] = types.OrderItemResponse{
			ID:         item.ID,
			MenuItemID: menuItemID,
			MenuItem:   menuItemSummary,
			Quantity:   item.Quantity,
			UnitPrice:  item.Price,
			TotalPrice: item.Total,
			Notes:      item.Notes,
			Options:    options,
			Status:     item.Status,
			CreatedAt:  item.CreatedAt,
		}
	}

	return types.OrderResponse{
		ID:              order.ID,
		BranchID:        order.BranchID,
		TableID:         order.TableID,
		Table:           tableResponse,
		OrderNumber:     order.OrderNumber,
		CustomerName:    order.CustomerName,
		CustomerPhone:   order.CustomerPhone,
		CustomerEmail:   order.CustomerEmail,
		OrderType:       order.Type,
		Status:          order.Status,
		Items:           items,
		Subtotal:        order.Subtotal,
		TaxAmount:       order.Tax,
		TipAmount:       order.Tip,
		TotalAmount:     order.Total,
		Notes:           order.Notes,
		DeliveryAddress: deliveryAddress,
		EstimatedTime:   order.EstimatedTime,
		ScheduledFor:    nil, // Not supported in current model
		PaymentStatus:   order.PaymentStatus,
		PaymentMethod:   order.PaymentMethod,
		CreatedAt:       order.CreatedAt,
		UpdatedAt:       order.UpdatedAt,
		CompletedAt:     order.CompletedAt,
	}
}

// Slug-based methods for shop API
func (s *OrderService) GetDashboardStatsBySlug(ctx context.Context, shopSlug, branchSlug string) (*types.FrontendDashboardStatsResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Get today's stats
	todayStats, err := s.GetDashboardStats(ctx, branch.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get dashboard stats")
		return nil, fmt.Errorf("failed to get dashboard stats: %w", err)
	}

	// Get yesterday's stats for growth calculation
	yesterday := time.Now().AddDate(0, 0, -1).Truncate(24 * time.Hour)
	tomorrow := yesterday.Add(24 * time.Hour)
	yesterdayStats, err := s.orderRepo.GetOrderStats(ctx, branch.ID, yesterday, tomorrow)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get yesterday's stats for growth calculation")
		// Continue without growth data
		yesterdayStats = &types.OrderStatsResponse{}
	}

	// Calculate growth percentages
	ordersGrowth := s.calculateGrowthPercentage(float64(todayStats.TodayOrders), float64(yesterdayStats.TotalOrders))
	revenueGrowth := s.calculateGrowthPercentage(todayStats.TodayRevenue, yesterdayStats.TotalRevenue)

	// For now, use placeholder values for reservations and staff
	// In a full implementation, these would come from reservation and staff services
	todayReservations := int64(0) // TODO: Get from reservation service
	activeStaff := int64(0)       // TODO: Get from staff/user service
	reservationsGrowth := 0.0     // TODO: Calculate from reservation data

	return &types.FrontendDashboardStatsResponse{
		TodayOrders:        todayStats.TodayOrders,
		TodayRevenue:       todayStats.TodayRevenue,
		TodayReservations:  todayReservations,
		ActiveStaff:        activeStaff,
		OrdersGrowth:       ordersGrowth,
		RevenueGrowth:      revenueGrowth,
		ReservationsGrowth: reservationsGrowth,
	}, nil
}

func (s *OrderService) GetRealTimeMetricsBySlug(ctx context.Context, shopSlug, branchSlug string) (*types.RealTimeMetricsResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Get real-time metrics for the branch
	// For now, we'll use the dashboard stats and format them as real-time metrics
	// In a full implementation, this would query different metrics
	dashboardStats, err := s.GetDashboardStats(ctx, branch.ID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get dashboard stats for real-time metrics")
		return nil, fmt.Errorf("failed to get real-time metrics: %w", err)
	}

	// Convert dashboard stats to real-time metrics format
	return &types.RealTimeMetricsResponse{
		ActiveOrders:    dashboardStats.ActiveOrders,
		TodayRevenue:    dashboardStats.TodayRevenue,
		OnlineCustomers: 0, // This would need to be tracked separately
		AverageWaitTime: dashboardStats.AverageOrderTime,
	}, nil
}

// Helper method to calculate growth percentage
func (s *OrderService) calculateGrowthPercentage(current, previous float64) float64 {
	if previous == 0 {
		if current > 0 {
			return 100.0 // 100% growth from 0
		}
		return 0.0
	}
	return ((current - previous) / previous) * 100.0
}

// GetOrdersBySlug gets orders for a branch using shop and branch slugs
func (s *OrderService) GetOrdersBySlug(ctx context.Context, shopSlug, branchSlug string, filters types.OrderFilters) (*types.OrdersResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Get orders using the branch ID
	orders, err := s.GetOrders(ctx, branch.ID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get orders by slug")
		return nil, fmt.Errorf("failed to get orders: %w", err)
	}

	return orders, nil
}

// GetOrderByIDAndSlug gets a specific order by ID using shop and branch slugs
func (s *OrderService) GetOrderByIDAndSlug(ctx context.Context, shopSlug, branchSlug, orderID string) (*types.OrderResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Parse order ID
	orderUUID, err := uuid.Parse(orderID)
	if err != nil {
		s.logger.WithError(err).WithField("order_id", orderID).Error("Invalid order ID format")
		return nil, fmt.Errorf("invalid order ID format")
	}

	// Get order using the branch ID and order ID
	order, err := s.GetOrderByID(ctx, branch.ID, orderUUID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get order by ID and slug")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	return order, nil
}

// GetOrderByNumberAndSlug gets a specific order by order number using shop and branch slugs
func (s *OrderService) GetOrderByNumberAndSlug(ctx context.Context, shopSlug, branchSlug, orderNumber string) (*types.OrderResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Get order using the branch ID and order number
	order, err := s.orderRepo.GetByOrderNumber(ctx, branch.ID, orderNumber)
	if err != nil {
		s.logger.WithError(err).WithField("order_number", orderNumber).Error("Failed to get order by number and slug")
		return nil, fmt.Errorf("failed to get order: %w", err)
	}

	response := s.convertOrderToResponse(*order)
	return &response, nil
}

// UpdateOrderStatusBySlug updates order status using shop and branch slugs
func (s *OrderService) UpdateOrderStatusBySlug(ctx context.Context, shopSlug, branchSlug string, orderID uuid.UUID, req types.UpdateOrderStatusRequest) (*types.OrderResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Update order status using the branch ID
	order, err := s.UpdateOrderStatus(ctx, branch.ID, orderID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update order status by slug")
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}

	return order, nil
}

// UpdateOrderStatusByNumberAndSlug updates order status using shop and branch slugs and order number
func (s *OrderService) UpdateOrderStatusByNumberAndSlug(ctx context.Context, shopSlug, branchSlug, orderNumber string, req types.UpdateOrderStatusRequest) (*types.OrderResponse, error) {
	// Look up branch by slug to get the branch ID
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).WithFields(logrus.Fields{
			"shop_slug":   shopSlug,
			"branch_slug": branchSlug,
		}).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("branch not found")
	}

	// Get order by order number to get the ID
	order, err := s.orderRepo.GetByOrderNumber(ctx, branch.ID, orderNumber)
	if err != nil {
		s.logger.WithError(err).WithField("order_number", orderNumber).Error("Failed to get order by number")
		return nil, fmt.Errorf("order not found")
	}

	// Update order status using the order ID
	updatedOrder, err := s.UpdateOrderStatus(ctx, branch.ID, order.ID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update order status by number and slug")
		return nil, fmt.Errorf("failed to update order status: %w", err)
	}

	return updatedOrder, nil
}
