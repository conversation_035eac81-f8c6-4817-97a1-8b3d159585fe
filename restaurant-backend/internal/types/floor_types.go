package types

import (
	"restaurant-backend/internal/models"
)

// Floor request types
type CreateFloorRequest struct {
	Name        string              `json:"name" binding:"required,min=1,max=255"`
	Description string              `json:"description"`
	Order       int                 `json:"order" binding:"required,min=1"`
	Layout      *models.FloorLayout `json:"layout"`
}

type UpdateFloorRequest struct {
	Name        string              `json:"name" binding:"omitempty,min=1,max=255"`
	Description *string             `json:"description"`
	Order       int                 `json:"order" binding:"omitempty,min=1"`
	Layout      *models.FloorLayout `json:"layout"`
}

// Floor filter types
type FloorFilters struct {
	StandardPagination
	StandardSorting
	StandardSearch
	StandardStatusFilter

	// Floor specific filters
	Name         string `form:"name"`
	IncludeAreas bool   `form:"include_areas"`
}

// FloorSortFields defines valid sort fields for floors
var FloorSortFields = []string{
	"name", "order", "created_at", "updated_at",
}

// Floor response types
type FloorResponse struct {
	ID          string             `json:"id"`
	BranchID    string             `json:"branch_id"`
	Name        string             `json:"name"`
	Description string             `json:"description"`
	Order       int                `json:"order"`
	Layout      models.FloorLayout `json:"layout"`
	IsActive    bool               `json:"is_active"`
	CreatedAt   string             `json:"created_at"`
	UpdatedAt   string             `json:"updated_at"`
	Areas       []AreaResponse     `json:"areas,omitempty"`
}

type FloorListResponse struct {
	Floors []FloorResponse `json:"floors"`
	Total  int             `json:"total"`
}

// FloorsResponse for standardized paginated floors
type FloorsResponse struct {
	StandardResponse
	Data []FloorResponse `json:"data"`
}
