package types

import (
	"time"

	"restaurant-backend/internal/models"
)

// Review request types
type CreateReviewRequest struct {
	CustomerName   string   `json:"customer_name" binding:"required"`
	CustomerEmail  string   `json:"customer_email"`
	CustomerAvatar string   `json:"customer_avatar"`
	Rating         int      `json:"rating" binding:"required,min=1,max=5"`
	Title          string   `json:"title"`
	Comment        string   `json:"comment" binding:"required"`
	Photos         []string `json:"photos"`
	Source         string   `json:"source"`
	Tags           []string `json:"tags"`
}

type RespondToReviewRequest struct {
	Message     string `json:"message" binding:"required"`
	RespondedBy string `json:"responded_by" binding:"required"`
}

type UpdateReviewResponseRequest struct {
	Message string `json:"message" binding:"required"`
}

type UpdateReviewStatusRequest struct {
	Status string `json:"status" binding:"required,oneof=pending approved rejected flagged"`
}

// Review filter types
type ReviewFilters struct {
	Status    string    `form:"status"`
	Rating    int       `form:"rating" binding:"min=0,max=5"`
	Source    string    `form:"source"`
	Sentiment string    `form:"sentiment"`
	Search    string    `form:"search"`
	DateFrom  time.Time `form:"date_from" time_format:"2006-01-02"`
	DateTo    time.Time `form:"date_to" time_format:"2006-01-02"`
	SortBy    string    `form:"sort_by"`
	SortOrder string    `form:"sort_order"`
	Page      int       `form:"page" binding:"min=0"`
	Limit     int       `form:"limit" binding:"min=0,max=100"`
}

// Review response types
type ReviewsListResponse struct {
	Data       []models.Review    `json:"data"`
	Pagination PaginationResponse `json:"pagination"`
}

type ReviewStats struct {
	TotalReviews        int64            `json:"total_reviews"`
	AverageRating       float64          `json:"average_rating"`
	RatingDistribution  map[int]int64    `json:"rating_distribution"`
	ResponseRate        float64          `json:"response_rate"`
	SentimentBreakdown  map[string]int64 `json:"sentiment_breakdown,omitempty"`
	SourceBreakdown     map[string]int64 `json:"source_breakdown,omitempty"`
	MonthlyTrend        []MonthlyStats   `json:"monthly_trend,omitempty"`
}

type MonthlyStats struct {
	Month         string  `json:"month"`
	TotalReviews  int64   `json:"total_reviews"`
	AverageRating float64 `json:"average_rating"`
}

// Common pagination response
type PaginationResponse struct {
	Page       int `json:"page"`
	Limit      int `json:"limit"`
	Total      int `json:"total"`
	TotalPages int `json:"total_pages"`
}

// Review insights types
type ReviewInsights struct {
	CommonKeywords       []KeywordInsight       `json:"common_keywords"`
	ImprovementAreas     []string               `json:"improvement_areas"`
	Strengths            []string               `json:"strengths"`
	CompetitorComparison *CompetitorComparison  `json:"competitor_comparison,omitempty"`
}

type KeywordInsight struct {
	Word      string `json:"word"`
	Count     int    `json:"count"`
	Sentiment string `json:"sentiment"`
}

type CompetitorComparison struct {
	AverageRating  float64 `json:"average_rating"`
	ReviewCount    int64   `json:"review_count"`
	ResponseRate   float64 `json:"response_rate"`
}

// Export request types
type ExportReviewsRequest struct {
	Format  string        `json:"format" binding:"required,oneof=csv excel pdf"`
	Filters ReviewFilters `json:"filters"`
}

type ExportReviewsResponse struct {
	DownloadURL string `json:"download_url"`
	ExpiresAt   string `json:"expires_at"`
}

// Sync external reviews types
type SyncExternalReviewsRequest struct {
	Sources []string `json:"sources"`
}

type SyncExternalReviewsResponse struct {
	Synced int      `json:"synced"`
	Errors []string `json:"errors"`
}

// Review constants
const (
	ReviewStatusPending  = "pending"
	ReviewStatusApproved = "approved"
	ReviewStatusRejected = "rejected"
	ReviewStatusFlagged  = "flagged"
)

const (
	ReviewSentimentPositive = "positive"
	ReviewSentimentNeutral  = "neutral"
	ReviewSentimentNegative = "negative"
)

const (
	ReviewSourceGoogle      = "google"
	ReviewSourceYelp        = "yelp"
	ReviewSourceFacebook    = "facebook"
	ReviewSourceTripadvisor = "tripadvisor"
	ReviewSourceInternal    = "internal"
	ReviewSourceOther       = "other"
)
