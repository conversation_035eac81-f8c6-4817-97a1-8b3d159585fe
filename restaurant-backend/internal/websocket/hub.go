package websocket

import (
	"encoding/json"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// In production, implement proper origin checking
		return true
	},
}

// Hub maintains the set of active clients and broadcasts messages to the clients
type Hub struct {
	// Registered clients
	clients map[*Client]bool

	// Inbound messages from the clients
	broadcast chan []byte

	// Register requests from the clients
	register chan *Client

	// Unregister requests from clients
	unregister chan *Client

	// Branch-specific client groups
	branchClients map[uuid.UUID]map[*Client]bool

	// Mutex for thread safety
	mutex sync.RWMutex

	logger *logrus.Logger
}

// Client is a middleman between the websocket connection and the hub
type Client struct {
	hub *Hub

	// The websocket connection
	conn *websocket.Conn

	// Buffered channel of outbound messages
	send chan []byte

	// User information
	userID   uuid.UUID
	branchID uuid.UUID
	role     string

	// Client metadata
	metadata map[string]interface{}
}

// Message represents a WebSocket message
type Message struct {
	Type      string                 `json:"type"`
	Data      interface{}            `json:"data"`
	Target    string                 `json:"target,omitempty"` // specific, branch, all
	BranchID  *uuid.UUID             `json:"branch_id,omitempty"`
	UserID    *uuid.UUID             `json:"user_id,omitempty"`
	Timestamp int64                  `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// OrderUpdate represents an order status update message
type OrderUpdate struct {
	OrderID     uuid.UUID `json:"order_id"`
	OrderNumber string    `json:"order_number"`
	Status      string    `json:"status"`
	UpdatedBy   uuid.UUID `json:"updated_by"`
	UpdatedAt   int64     `json:"updated_at"`
}

// TableUpdate represents a table status update message
type TableUpdate struct {
	TableID   uuid.UUID `json:"table_id"`
	TableName string    `json:"table_name"`
	Status    string    `json:"status"`
	UpdatedBy uuid.UUID `json:"updated_by"`
	UpdatedAt int64     `json:"updated_at"`
}

// NewHub creates a new WebSocket hub
func NewHub(logger *logrus.Logger) *Hub {
	return &Hub{
		clients:       make(map[*Client]bool),
		broadcast:     make(chan []byte),
		register:      make(chan *Client),
		unregister:    make(chan *Client),
		branchClients: make(map[uuid.UUID]map[*Client]bool),
		logger:        logger,
	}
}

// Run starts the hub and handles client registration/unregistration and message broadcasting
func (h *Hub) Run() {
	for {
		select {
		case client := <-h.register:
			h.registerClient(client)

		case client := <-h.unregister:
			h.unregisterClient(client)

		case message := <-h.broadcast:
			h.broadcastMessage(message)
		}
	}
}

func (h *Hub) registerClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	h.clients[client] = true

	// Add to branch-specific group
	if h.branchClients[client.branchID] == nil {
		h.branchClients[client.branchID] = make(map[*Client]bool)
	}
	h.branchClients[client.branchID][client] = true

	h.logger.WithFields(logrus.Fields{
		"user_id":   client.userID,
		"branch_id": client.branchID,
		"role":      client.role,
	}).Info("Client connected")

	// Send welcome message
	welcomeMsg := Message{
		Type: "connection",
		Data: map[string]interface{}{
			"status":  "connected",
			"user_id": client.userID,
		},
		Timestamp: getCurrentTimestamp(),
	}

	if data, err := json.Marshal(welcomeMsg); err == nil {
		select {
		case client.send <- data:
		default:
			close(client.send)
			delete(h.clients, client)
		}
	}
}

func (h *Hub) unregisterClient(client *Client) {
	h.mutex.Lock()
	defer h.mutex.Unlock()

	if _, ok := h.clients[client]; ok {
		delete(h.clients, client)
		close(client.send)

		// Remove from branch-specific group
		if branchClients, exists := h.branchClients[client.branchID]; exists {
			delete(branchClients, client)
			if len(branchClients) == 0 {
				delete(h.branchClients, client.branchID)
			}
		}

		h.logger.WithFields(logrus.Fields{
			"user_id":   client.userID,
			"branch_id": client.branchID,
		}).Info("Client disconnected")
	}
}

func (h *Hub) broadcastMessage(message []byte) {
	var msg Message
	if err := json.Unmarshal(message, &msg); err != nil {
		h.logger.WithError(err).Error("Failed to unmarshal broadcast message")
		return
	}

	h.mutex.RLock()
	defer h.mutex.RUnlock()

	switch msg.Target {
	case "specific":
		// Send to specific user
		if msg.UserID != nil {
			h.sendToUser(*msg.UserID, message)
		}
	case "branch":
		// Send to all clients in a branch
		if msg.BranchID != nil {
			h.sendToBranch(*msg.BranchID, message)
		}
	case "all":
		// Send to all clients
		h.sendToAll(message)
	default:
		// Default to branch-specific if branch_id is provided
		if msg.BranchID != nil {
			h.sendToBranch(*msg.BranchID, message)
		} else {
			h.sendToAll(message)
		}
	}
}

func (h *Hub) sendToUser(userID uuid.UUID, message []byte) {
	for client := range h.clients {
		if client.userID == userID {
			select {
			case client.send <- message:
			default:
				h.unregisterClient(client)
			}
		}
	}
}

func (h *Hub) sendToBranch(branchID uuid.UUID, message []byte) {
	if branchClients, exists := h.branchClients[branchID]; exists {
		for client := range branchClients {
			select {
			case client.send <- message:
			default:
				h.unregisterClient(client)
			}
		}
	}
}

func (h *Hub) sendToAll(message []byte) {
	for client := range h.clients {
		select {
		case client.send <- message:
		default:
			h.unregisterClient(client)
		}
	}
}

// BroadcastOrderUpdate broadcasts an order status update to the relevant branch
func (h *Hub) BroadcastOrderUpdate(branchID uuid.UUID, update OrderUpdate) {
	msg := Message{
		Type:      "order_update",
		Data:      update,
		Target:    "branch",
		BranchID:  &branchID,
		Timestamp: getCurrentTimestamp(),
	}

	if data, err := json.Marshal(msg); err == nil {
		h.broadcast <- data
	}
}

// BroadcastTableUpdate broadcasts a table status update to the relevant branch
func (h *Hub) BroadcastTableUpdate(branchID uuid.UUID, update TableUpdate) {
	msg := Message{
		Type:      "table_update",
		Data:      update,
		Target:    "branch",
		BranchID:  &branchID,
		Timestamp: getCurrentTimestamp(),
	}

	if data, err := json.Marshal(msg); err == nil {
		h.broadcast <- data
	}
}

// BroadcastNotification broadcasts a general notification
func (h *Hub) BroadcastNotification(branchID *uuid.UUID, userID *uuid.UUID, notification interface{}) {
	target := "all"
	if userID != nil {
		target = "specific"
	} else if branchID != nil {
		target = "branch"
	}

	msg := Message{
		Type:      "notification",
		Data:      notification,
		Target:    target,
		BranchID:  branchID,
		UserID:    userID,
		Timestamp: getCurrentTimestamp(),
	}

	if data, err := json.Marshal(msg); err == nil {
		h.broadcast <- data
	}
}

// GetConnectedClients returns the number of connected clients
func (h *Hub) GetConnectedClients() int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	return len(h.clients)
}

// GetBranchClients returns the number of clients connected for a specific branch
func (h *Hub) GetBranchClients(branchID uuid.UUID) int {
	h.mutex.RLock()
	defer h.mutex.RUnlock()
	if branchClients, exists := h.branchClients[branchID]; exists {
		return len(branchClients)
	}
	return 0
}

// HandleWebSocket handles WebSocket connection requests
func (h *Hub) HandleWebSocket(c *gin.Context) {
	// Extract user information from JWT token (should be set by auth middleware)
	userIDStr, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "User not authenticated"})
		return
	}

	branchID, exists := c.Get("branch_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Branch not specified"})
		return
	}

	role, _ := c.Get("role")

	// Convert user ID to appropriate format
	var userID uuid.UUID
	switch v := userIDStr.(type) {
	case string:
		// Try to parse as UUID, or generate one for OAuth provider IDs
		if parsedUUID, err := uuid.Parse(v); err == nil {
			userID = parsedUUID
		} else {
			// For OAuth provider IDs, generate a deterministic UUID
			// In a real implementation, you'd have a user mapping table
			userID = uuid.New() // This should be replaced with proper user lookup
		}
	case uuid.UUID:
		userID = v
	default:
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid user ID format"})
		return
	}

	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}

	client := &Client{
		hub:      h,
		conn:     conn,
		send:     make(chan []byte, 256),
		userID:   userID,
		branchID: branchID.(uuid.UUID),
		role:     role.(string),
		metadata: make(map[string]interface{}),
	}

	client.hub.register <- client

	// Allow collection of memory referenced by the caller by doing all work in new goroutines
	go client.writePump()
	go client.readPump()
}

func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}
