-- Migration: Add purchase orders tables
-- Description: Creates tables for purchase order management with consistent filtering, sorting, and pagination support

-- Create suppliers table if it doesn't exist
CREATE TABLE IF NOT EXISTS suppliers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    contact_person VA<PERSON>HA<PERSON>(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100) DEFAULT 'US',
    payment_terms VARCHAR(100),
    delivery_terms VARCHAR(100),
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create purchase_orders table
CREATE TABLE IF NOT EXISTS purchase_orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number VARCHAR(100) NOT NULL UNIQUE,
    shop_id UUID NOT NULL,
    branch_id UUID NOT NULL,
    supplier_id UUID NOT NULL REFERENCES suppliers(id) ON DELETE RESTRICT,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'ordered', 'received', 'partial', 'cancelled', 'completed')),
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    currency VARCHAR(3) DEFAULT 'USD',
    expected_delivery TIMESTAMP WITH TIME ZONE,
    actual_delivery TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create purchase_order_items table
CREATE TABLE IF NOT EXISTS purchase_order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    purchase_order_id UUID NOT NULL REFERENCES purchase_orders(id) ON DELETE CASCADE,
    ingredient_id UUID NOT NULL REFERENCES ingredients(id) ON DELETE RESTRICT,
    quantity DECIMAL(10,3) NOT NULL,
    unit VARCHAR(50) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    received_quantity DECIMAL(10,3) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_purchase_orders_shop_branch ON purchase_orders(shop_id, branch_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_supplier ON purchase_orders(supplier_id);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_status ON purchase_orders(status);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_created_at ON purchase_orders(created_at);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_expected_delivery ON purchase_orders(expected_delivery);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_order_number ON purchase_orders(order_number);
CREATE INDEX IF NOT EXISTS idx_purchase_orders_created_by ON purchase_orders(created_by);

CREATE INDEX IF NOT EXISTS idx_purchase_order_items_order ON purchase_order_items(purchase_order_id);
CREATE INDEX IF NOT EXISTS idx_purchase_order_items_ingredient ON purchase_order_items(ingredient_id);

CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name);
CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_suppliers_updated_at BEFORE UPDATE ON suppliers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchase_orders_updated_at BEFORE UPDATE ON purchase_orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_purchase_order_items_updated_at BEFORE UPDATE ON purchase_order_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample suppliers for testing
INSERT INTO suppliers (name, contact_person, email, phone, address, city, state, postal_code, payment_terms, delivery_terms, notes) VALUES
('Fresh Foods Supply Co.', 'John Smith', '<EMAIL>', '******-0101', '123 Market Street', 'San Francisco', 'CA', '94102', 'Net 30', '2-3 business days', 'Primary produce supplier'),
('Premium Meats Inc.', 'Sarah Johnson', '<EMAIL>', '******-0102', '456 Industrial Blvd', 'Los Angeles', 'CA', '90210', 'Net 15', '1-2 business days', 'High-quality meat supplier'),
('Ocean Fresh Seafood', 'Mike Chen', '<EMAIL>', '******-0103', '789 Harbor Drive', 'Seattle', 'WA', '98101', 'COD', 'Same day', 'Fresh seafood daily delivery'),
('Dairy Delights Ltd.', 'Emma Wilson', '<EMAIL>', '******-0104', '321 Farm Road', 'Portland', 'OR', '97201', 'Net 30', '2-3 business days', 'Organic dairy products'),
('Spice World Trading', 'Carlos Rodriguez', '<EMAIL>', '******-0105', '654 Spice Lane', 'Phoenix', 'AZ', '85001', 'Net 45', '3-5 business days', 'International spices and seasonings')
ON CONFLICT (name) DO NOTHING;

-- Insert sample purchase orders for testing
INSERT INTO purchase_orders (
    order_number, shop_id, branch_id, supplier_id, status, total_amount, 
    expected_delivery, notes, created_by
) VALUES
(
    'PO-' || EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::bigint,
    '550e8400-e29b-41d4-a716-************'::uuid,
    '550e8400-e29b-41d4-a716-************'::uuid,
    (SELECT id FROM suppliers WHERE name = 'Fresh Foods Supply Co.' LIMIT 1),
    'pending',
    1250.75,
    CURRENT_TIMESTAMP + INTERVAL '3 days',
    'Weekly produce order',
    '550e8400-e29b-41d4-a716-************'::uuid
),
(
    'PO-' || (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::bigint + 1),
    '550e8400-e29b-41d4-a716-************'::uuid,
    '550e8400-e29b-41d4-a716-************'::uuid,
    (SELECT id FROM suppliers WHERE name = 'Premium Meats Inc.' LIMIT 1),
    'approved',
    2100.50,
    CURRENT_TIMESTAMP + INTERVAL '2 days',
    'Monthly meat order',
    '550e8400-e29b-41d4-a716-************'::uuid
),
(
    'PO-' || (EXTRACT(EPOCH FROM CURRENT_TIMESTAMP)::bigint + 2),
    '550e8400-e29b-41d4-a716-************'::uuid,
    '550e8400-e29b-41d4-a716-************'::uuid,
    (SELECT id FROM suppliers WHERE name = 'Ocean Fresh Seafood' LIMIT 1),
    'received',
    850.25,
    CURRENT_TIMESTAMP - INTERVAL '1 day',
    'Daily seafood delivery',
    '550e8400-e29b-41d4-a716-************'::uuid
);

COMMENT ON TABLE suppliers IS 'Suppliers for purchase orders';
COMMENT ON TABLE purchase_orders IS 'Purchase orders with consistent filtering, sorting, and pagination support';
COMMENT ON TABLE purchase_order_items IS 'Items within purchase orders';

COMMENT ON COLUMN purchase_orders.status IS 'Order status: pending, approved, ordered, received, partial, cancelled, completed';
COMMENT ON COLUMN purchase_orders.order_number IS 'Unique order number for tracking';
COMMENT ON COLUMN purchase_orders.total_amount IS 'Total order amount in specified currency';
COMMENT ON COLUMN purchase_orders.expected_delivery IS 'Expected delivery date and time';
COMMENT ON COLUMN purchase_orders.actual_delivery IS 'Actual delivery date and time';

COMMENT ON COLUMN purchase_order_items.quantity IS 'Ordered quantity';
COMMENT ON COLUMN purchase_order_items.received_quantity IS 'Actually received quantity';
COMMENT ON COLUMN purchase_order_items.unit_price IS 'Price per unit';
COMMENT ON COLUMN purchase_order_items.total_price IS 'Total price for this line item';
