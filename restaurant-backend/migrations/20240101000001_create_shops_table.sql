-- +goose Up
-- <PERSON><PERSON> shops table
CREATE TABLE IF NOT EXISTS shops (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    shop_type VARCHAR(50) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    website VARCHAR(255),
    logo VARCHAR(500),
    cover_image VARCHAR(500),
    
    -- Address fields (embedded)
    address_street VARCHAR(255),
    address_city VARCHAR(100),
    address_state VARCHAR(100),
    address_zip_code VARCHAR(20),
    address_country VARCHAR(100),
    
    -- Business details
    cuisine_type VARCHAR(100),
    price_range VARCHAR(20),
    rating DECIMAL(3,2) DEFAULT 0,
    review_count INTEGER DEFAULT 0,
    
    -- <PERSON><PERSON><PERSON> fields
    social_media JSONB DEFAULT '{}',
    settings JSONB DEFAULT '{}',
    business_hours JSONB DEFAULT '{}',
    
    -- Status
    status VARCHAR(20) DEFAULT 'active',
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    CONSTRAINT fk_shops_owner FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_shops_owner_id ON shops(owner_id);
CREATE INDEX IF NOT EXISTS idx_shops_shop_type ON shops(shop_type);
CREATE INDEX IF NOT EXISTS idx_shops_cuisine_type ON shops(cuisine_type);
CREATE INDEX IF NOT EXISTS idx_shops_city ON shops(address_city);
CREATE INDEX IF NOT EXISTS idx_shops_state ON shops(address_state);
CREATE INDEX IF NOT EXISTS idx_shops_status ON shops(status);
CREATE INDEX IF NOT EXISTS idx_shops_is_active ON shops(is_active);

-- Create shop_branches table
CREATE TABLE IF NOT EXISTS shop_branches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    shop_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    
    -- Address fields (embedded)
    address_street VARCHAR(255),
    address_city VARCHAR(100),
    address_state VARCHAR(100),
    address_zip_code VARCHAR(20),
    address_country VARCHAR(100),
    
    -- Branch settings
    settings JSONB DEFAULT '{}',
    business_hours JSONB DEFAULT '{}',
    timezone VARCHAR(50) DEFAULT 'UTC',
    
    -- Status
    status VARCHAR(20) DEFAULT 'active',
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_shop_branches_shop FOREIGN KEY (shop_id) REFERENCES shops(id) ON DELETE CASCADE,
    CONSTRAINT uk_shop_branches_shop_slug UNIQUE (shop_id, slug)
);

-- Create indexes for shop_branches
CREATE INDEX IF NOT EXISTS idx_shop_branches_shop_id ON shop_branches(shop_id);
CREATE INDEX IF NOT EXISTS idx_shop_branches_city ON shop_branches(address_city);
CREATE INDEX IF NOT EXISTS idx_shop_branches_state ON shop_branches(address_state);
CREATE INDEX IF NOT EXISTS idx_shop_branches_status ON shop_branches(status);
CREATE INDEX IF NOT EXISTS idx_shop_branches_is_active ON shop_branches(is_active);

-- Create updated_at trigger for shops
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_shops_updated_at BEFORE UPDATE ON shops
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_shop_branches_updated_at BEFORE UPDATE ON shop_branches
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- +goose Down
DROP TRIGGER IF EXISTS update_shop_branches_updated_at ON shop_branches;
DROP TRIGGER IF EXISTS update_shops_updated_at ON shops;
DROP FUNCTION IF EXISTS update_updated_at_column();
DROP TABLE IF EXISTS shop_branches;
DROP TABLE IF EXISTS shops;
