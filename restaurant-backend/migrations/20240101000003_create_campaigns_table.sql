-- +goose Up
-- Create communication_templates table
CREATE TABLE IF NOT EXISTS communication_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- email, sms, push
    category VARCHAR(50) NOT NULL, -- appointment_confirmation, marketing, etc.
    subject VARCHAR(255),
    content TEXT NOT NULL,
    variables JSONB DEFAULT '[]', -- available template variables
    is_default BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_communication_templates_merchant FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE
);

-- Create campaign_segments table
CREATE TABLE IF NOT EXISTS campaign_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(50) NOT NULL, -- demographic, behavioral, geographic
    criteria JSONB NOT NULL DEFAULT '{}',
    customer_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_campaign_segments_merchant FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE
);

-- Create communication_campaigns table
CREATE TABLE IF NOT EXISTS communication_campaigns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type VARCHAR(20) NOT NULL, -- email, sms, push
    status VARCHAR(20) DEFAULT 'draft', -- draft, scheduled, running, completed, cancelled
    template_id UUID,
    segment_id UUID,
    
    -- Campaign content
    subject VARCHAR(255),
    content TEXT NOT NULL,
    
    -- Scheduling
    scheduled_at TIMESTAMP WITH TIME ZONE,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    
    -- Targeting
    target_audience VARCHAR(100), -- all, segment, custom
    recipients JSONB DEFAULT '[]', -- for custom targeting
    
    -- Analytics
    total_recipients INTEGER DEFAULT 0,
    sent_count INTEGER DEFAULT 0,
    delivered_count INTEGER DEFAULT 0,
    opened_count INTEGER DEFAULT 0,
    clicked_count INTEGER DEFAULT 0,
    unsubscribe_count INTEGER DEFAULT 0,
    bounce_count INTEGER DEFAULT 0,
    
    -- Settings
    settings JSONB DEFAULT '{}',
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_communication_campaigns_merchant FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    CONSTRAINT fk_communication_campaigns_template FOREIGN KEY (template_id) REFERENCES communication_templates(id) ON DELETE SET NULL,
    CONSTRAINT fk_communication_campaigns_segment FOREIGN KEY (segment_id) REFERENCES campaign_segments(id) ON DELETE SET NULL
);

-- Create communication_analytics table
CREATE TABLE IF NOT EXISTS communication_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    merchant_id UUID NOT NULL,
    campaign_id UUID,
    type VARCHAR(20) NOT NULL, -- email, sms, push
    event VARCHAR(50) NOT NULL, -- sent, delivered, opened, clicked, etc.
    recipient VARCHAR(255) NOT NULL,
    event_data JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT fk_communication_analytics_merchant FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
    CONSTRAINT fk_communication_analytics_campaign FOREIGN KEY (campaign_id) REFERENCES communication_campaigns(id) ON DELETE SET NULL
);

-- Create indexes
CREATE INDEX IF NOT EXISTS idx_communication_templates_merchant_id ON communication_templates(merchant_id);
CREATE INDEX IF NOT EXISTS idx_communication_templates_type ON communication_templates(type);
CREATE INDEX IF NOT EXISTS idx_communication_templates_category ON communication_templates(category);
CREATE INDEX IF NOT EXISTS idx_communication_templates_is_active ON communication_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_campaign_segments_merchant_id ON campaign_segments(merchant_id);
CREATE INDEX IF NOT EXISTS idx_campaign_segments_type ON campaign_segments(type);
CREATE INDEX IF NOT EXISTS idx_campaign_segments_is_active ON campaign_segments(is_active);

CREATE INDEX IF NOT EXISTS idx_communication_campaigns_merchant_id ON communication_campaigns(merchant_id);
CREATE INDEX IF NOT EXISTS idx_communication_campaigns_type ON communication_campaigns(type);
CREATE INDEX IF NOT EXISTS idx_communication_campaigns_status ON communication_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_communication_campaigns_template_id ON communication_campaigns(template_id);
CREATE INDEX IF NOT EXISTS idx_communication_campaigns_segment_id ON communication_campaigns(segment_id);
CREATE INDEX IF NOT EXISTS idx_communication_campaigns_scheduled_at ON communication_campaigns(scheduled_at);

CREATE INDEX IF NOT EXISTS idx_communication_analytics_merchant_id ON communication_analytics(merchant_id);
CREATE INDEX IF NOT EXISTS idx_communication_analytics_campaign_id ON communication_analytics(campaign_id);
CREATE INDEX IF NOT EXISTS idx_communication_analytics_type ON communication_analytics(type);
CREATE INDEX IF NOT EXISTS idx_communication_analytics_event ON communication_analytics(event);
CREATE INDEX IF NOT EXISTS idx_communication_analytics_timestamp ON communication_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_communication_analytics_recipient ON communication_analytics(recipient);

-- Create updated_at triggers
CREATE TRIGGER update_communication_templates_updated_at BEFORE UPDATE ON communication_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_campaign_segments_updated_at BEFORE UPDATE ON campaign_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_communication_campaigns_updated_at BEFORE UPDATE ON communication_campaigns
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_communication_analytics_updated_at BEFORE UPDATE ON communication_analytics
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create trigger to update last_updated on campaign_segments
CREATE OR REPLACE FUNCTION update_segment_last_updated()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_updated = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_campaign_segments_last_updated BEFORE UPDATE ON campaign_segments
    FOR EACH ROW EXECUTE FUNCTION update_segment_last_updated();

-- +goose Down
DROP TRIGGER IF EXISTS update_campaign_segments_last_updated ON campaign_segments;
DROP FUNCTION IF EXISTS update_segment_last_updated();

DROP TRIGGER IF EXISTS update_communication_analytics_updated_at ON communication_analytics;
DROP TRIGGER IF EXISTS update_communication_campaigns_updated_at ON communication_campaigns;
DROP TRIGGER IF EXISTS update_campaign_segments_updated_at ON campaign_segments;
DROP TRIGGER IF EXISTS update_communication_templates_updated_at ON communication_templates;

DROP TABLE IF EXISTS communication_analytics;
DROP TABLE IF EXISTS communication_campaigns;
DROP TABLE IF EXISTS campaign_segments;
DROP TABLE IF EXISTS communication_templates;
