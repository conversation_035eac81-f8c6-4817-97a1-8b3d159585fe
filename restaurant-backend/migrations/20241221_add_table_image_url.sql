-- Migration: Add image_url column to tables table
-- Date: 2024-12-21
-- Description: Add image_url field to store table background images for better UI display

-- Add image_url column to tables table
ALTER TABLE tables 
ADD COLUMN image_url VARCHAR(500) DEFAULT '';

-- Add comment to document the column purpose
COMMENT ON COLUMN tables.image_url IS 'URL of the table background image for display in the UI';

-- Update existing tables with default empty string (already handled by DEFAULT)
-- No need for explicit UPDATE since DEFAULT handles new rows and existing rows will have empty string

-- Create index for potential image_url queries (optional, for performance if needed)
-- CREATE INDEX idx_tables_image_url ON tables(image_url) WHERE image_url != '';
