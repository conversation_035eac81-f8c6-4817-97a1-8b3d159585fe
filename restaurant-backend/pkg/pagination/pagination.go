package pagination

import (
	"fmt"
	"strings"

	"restaurant-backend/internal/types"
)

// ApplyStandardDefaults applies default values to pagination, sorting, and search
func ApplyStandardDefaults(pagination *types.StandardPagination, sorting *types.StandardSorting) {
	if pagination != nil {
		pagination.ApplyDefaults()
	}
	if sorting != nil {
		sorting.ApplyDefaults()
	}
}

// ValidateAndApplySorting validates sort field and applies defaults
func ValidateAndApplySorting(sorting *types.StandardSorting, allowedFields []string, defaultField string) {
	if sorting == nil {
		return
	}

	// Apply defaults first
	if sorting.SortBy == "" {
		sorting.SortBy = defaultField
	}
	if sorting.SortOrder == "" {
		sorting.SortOrder = types.SortingDefaults.SortOrder
	}

	// Validate sort field
	if !types.ValidateSortField(sorting.SortBy, allowedFields) {
		sorting.SortBy = defaultField
	}

	// Normalize sort order
	sorting.SortOrder = strings.ToLower(sorting.SortOrder)
	if sorting.SortOrder != "asc" && sorting.SortOrder != "desc" {
		sorting.SortOrder = "desc"
	}
}

// BuildOrderByClause builds SQL ORDER BY clause from sorting parameters
func BuildOrderByClause(sorting types.StandardSorting, fieldMapping map[string]string) string {
	sortField := sorting.SortBy
	
	// Map field name to database column if mapping exists
	if fieldMapping != nil {
		if dbField, exists := fieldMapping[sortField]; exists {
			sortField = dbField
		}
	}

	sortOrder := strings.ToUpper(sorting.SortOrder)
	return fmt.Sprintf("%s %s", sortField, sortOrder)
}

// BuildSearchCondition builds SQL search condition for multiple fields
func BuildSearchCondition(search string, searchFields []string, tableName string) (string, []interface{}) {
	if search == "" || len(searchFields) == 0 {
		return "", nil
	}

	var conditions []string
	var args []interface{}
	searchPattern := "%" + search + "%"

	for _, field := range searchFields {
		if tableName != "" {
			field = tableName + "." + field
		}
		conditions = append(conditions, fmt.Sprintf("%s ILIKE ?", field))
		args = append(args, searchPattern)
	}

	return "(" + strings.Join(conditions, " OR ") + ")", args
}

// CreateStandardResponse creates a standardized response with pagination info
func CreateStandardResponse(total int64, page, limit int) types.StandardResponse {
	return types.StandardResponse{
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: types.CalculateTotalPages(total, limit),
	}
}

// PaginationInfo holds pagination calculation results
type PaginationInfo struct {
	Offset     int
	Limit      int
	Page       int
	TotalPages int
}

// CalculatePaginationInfo calculates all pagination-related values
func CalculatePaginationInfo(total int64, page, limit int) PaginationInfo {
	if page <= 0 {
		page = 1
	}
	if limit <= 0 {
		limit = types.PaginationDefaults.Limit
	}

	offset := types.CalculateOffset(page, limit)
	totalPages := types.CalculateTotalPages(total, limit)

	return PaginationInfo{
		Offset:     offset,
		Limit:      limit,
		Page:       page,
		TotalPages: totalPages,
	}
}

// FilterBuilder helps build dynamic WHERE clauses
type FilterBuilder struct {
	conditions []string
	args       []interface{}
}

// NewFilterBuilder creates a new filter builder
func NewFilterBuilder() *FilterBuilder {
	return &FilterBuilder{
		conditions: make([]string, 0),
		args:       make([]interface{}, 0),
	}
}

// AddCondition adds a condition to the filter
func (fb *FilterBuilder) AddCondition(condition string, args ...interface{}) {
	if condition != "" {
		fb.conditions = append(fb.conditions, condition)
		fb.args = append(fb.args, args...)
	}
}

// AddStringFilter adds a string filter condition
func (fb *FilterBuilder) AddStringFilter(field, value string) {
	if value != "" {
		fb.AddCondition(fmt.Sprintf("%s = ?", field), value)
	}
}

// AddBoolFilter adds a boolean filter condition
func (fb *FilterBuilder) AddBoolFilter(field string, value *bool) {
	if value != nil {
		fb.AddCondition(fmt.Sprintf("%s = ?", field), *value)
	}
}

// AddUUIDFilter adds a UUID filter condition
func (fb *FilterBuilder) AddUUIDFilter(field string, value interface{}) {
	if value != nil {
		fb.AddCondition(fmt.Sprintf("%s = ?", field), value)
	}
}

// AddRangeFilter adds a range filter condition
func (fb *FilterBuilder) AddRangeFilter(field string, min, max *float64) {
	if min != nil {
		fb.AddCondition(fmt.Sprintf("%s >= ?", field), *min)
	}
	if max != nil {
		fb.AddCondition(fmt.Sprintf("%s <= ?", field), *max)
	}
}

// AddDateRangeFilter adds a date range filter condition
func (fb *FilterBuilder) AddDateRangeFilter(field string, dateFilters types.StandardDateFilters) {
	if dateFilters.DateFrom != nil {
		fb.AddCondition(fmt.Sprintf("%s >= ?", field), *dateFilters.DateFrom)
	}
	if dateFilters.DateTo != nil {
		fb.AddCondition(fmt.Sprintf("%s <= ?", field), *dateFilters.DateTo)
	}
}

// AddSearchFilter adds a search filter condition
func (fb *FilterBuilder) AddSearchFilter(search string, searchFields []string, tableName string) {
	condition, args := BuildSearchCondition(search, searchFields, tableName)
	if condition != "" {
		fb.AddCondition(condition, args...)
	}
}

// Build returns the final WHERE clause and arguments
func (fb *FilterBuilder) Build() (string, []interface{}) {
	if len(fb.conditions) == 0 {
		return "", nil
	}
	return strings.Join(fb.conditions, " AND "), fb.args
}

// Common field mappings for database columns
var CommonFieldMappings = map[string]string{
	"total_amount": "total",
	"created_date": "created_at",
	"updated_date": "updated_at",
	"is_active":    "active",
}

// GetFieldMapping returns field mapping with common mappings as fallback
func GetFieldMapping(customMapping map[string]string) map[string]string {
	if customMapping == nil {
		return CommonFieldMappings
	}

	// Merge custom mapping with common mappings
	result := make(map[string]string)
	for k, v := range CommonFieldMappings {
		result[k] = v
	}
	for k, v := range customMapping {
		result[k] = v
	}
	return result
}
