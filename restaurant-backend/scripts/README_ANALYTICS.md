# Analytics Seeding Documentation

This directory contains scripts and tools for seeding comprehensive analytics data into your restaurant database.

## Overview

The analytics seeding system provides realistic sample data for:

- **Sales Metrics**: Revenue, order counts, and customer counts by hour and day
- **Menu Item Metrics**: Performance data for each menu item including orders and revenue
- **Customer Metrics**: Customer behavior patterns, visit frequency, and spending habits
- **Staff Metrics**: Staff performance including orders processed, efficiency, and ratings
- **Table Metrics**: Table utilization rates, turnover, and revenue by table and hour
- **Analytics Reports**: Pre-generated sample reports for different business areas

## Seeding Methods

### Method 1: Automatic Go-based Seeding (Recommended)

The analytics seeding is automatically integrated into the main database seeding process. When you start the backend server, it will automatically seed analytics data if:

1. The thai-delight shop and downtown branch exist
2. Menu items exist for the branch
3. No analytics data already exists

**To trigger automatic seeding:**

```bash
# Start the backend server (this will run migrations and seeding)
make backend

# Or run the server directly
go run cmd/server/main.go
```

### Method 2: Direct PostgreSQL Seeding

For more control or to re-seed analytics data, you can use the PostgreSQL scripts directly.

**Prerequisites:**
- PostgreSQL client (`psql`) installed
- Access to your database
- Existing shop, branch, menu items, staff, and tables data

**Usage:**

```bash
# Method 2a: Using the shell script (recommended)
cd restaurant-backend/scripts
./run_analytics_seed.sh

# Method 2b: Direct psql execution
psql -h localhost -p 5432 -U postgres -d restaurant_db -f seed_analytics.sql
```

**Environment Variables for Shell Script:**

```bash
export DB_HOST="localhost"        # Database host (default: localhost)
export DB_PORT="5432"            # Database port (default: 5432)
export DB_NAME="restaurant_db"   # Database name (default: restaurant_db)
export DB_USER="postgres"        # Database user (default: postgres)
export DB_PASSWORD="password"    # Database password (will prompt if not set)
```

## Data Generated

### Sales Metrics (30 days)
- **Hourly data** from 10 AM to 10 PM
- **Peak hours**: 12-2 PM and 6-8 PM (2x multiplier)
- **Weekend boost**: 30% increase on weekends
- **Metrics**: Revenue, order count, customer count

### Menu Item Metrics (30 days)
- **Daily performance** for each menu item
- **Popularity factors**: Some items are more popular than others
- **Realistic quantities**: Multiple items per order
- **Revenue calculation**: Based on actual menu item prices

### Customer Metrics (30 days)
- **New customers**: 5-20 per day
- **Returning customers**: 10-35 per day
- **Spending patterns**: $75-125 average spend
- **Customer types**: New vs returning behavior

### Staff Metrics (30 days)
- **Performance data** for each staff member
- **Orders processed**: 5-25 per day per staff
- **Efficiency metrics**: Orders per hour
- **Customer ratings**: 3.5-5.0 scale
- **Working hours**: 6-10 hours per day

### Table Metrics (30 days, hourly)
- **Utilization rates**: 30-70% base utilization
- **Peak hour multipliers**: Higher utilization during busy times
- **Turnover data**: 0-2 turnovers per hour
- **Revenue tracking**: Per table revenue
- **Party size and dining time**: Realistic averages

### Analytics Reports (5 sample reports)
- **Weekly Sales Report**: Revenue, orders, top items
- **Menu Performance Analysis**: Item popularity and profitability
- **Staff Performance Report**: Efficiency and customer ratings
- **Customer Analytics Report**: Behavior and satisfaction
- **Monthly Financial Summary**: Complete financial overview

## Data Characteristics

### Realistic Patterns
- **Business hours**: 10 AM to 10 PM
- **Peak times**: Lunch (12-2 PM) and dinner (6-8 PM)
- **Weekend patterns**: 30-40% increase in activity
- **Seasonal variation**: Random but realistic fluctuations

### Data Relationships
- **Consistent totals**: Sales metrics align with menu item metrics
- **Staff performance**: Varies by individual with realistic ranges
- **Table utilization**: Considers table capacity and restaurant flow
- **Customer behavior**: New vs returning customer patterns

## Troubleshooting

### Common Issues

1. **"Branch not found" error**
   - Ensure the thai-delight shop and downtown branch exist
   - Check that the shop slug is exactly "thai-delight"
   - Verify the branch slug is exactly "downtown"

2. **"No menu items found" error**
   - Seed menu items first using the main seeding process
   - Ensure menu items are associated with the correct branch

3. **"Analytics data already exists" message**
   - This is normal - the system prevents duplicate seeding
   - To re-seed, delete existing analytics data first:
     ```sql
     DELETE FROM analytics_reports WHERE branch_id = 'your-branch-id';
     DELETE FROM table_metrics WHERE branch_id = 'your-branch-id';
     DELETE FROM staff_metrics WHERE branch_id = 'your-branch-id';
     DELETE FROM customer_metrics WHERE branch_id = 'your-branch-id';
     DELETE FROM menu_item_metrics WHERE branch_id = 'your-branch-id';
     DELETE FROM sales_metrics WHERE branch_id = 'your-branch-id';
     ```

4. **Database connection issues**
   - Verify your database credentials
   - Ensure the database server is running
   - Check network connectivity

### Verification

To verify the seeding was successful, run these queries:

```sql
-- Check sales metrics count
SELECT COUNT(*) FROM sales_metrics WHERE branch_id = 'your-branch-id';

-- Check date range of data
SELECT MIN(date), MAX(date) FROM sales_metrics WHERE branch_id = 'your-branch-id';

-- Check analytics reports
SELECT report_type, title, status FROM analytics_reports WHERE branch_id = 'your-branch-id';

-- Check staff metrics
SELECT COUNT(DISTINCT user_id) as staff_count, COUNT(*) as total_records 
FROM staff_metrics WHERE branch_id = 'your-branch-id';
```

## Files

- `seed_analytics.sql`: PostgreSQL script for direct database seeding
- `run_analytics_seed.sh`: Shell script wrapper for easy execution
- `README_ANALYTICS.md`: This documentation file

## Integration

The analytics seeding is integrated into the main Go application at:
- `internal/database/seed_analytics.go`: Go-based seeding functions
- `internal/database/database.go`: Integration with main seeding process

The seeding functions are automatically called during the database migration and seeding process when the backend server starts.
