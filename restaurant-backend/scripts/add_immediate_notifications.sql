-- Add immediate notifications for testing the notification bell
-- These will appear at the top of the notification list

-- Add a very recent urgent notification
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'URGENT: Kitchen Equipment Issue' as title,
    'The main stove has stopped working. Kitchen operations may be affected. Please check immediately.' as message,
    'system' as type,
    'urgent' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/equipment' as link,
    'Check Equipment' as action_label,
    JSON_BUILD_OBJECT(
        'equipment', 'main-stove',
        'issue_type', 'malfunction',
        'severity', 'high',
        'reported_by', 'Kitchen Staff'
    ) as data,
    NOW() as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

-- Add a recent order notification
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'New Order #ORD-2024-999' as title,
    'A new order has just been placed. Customer: <PERSON>. Total: $67.50. Items: Pad Thai, Green Curry, Mango Sticky Rice.' as message,
    'order' as type,
    'high' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/orders' as link,
    'View Order' as action_label,
    JSON_BUILD_OBJECT(
        'order_id', 'ORD-2024-999',
        'customer_name', 'Sarah Johnson',
        'amount', 67.50,
        'items_count', 3,
        'items', ARRAY['Pad Thai', 'Green Curry', 'Mango Sticky Rice']
    ) as data,
    NOW() - INTERVAL '30 seconds' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

-- Add a table reservation notification
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Table Reservation - Tonight' as title,
    'New reservation for 6 people at 8:00 PM tonight. Customer: Michael Chen. Special request: Birthday celebration.' as message,
    'reservation' as type,
    'medium' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/reservations' as link,
    'View Reservation' as action_label,
    JSON_BUILD_OBJECT(
        'reservation_id', 'RES-2024-888',
        'party_size', 6,
        'time', '20:00',
        'customer', 'Michael Chen',
        'special_request', 'Birthday celebration'
    ) as data,
    NOW() - INTERVAL '1 minute' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

-- Add a positive review notification
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Excellent 5-Star Review!' as title,
    'A customer just left a 5-star review: "Outstanding food and service! The Pad Thai was absolutely perfect. Will definitely be back!"' as message,
    'review' as type,
    'low' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/reviews' as link,
    'View Review' as action_label,
    JSON_BUILD_OBJECT(
        'rating', 5,
        'customer', 'Happy Foodie',
        'review_id', 'REV-777',
        'review_text', 'Outstanding food and service! The Pad Thai was absolutely perfect. Will definitely be back!'
    ) as data,
    NOW() - INTERVAL '2 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

-- Add a staff notification
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    'Staff Check-in: Evening Shift' as title,
    'All evening shift staff have checked in successfully. Kitchen team: 4 members, Service team: 3 members. Ready for dinner service.' as message,
    'staff' as type,
    'low' as priority,
    true as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/staff' as link,
    'View Staff' as action_label,
    JSON_BUILD_OBJECT(
        'shift', 'evening',
        'kitchen_staff', 4,
        'service_staff', 3,
        'total_staff', 7,
        'status', 'all_checked_in'
    ) as data,
    NOW() - INTERVAL '3 minutes' as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';
