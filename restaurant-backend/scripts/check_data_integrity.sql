-- Check for orphaned shops (shops with owner_id that don't exist in users table)
SELECT 
    s.id as shop_id,
    s.name as shop_name,
    s.owner_id,
    'Missing user' as issue
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL;

-- Check for shops with NULL owner_id
SELECT 
    s.id as shop_id,
    s.name as shop_name,
    s.owner_id,
    'NULL owner_id' as issue
FROM shops s
WHERE s.owner_id IS NULL;

-- Count total problematic records
SELECT 
    COUNT(*) as total_orphaned_shops
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL;

-- Show all users for reference
SELECT id, email, name FROM users ORDER BY created_at;

-- Show all shops for reference
SELECT id, name, owner_id, created_at FROM shops ORDER BY created_at;
