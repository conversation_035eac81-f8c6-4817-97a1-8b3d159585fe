-- Cleanup script to remove old merchant_id columns
-- This script safely removes the old merchant_id columns after successful migration to shop_id

BEGIN;

-- Step 1: Verify that shop_id columns exist and have data
DO $$
DECLARE
    users_with_shop_id INTEGER;
    roles_with_shop_id INTEGER;
    users_with_null_shop_id INTEGER;
    roles_with_null_shop_id INTEGER;
BEGIN
    -- Check users table
    SELECT COUNT(*) INTO users_with_shop_id FROM users WHERE shop_id IS NOT NULL;
    SELECT COUNT(*) INTO users_with_null_shop_id FROM users WHERE shop_id IS NULL;
    
    -- Check roles table
    SELECT COUNT(*) INTO roles_with_shop_id FROM roles WHERE shop_id IS NOT NULL;
    SELECT COUNT(*) INTO roles_with_null_shop_id FROM roles WHERE shop_id IS NULL;
    
    RAISE NOTICE 'Users with shop_id: %, Users with NULL shop_id: %', users_with_shop_id, users_with_null_shop_id;
    RAISE NOTICE 'Roles with shop_id: %, Roles with NULL shop_id: %', roles_with_shop_id, roles_with_null_shop_id;
    
    -- Safety check: ensure no NULL shop_id values
    IF users_with_null_shop_id > 0 OR roles_with_null_shop_id > 0 THEN
        RAISE EXCEPTION 'Found NULL shop_id values. Migration not complete. Aborting cleanup.';
    END IF;
    
    -- Safety check: ensure we have data
    IF users_with_shop_id = 0 AND roles_with_shop_id = 0 THEN
        RAISE EXCEPTION 'No data found with shop_id. Something is wrong. Aborting cleanup.';
    END IF;
    
    RAISE NOTICE 'Safety checks passed. Proceeding with cleanup.';
END $$;

-- Step 2: Drop foreign key constraints that reference merchant_id (if any)
DO $$
BEGIN
    -- Drop foreign key constraints on users.merchant_id if they exist
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name LIKE '%merchant_id%' AND table_name = 'users'
    ) THEN
        -- Get the actual constraint name and drop it
        DECLARE
            constraint_name_var TEXT;
        BEGIN
            SELECT constraint_name INTO constraint_name_var
            FROM information_schema.table_constraints 
            WHERE constraint_name LIKE '%merchant_id%' AND table_name = 'users'
            LIMIT 1;
            
            IF constraint_name_var IS NOT NULL THEN
                EXECUTE 'ALTER TABLE users DROP CONSTRAINT ' || constraint_name_var;
                RAISE NOTICE 'Dropped constraint % from users table', constraint_name_var;
            END IF;
        END;
    END IF;

    -- Drop foreign key constraints on roles.merchant_id if they exist
    IF EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name LIKE '%merchant_id%' AND table_name = 'roles'
    ) THEN
        -- Get the actual constraint name and drop it
        DECLARE
            constraint_name_var TEXT;
        BEGIN
            SELECT constraint_name INTO constraint_name_var
            FROM information_schema.table_constraints 
            WHERE constraint_name LIKE '%merchant_id%' AND table_name = 'roles'
            LIMIT 1;
            
            IF constraint_name_var IS NOT NULL THEN
                EXECUTE 'ALTER TABLE roles DROP CONSTRAINT ' || constraint_name_var;
                RAISE NOTICE 'Dropped constraint % from roles table', constraint_name_var;
            END IF;
        END;
    END IF;
END $$;

-- Step 3: Drop indexes on merchant_id columns (if any)
DO $$
BEGIN
    -- Drop indexes on users.merchant_id
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname LIKE '%merchant_id%' AND tablename = 'users'
    ) THEN
        DROP INDEX IF EXISTS idx_users_merchant_id;
        RAISE NOTICE 'Dropped index idx_users_merchant_id';
    END IF;

    -- Drop indexes on roles.merchant_id
    IF EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE indexname LIKE '%merchant_id%' AND tablename = 'roles'
    ) THEN
        DROP INDEX IF EXISTS idx_roles_merchant_id;
        RAISE NOTICE 'Dropped index idx_roles_merchant_id';
    END IF;
END $$;

-- Step 4: Remove the merchant_id columns
DO $$
BEGIN
    -- Remove merchant_id from users table
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'merchant_id'
    ) THEN
        ALTER TABLE users DROP COLUMN merchant_id;
        RAISE NOTICE 'Dropped merchant_id column from users table';
    ELSE
        RAISE NOTICE 'merchant_id column does not exist in users table';
    END IF;

    -- Remove merchant_id from roles table
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'roles' AND column_name = 'merchant_id'
    ) THEN
        ALTER TABLE roles DROP COLUMN merchant_id;
        RAISE NOTICE 'Dropped merchant_id column from roles table';
    ELSE
        RAISE NOTICE 'merchant_id column does not exist in roles table';
    END IF;
END $$;

COMMIT;

-- Step 5: Verify cleanup
SELECT 
    'users' as table_name,
    COUNT(*) as total_records,
    COUNT(shop_id) as records_with_shop_id,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'users' AND column_name = 'merchant_id'
    ) THEN 'EXISTS' ELSE 'REMOVED' END as merchant_id_column_status
FROM users

UNION ALL

SELECT 
    'roles' as table_name,
    COUNT(*) as total_records,
    COUNT(shop_id) as records_with_shop_id,
    CASE WHEN EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'roles' AND column_name = 'merchant_id'
    ) THEN 'EXISTS' ELSE 'REMOVED' END as merchant_id_column_status
FROM roles;

-- Final verification message
DO $$
BEGIN
    RAISE NOTICE '=== CLEANUP COMPLETED SUCCESSFULLY ===';
    RAISE NOTICE 'Old merchant_id columns have been removed from users and roles tables';
    RAISE NOTICE 'All data is now using shop_id columns with proper foreign key constraints';
    RAISE NOTICE 'The migration from MerchantID to ShopID is now complete';
END $$;
