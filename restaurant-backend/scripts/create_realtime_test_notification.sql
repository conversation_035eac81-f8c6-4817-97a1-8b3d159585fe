-- Create a real-time test notification
INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) 
SELECT 
    s.id as shop_id,
    sb.id as branch_id,
    '🚀 REAL-TIME TEST: Live Notification!' as title,
    'This notification was just created to test real-time functionality. If you see this appear immediately in your notification bell, the system is working perfectly! Created at: ' || NOW()::TEXT as message,
    'system' as type,
    'urgent' as priority,
    false as is_read,
    '/app/restaurant/' || s.slug || '/' || sb.slug || '/dashboard' as link,
    'View Dashboard' as action_label,
    JSON_BUILD_OBJECT(
        'test', true,
        'created_via', 'sql_script',
        'timestamp', NOW()::TEXT,
        'feature', 'real-time notifications',
        'purpose', 'testing notification bell updates'
    ) as data,
    NOW() as timestamp
FROM shops s
JOIN shop_branches sb ON s.id = sb.shop_id
WHERE s.slug = 'weerawat-poseeya' AND sb.slug = 'the-green-terrace';

-- Show the notification count after creation
SELECT 
    COUNT(*) as total_notifications, 
    COUNT(CASE WHEN is_read = false THEN 1 END) as unread_notifications,
    'Notification created successfully!' as message
FROM notifications 
WHERE shop_id IN (SELECT id FROM shops WHERE slug = 'weerawat-poseeya');
