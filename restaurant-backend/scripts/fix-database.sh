#!/bin/bash

# Database integrity fix script
# This script fixes foreign key constraint issues

set -e

echo "🔧 Database Integrity Fix Script"
echo "================================"

# Check if we're in the right directory
if [ ! -f "go.mod" ]; then
    echo "❌ Error: Please run this script from the restaurant-backend directory"
    exit 1
fi

# Check if database environment variables are set
if [ -z "$DB_HOST" ] || [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "⚠️  Database environment variables not set. Using defaults..."
    export DB_HOST=${DB_HOST:-localhost}
    export DB_PORT=${DB_PORT:-5432}
    export DB_NAME=${DB_NAME:-restaurant_db}
    export DB_USER=${DB_USER:-postgres}
    export DB_PASSWORD=${DB_PASSWORD:-password}
    export DB_SSLMODE=${DB_SSLMODE:-disable}
fi

echo "📊 Database Configuration:"
echo "   Host: $DB_HOST:$DB_PORT"
echo "   Database: $DB_NAME"
echo "   User: $DB_USER"
echo ""

# Option 1: Run SQL script directly
echo "🔧 Option 1: Run SQL fix script directly"
echo "========================================"

# Check if psql is available
if command -v psql &> /dev/null; then
    echo "✅ PostgreSQL client found"
    
    read -p "❓ Do you want to run the SQL fix script? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 Running SQL fix script..."
        
        PGPASSWORD=$DB_PASSWORD psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -f scripts/fix_database_integrity.sql
        
        if [ $? -eq 0 ]; then
            echo "✅ SQL fix script completed successfully!"
        else
            echo "❌ SQL fix script failed!"
            exit 1
        fi
    fi
else
    echo "⚠️  PostgreSQL client (psql) not found. Skipping SQL option."
fi

echo ""

# Option 2: Run Go fix tool
echo "🔧 Option 2: Run Go fix tool"
echo "============================"

read -p "❓ Do you want to run the Go fix tool? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Building and running Go fix tool..."
    
    # Build the fix tool
    go build -o tmp/fix-db ./cmd/fix-db
    
    if [ $? -eq 0 ]; then
        echo "✅ Fix tool built successfully"
        
        # Run the fix tool
        ./tmp/fix-db
        
        if [ $? -eq 0 ]; then
            echo "✅ Go fix tool completed successfully!"
        else
            echo "❌ Go fix tool failed!"
            exit 1
        fi
    else
        echo "❌ Failed to build fix tool!"
        exit 1
    fi
fi

echo ""

# Option 3: Try running the main application
echo "🔧 Option 3: Test main application"
echo "================================="

read -p "❓ Do you want to test the main application now? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 Testing main application..."
    
    # Build the main application
    go build -o tmp/main ./cmd/server
    
    if [ $? -eq 0 ]; then
        echo "✅ Main application built successfully"
        echo "🚀 Starting application (press Ctrl+C to stop)..."
        
        # Run the main application
        ./tmp/main
    else
        echo "❌ Failed to build main application!"
        exit 1
    fi
fi

echo ""
echo "🎉 Database fix script completed!"
echo "✅ Your database should now be ready for migrations."
