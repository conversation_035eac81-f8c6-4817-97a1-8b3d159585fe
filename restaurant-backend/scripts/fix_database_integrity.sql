-- Database integrity fix script
-- Run this script to fix foreign key constraint issues

BEGIN;

-- Step 1: Check current state
SELECT 'Current state analysis:' as step;

-- Count orphaned shops
SELECT 
    COUNT(*) as orphaned_shops_count,
    'Shops with invalid owner_id' as description
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL;

-- Count shops with NULL owner_id
SELECT 
    COUNT(*) as null_owner_shops_count,
    'Shops with NULL owner_id' as description
FROM shops s
WHERE s.owner_id IS NULL;

-- Step 2: Create system user if it doesn't exist
INSERT INTO users (
    id, 
    email, 
    first_name, 
    last_name, 
    password_hash,
    status,
    created_at, 
    updated_at
)
VALUES (
    '00000000-0000-0000-0000-000000000001'::uuid,
    '<EMAIL>',
    'System',
    'User',
    '$2a$10$dummy.hash.for.system.user.placeholder.value',
    'active',
    NOW(),
    NOW()
) 
ON CONFLICT (id) DO NOTHING;

-- Step 3: Fix orphaned shops (shops with owner_id that don't exist in users)
UPDATE shops 
SET 
    owner_id = '00000000-0000-0000-0000-000000000001'::uuid,
    updated_at = NOW()
WHERE owner_id IS NOT NULL 
AND owner_id NOT IN (SELECT id FROM users);

-- Step 4: Fix shops with NULL owner_id
UPDATE shops 
SET 
    owner_id = '00000000-0000-0000-0000-000000000001'::uuid,
    updated_at = NOW()
WHERE owner_id IS NULL;

-- Step 5: Verify the fix
SELECT 'Verification after fix:' as step;

-- Should return 0
SELECT 
    COUNT(*) as remaining_orphaned_shops,
    'Should be 0' as expected
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL;

-- Should return 0
SELECT 
    COUNT(*) as remaining_null_owners,
    'Should be 0' as expected
FROM shops s
WHERE s.owner_id IS NULL;

-- Show all shops and their owners
SELECT 
    s.id as shop_id,
    s.name as shop_name,
    s.owner_id,
    u.email as owner_email,
    u.first_name || ' ' || u.last_name as owner_name
FROM shops s
JOIN users u ON s.owner_id = u.id
ORDER BY s.created_at;

-- Step 6: Now the foreign key constraint can be safely added
-- This will be done by GORM during migration
SELECT 'Foreign key constraint can now be safely added' as final_step;

COMMIT;

-- If you want to manually add the constraint (optional, GORM will do this):
-- ALTER TABLE shops ADD CONSTRAINT fk_shops_owner FOREIGN KEY (owner_id) REFERENCES users(id);
