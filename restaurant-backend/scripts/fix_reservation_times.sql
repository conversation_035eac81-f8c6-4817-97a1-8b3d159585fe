-- Fix malformed reservation times in the database
-- This script updates reservations where reservation_time has invalid dates like 0000-01-01 or 0000-01-02

-- Update reservations with malformed times by combining reservation_date with the time portion
UPDATE reservations 
SET reservation_time = (
    -- Extract the date from reservation_date and combine with time from reservation_time
    DATE(reservation_date) + EXTRACT(HOUR FROM reservation_time) * INTERVAL '1 hour' + 
    EXTRACT(MINUTE FROM reservation_time) * INTERVAL '1 minute'
)
WHERE 
    -- Only update records where reservation_time has invalid dates
    DATE(reservation_time) < '1900-01-01'
    OR DATE(reservation_time) = '0000-01-01'
    OR DATE(reservation_time) = '0000-01-02';

-- Verify the fix by showing updated records
SELECT 
    id,
    customer_name,
    reservation_date,
    reservation_time,
    DATE(reservation_date) as date_part,
    TIME(reservation_time) as time_part
FROM reservations 
WHERE updated_at >= NOW() - INTERVAL '1 minute'
ORDER BY updated_at DESC;
