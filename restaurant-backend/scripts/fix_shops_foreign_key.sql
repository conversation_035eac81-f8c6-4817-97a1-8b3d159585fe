-- Migration to fix shops foreign key constraint
-- Run this before adding the foreign key constraint

BEGIN;

-- Option 1: Create a default system user for orphaned shops
INSERT INTO users (id, email, name, password_hash, created_at, updated_at)
VALUES (
    'system-user-id', 
    '<EMAIL>', 
    'System User', 
    '$2a$10$dummy.hash.for.system.user', 
    NOW(), 
    NOW()
) ON CONFLICT (id) DO NOTHING;

-- Option 2: Update orphaned shops to use the system user
UPDATE shops 
SET owner_id = 'system-user-id'
WHERE owner_id IS NOT NULL 
AND owner_id NOT IN (SELECT id FROM users);

-- Option 3: Set NULL owner_id for shops without valid owners (if your schema allows)
-- UPDATE shops 
-- SET owner_id = NULL
-- WHERE owner_id IS NOT NULL 
-- AND owner_id NOT IN (SELECT id FROM users);

-- Option 4: Delete orphaned shops (use with caution!)
-- DELETE FROM shops 
-- WHERE owner_id IS NOT NULL 
-- AND owner_id NOT IN (SELECT id FROM users);

-- Verify the fix
SELECT 
    COUNT(*) as remaining_orphaned_shops
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL;

COMMIT;
