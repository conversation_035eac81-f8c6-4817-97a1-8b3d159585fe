-- Simple database fix for foreign key constraint issues
-- This script fixes the shops table foreign key constraint problem

BEGIN;

-- Step 1: Check current state
SELECT 'Current state analysis:' as step;

-- Show existing users
SELECT 'Existing users:' as info;
SELECT id, email, first_name, last_name FROM users ORDER BY created_at LIMIT 5;

-- Show existing merchants  
SELECT 'Existing merchants:' as info;
SELECT id, name, email FROM merchants ORDER BY created_at LIMIT 5;

-- Show problematic shops
SELECT 'Problematic shops:' as info;
SELECT 
    s.id as shop_id,
    s.name as shop_name,
    s.owner_id,
    'Missing user' as issue
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL
LIMIT 10;

-- Step 2: Get the first existing user to use as system owner
DO $$
DECLARE
    first_user_id UUID;
    orphaned_count INTEGER;
BEGIN
    -- Get the first existing user
    SELECT id INTO first_user_id FROM users ORDER BY created_at LIMIT 1;
    
    IF first_user_id IS NOT NULL THEN
        RAISE NOTICE 'Using existing user % as system owner', first_user_id;
        
        -- Count orphaned shops before fix
        SELECT COUNT(*) INTO orphaned_count
        FROM shops s
        LEFT JOIN users u ON s.owner_id = u.id
        WHERE u.id IS NULL AND s.owner_id IS NOT NULL;
        
        RAISE NOTICE 'Found % orphaned shops', orphaned_count;
        
        -- Fix orphaned shops by assigning them to the first user
        UPDATE shops 
        SET 
            owner_id = first_user_id,
            updated_at = NOW()
        WHERE owner_id IS NOT NULL 
        AND owner_id NOT IN (SELECT id FROM users);
        
        GET DIAGNOSTICS orphaned_count = ROW_COUNT;
        RAISE NOTICE 'Fixed % orphaned shops', orphaned_count;
        
        -- Fix shops with NULL owner_id
        UPDATE shops 
        SET 
            owner_id = first_user_id,
            updated_at = NOW()
        WHERE owner_id IS NULL;
        
        GET DIAGNOSTICS orphaned_count = ROW_COUNT;
        RAISE NOTICE 'Fixed % shops with NULL owner_id', orphaned_count;
        
    ELSE
        RAISE NOTICE 'No existing users found - cannot fix orphaned shops';
    END IF;
END $$;

-- Step 3: Verification
SELECT 'Verification after fix:' as step;

-- Check remaining orphaned shops
SELECT 
    COUNT(*) as remaining_orphaned_shops,
    'Should be 0' as expected
FROM shops s
LEFT JOIN users u ON s.owner_id = u.id
WHERE u.id IS NULL AND s.owner_id IS NOT NULL;

-- Check remaining NULL owners
SELECT 
    COUNT(*) as remaining_null_owners,
    'Should be 0' as expected
FROM shops s
WHERE s.owner_id IS NULL;

-- Show final state
SELECT 'Final shop-owner relationships:' as info;
SELECT 
    s.id as shop_id,
    s.name as shop_name,
    s.owner_id,
    u.email as owner_email,
    u.first_name || ' ' || u.last_name as owner_name
FROM shops s
JOIN users u ON s.owner_id = u.id
ORDER BY s.created_at
LIMIT 10;

COMMIT;

-- Final message
SELECT 'SUCCESS: All shops now have valid owner references!' as result;
