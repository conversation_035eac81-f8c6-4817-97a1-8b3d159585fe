-- Supabase Database Integrity Fix
-- Run this in your Supabase SQL Editor

-- Step 1: Check current state
SELECT 'Current state analysis:' as step;

-- Check if tables exist
SELECT 
    table_name,
    'Table exists' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('shops', 'users');

-- Count orphaned shops (if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops' AND table_schema = 'public') 
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        
        RAISE NOTICE 'Checking for orphaned shops...';
        
        -- Show orphaned shops count
        PERFORM (
            SELECT COUNT(*) 
            FROM shops s
            LEFT JOIN users u ON s.owner_id = u.id
            WHERE u.id IS NULL AND s.owner_id IS NOT NULL
        );
        
    ELSE
        RAISE NOTICE 'Tables do not exist yet - no fix needed';
    END IF;
END $$;

-- Step 2: Create system user (only if users table exists)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        
        -- Create system user
        INSERT INTO users (
            id, 
            email, 
            first_name, 
            last_name, 
            password_hash,
            status,
            created_at, 
            updated_at
        )
        VALUES (
            '00000000-0000-0000-0000-000000000001'::uuid,
            '<EMAIL>',
            'System',
            'User',
            '$2a$10$dummy.hash.for.system.user.placeholder.value',
            'active',
            NOW(),
            NOW()
        ) 
        ON CONFLICT (id) DO NOTHING;
        
        RAISE NOTICE 'System user created or already exists';
        
    END IF;
END $$;

-- Step 3: Fix orphaned shops (only if both tables exist)
DO $$
DECLARE
    orphaned_count INTEGER;
    null_owner_count INTEGER;
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops' AND table_schema = 'public') 
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        
        -- Count orphaned shops before fix
        SELECT COUNT(*) INTO orphaned_count
        FROM shops s
        LEFT JOIN users u ON s.owner_id = u.id
        WHERE u.id IS NULL AND s.owner_id IS NOT NULL;
        
        -- Count NULL owner shops before fix
        SELECT COUNT(*) INTO null_owner_count
        FROM shops s
        WHERE s.owner_id IS NULL;
        
        RAISE NOTICE 'Found % orphaned shops and % shops with NULL owner_id', orphaned_count, null_owner_count;
        
        -- Fix orphaned shops
        UPDATE shops 
        SET 
            owner_id = '00000000-0000-0000-0000-000000000001'::uuid,
            updated_at = NOW()
        WHERE owner_id IS NOT NULL 
        AND owner_id NOT IN (SELECT id FROM users);
        
        -- Fix shops with NULL owner_id
        UPDATE shops 
        SET 
            owner_id = '00000000-0000-0000-0000-000000000001'::uuid,
            updated_at = NOW()
        WHERE owner_id IS NULL;
        
        RAISE NOTICE 'Fixed orphaned shops and NULL owner_id shops';
        
    END IF;
END $$;

-- Step 4: Verification
DO $$
DECLARE
    remaining_orphaned INTEGER;
    remaining_null INTEGER;
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops' AND table_schema = 'public') 
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        
        -- Check remaining orphaned shops
        SELECT COUNT(*) INTO remaining_orphaned
        FROM shops s
        LEFT JOIN users u ON s.owner_id = u.id
        WHERE u.id IS NULL AND s.owner_id IS NOT NULL;
        
        -- Check remaining NULL owners
        SELECT COUNT(*) INTO remaining_null
        FROM shops s
        WHERE s.owner_id IS NULL;
        
        RAISE NOTICE 'Verification: % orphaned shops remaining, % NULL owners remaining', remaining_orphaned, remaining_null;
        
        IF remaining_orphaned = 0 AND remaining_null = 0 THEN
            RAISE NOTICE 'SUCCESS: All shops now have valid owner references!';
        ELSE
            RAISE NOTICE 'WARNING: Some issues remain - manual intervention may be needed';
        END IF;
        
    END IF;
END $$;

-- Step 5: Show final state
SELECT 'Final verification:' as step;

-- Show all shops and their owners (if tables exist)
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'shops' AND table_schema = 'public') 
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'users' AND table_schema = 'public') THEN
        
        -- This will show the results in the query result
        RAISE NOTICE 'Check the query results below for shop-owner relationships';
        
    END IF;
END $$;

-- Final query to show results (only runs if tables exist)
SELECT 
    s.id as shop_id,
    s.name as shop_name,
    s.owner_id,
    u.email as owner_email,
    u.first_name || ' ' || u.last_name as owner_name,
    CASE 
        WHEN u.email = '<EMAIL>' THEN 'System User (Fixed)'
        ELSE 'Regular User'
    END as user_type
FROM shops s
JOIN users u ON s.owner_id = u.id
ORDER BY s.created_at
LIMIT 10;
