#!/bin/bash

# Restaurant API Test Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="http://localhost:8080"
TIMEOUT=5

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Restaurant API Test Suite${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Function to test an endpoint
test_endpoint() {
    local method=$1
    local endpoint=$2
    local expected_status=$3
    local description=$4
    local data=$5
    
    print_status "Testing: $description"
    
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/api_response.json --connect-timeout $TIMEOUT"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        curl_cmd="$curl_cmd -X POST -H 'Content-Type: application/json' -d '$data'"
    elif [ "$method" = "PUT" ] && [ -n "$data" ]; then
        curl_cmd="$curl_cmd -X PUT -H 'Content-Type: application/json' -d '$data'"
    elif [ "$method" = "DELETE" ]; then
        curl_cmd="$curl_cmd -X DELETE"
    fi
    
    curl_cmd="$curl_cmd $API_BASE_URL$endpoint"
    
    local status_code
    status_code=$(eval $curl_cmd 2>/dev/null || echo "000")
    
    if [ "$status_code" = "$expected_status" ]; then
        print_success "✓ $description - Status: $status_code"
        return 0
    else
        print_error "✗ $description - Expected: $expected_status, Got: $status_code"
        if [ -f /tmp/api_response.json ]; then
            echo "Response: $(cat /tmp/api_response.json)"
        fi
        return 1
    fi
}

# Function to wait for server to be ready
wait_for_server() {
    print_status "Waiting for server to be ready..."
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s --connect-timeout 2 "$API_BASE_URL/health" >/dev/null 2>&1; then
            print_success "Server is ready!"
            return 0
        fi
        
        echo -n "."
        sleep 1
        attempt=$((attempt + 1))
    done
    
    print_error "Server failed to start within $max_attempts seconds"
    return 1
}

# Function to run basic health checks
test_health_endpoints() {
    echo ""
    print_status "=== Testing Health Endpoints ==="
    
    test_endpoint "GET" "/health" "200" "Health check endpoint"
    test_endpoint "GET" "/ready" "200" "Readiness check endpoint"
}

# Function to test authentication endpoints
test_auth_endpoints() {
    echo ""
    print_status "=== Testing Authentication Endpoints ==="
    
    # Test login endpoint (should fail without proper credentials)
    local login_data='{"email":"<EMAIL>","password":"wrongpassword"}'
    test_endpoint "POST" "/api/v1/auth/login" "401" "Login with invalid credentials" "$login_data"
    
    # Test register endpoint
    local register_data='{"email":"<EMAIL>","password":"password123","first_name":"Test","last_name":"User"}'
    test_endpoint "POST" "/api/v1/auth/register" "201" "User registration" "$register_data"
}

# Function to test order endpoints (without authentication for now)
test_order_endpoints() {
    echo ""
    print_status "=== Testing Order Endpoints (Placeholder) ==="
    
    # These will return 401 without authentication, which is expected
    test_endpoint "GET" "/api/v1/merchants/123e4567-e89b-12d3-a456-426614174000/branches/123e4567-e89b-12d3-a456-426614174001/orders" "401" "Get orders (no auth)"
}

# Function to test documentation endpoints
test_docs_endpoints() {
    echo ""
    print_status "=== Testing Documentation Endpoints ==="
    
    test_endpoint "GET" "/docs/index.html" "200" "Swagger documentation"
    test_endpoint "GET" "/" "301" "Root redirect to docs"
}

# Function to run all tests
run_all_tests() {
    local failed_tests=0
    
    # Wait for server to be ready
    if ! wait_for_server; then
        print_error "Cannot connect to server. Make sure the API is running on $API_BASE_URL"
        exit 1
    fi
    
    # Run test suites
    test_health_endpoints || failed_tests=$((failed_tests + 1))
    test_auth_endpoints || failed_tests=$((failed_tests + 1))
    test_order_endpoints || failed_tests=$((failed_tests + 1))
    test_docs_endpoints || failed_tests=$((failed_tests + 1))
    
    # Summary
    echo ""
    print_status "=== Test Summary ==="
    if [ $failed_tests -eq 0 ]; then
        print_success "All test suites completed successfully!"
    else
        print_error "$failed_tests test suite(s) had failures"
        exit 1
    fi
}

# Function to test a single endpoint
test_single() {
    local endpoint=$1
    if [ -z "$endpoint" ]; then
        print_error "Please provide an endpoint to test"
        echo "Usage: $0 single /health"
        exit 1
    fi
    
    wait_for_server
    test_endpoint "GET" "$endpoint" "200" "Single endpoint test"
}

# Function to show help
show_help() {
    echo "Usage: $0 [OPTION] [ENDPOINT]"
    echo ""
    echo "Options:"
    echo "  all           Run all test suites (default)"
    echo "  health        Test health endpoints only"
    echo "  auth          Test authentication endpoints only"
    echo "  orders        Test order endpoints only"
    echo "  docs          Test documentation endpoints only"
    echo "  single        Test a single endpoint"
    echo "  help          Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0              # Run all tests"
    echo "  $0 health       # Test health endpoints only"
    echo "  $0 single /health  # Test single endpoint"
    echo ""
    echo "Environment Variables:"
    echo "  API_BASE_URL    Base URL for the API (default: http://localhost:8080)"
    echo "  TIMEOUT         Request timeout in seconds (default: 5)"
}

# Main function
main() {
    print_header
    
    # Override defaults with environment variables if set
    API_BASE_URL=${API_BASE_URL:-"http://localhost:8080"}
    TIMEOUT=${TIMEOUT:-5}
    
    print_status "Testing API at: $API_BASE_URL"
    print_status "Request timeout: ${TIMEOUT}s"
    echo ""
    
    case "${1:-all}" in
        "all")
            run_all_tests
            ;;
        "health")
            wait_for_server
            test_health_endpoints
            ;;
        "auth")
            wait_for_server
            test_auth_endpoints
            ;;
        "orders")
            wait_for_server
            test_order_endpoints
            ;;
        "docs")
            wait_for_server
            test_docs_endpoints
            ;;
        "single")
            test_single "$2"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
}

# Cleanup function
cleanup() {
    rm -f /tmp/api_response.json
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function with all arguments
main "$@"
