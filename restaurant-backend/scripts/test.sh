#!/bin/bash

# Test script for the restaurant backend
set -e

echo "🧪 Running Restaurant Backend Tests"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✓${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

print_error() {
    echo -e "${RED}✗${NC} $1"
}

# Check if Go is installed
if ! command -v go &> /dev/null; then
    print_error "Go is not installed. Please install Go first."
    exit 1
fi

print_status "Go is installed: $(go version)"

# Set test environment variables
export GIN_MODE=test
export LOG_LEVEL=error

# Clean test cache
print_status "Cleaning test cache..."
go clean -testcache

# Download dependencies
print_status "Downloading dependencies..."
go mod download

# Verify dependencies
print_status "Verifying dependencies..."
go mod verify

# Run tests with coverage
print_status "Running tests with coverage..."
go test -v -race -coverprofile=coverage.out ./...

# Check if tests passed
if [ $? -eq 0 ]; then
    print_status "All tests passed!"
    
    # Generate coverage report
    if [ -f coverage.out ]; then
        print_status "Generating coverage report..."
        go tool cover -html=coverage.out -o coverage.html
        
        # Show coverage summary
        echo ""
        echo "📊 Coverage Summary:"
        echo "==================="
        go tool cover -func=coverage.out | tail -1
        
        print_status "Coverage report generated: coverage.html"
    fi
else
    print_error "Tests failed!"
    exit 1
fi

# Run linting if golangci-lint is available
if command -v golangci-lint &> /dev/null; then
    print_status "Running linter..."
    golangci-lint run
    
    if [ $? -eq 0 ]; then
        print_status "Linting passed!"
    else
        print_warning "Linting found issues (non-blocking)"
    fi
else
    print_warning "golangci-lint not found. Skipping linting."
    print_warning "Install with: go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest"
fi

# Check for security vulnerabilities if govulncheck is available
if command -v govulncheck &> /dev/null; then
    print_status "Running security vulnerability check..."
    govulncheck ./...
    
    if [ $? -eq 0 ]; then
        print_status "No security vulnerabilities found!"
    else
        print_warning "Security vulnerabilities found (check output above)"
    fi
else
    print_warning "govulncheck not found. Skipping security check."
    print_warning "Install with: go install golang.org/x/vuln/cmd/govulncheck@latest"
fi

# Build the application to ensure it compiles
print_status "Building application..."
go build -o bin/restaurant-backend ./cmd/server

if [ $? -eq 0 ]; then
    print_status "Build successful!"
    rm -f bin/restaurant-backend  # Clean up
else
    print_error "Build failed!"
    exit 1
fi

echo ""
echo "🎉 All checks completed successfully!"
echo ""
echo "📋 Summary:"
echo "==========="
echo "✓ Dependencies verified"
echo "✓ Tests passed with coverage"
echo "✓ Application builds successfully"

if command -v golangci-lint &> /dev/null; then
    echo "✓ Linting completed"
fi

if command -v govulncheck &> /dev/null; then
    echo "✓ Security check completed"
fi

echo ""
echo "🚀 Ready for deployment!"
