<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .sent {
            background-color: #e3f2fd;
            text-align: right;
        }
        .received {
            background-color: #f3e5f5;
        }
        .system {
            background-color: #fff3e0;
            font-style: italic;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .connect { background-color: #4caf50; color: white; }
        .disconnect { background-color: #f44336; color: white; }
        .send { background-color: #2196f3; color: white; }
        input, select {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>WebSocket Test Client</h1>
    
    <div class="container">
        <h3>Connection</h3>
        <div id="status" class="status disconnected">Disconnected</div>
        <input type="text" id="wsUrl" value="ws://localhost:8200/ws" placeholder="WebSocket URL">
        <button class="connect" onclick="connect()">Connect</button>
        <button class="disconnect" onclick="disconnect()">Disconnect</button>
    </div>

    <div class="container">
        <h3>Send Message</h3>
        <select id="messageType">
            <option value="ping">Ping</option>
            <option value="subscribe">Subscribe</option>
            <option value="unsubscribe">Unsubscribe</option>
            <option value="order_status_request">Order Status Request</option>
            <option value="table_status_request">Table Status Request</option>
        </select>
        <input type="text" id="messageData" placeholder="Message data (JSON)" value='{"channel": "notifications"}'>
        <button class="send" onclick="sendMessage()">Send Message</button>
    </div>

    <div class="container">
        <h3>Messages</h3>
        <div id="messages" class="messages"></div>
        <button onclick="clearMessages()">Clear Messages</button>
    </div>

    <script>
        let ws = null;
        const messagesDiv = document.getElementById('messages');
        const statusDiv = document.getElementById('status');

        function updateStatus(connected) {
            if (connected) {
                statusDiv.textContent = 'Connected';
                statusDiv.className = 'status connected';
            } else {
                statusDiv.textContent = 'Disconnected';
                statusDiv.className = 'status disconnected';
            }
        }

        function addMessage(message, type = 'received') {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            messageDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong>: ${message}`;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function connect() {
            const url = document.getElementById('wsUrl').value;
            
            try {
                ws = new WebSocket(url);
                
                ws.onopen = function(event) {
                    updateStatus(true);
                    addMessage('Connected to WebSocket server', 'system');
                };
                
                ws.onmessage = function(event) {
                    addMessage(`Received: ${event.data}`, 'received');
                };
                
                ws.onclose = function(event) {
                    updateStatus(false);
                    addMessage(`Connection closed: ${event.code} - ${event.reason}`, 'system');
                };
                
                ws.onerror = function(error) {
                    addMessage(`WebSocket error: ${error}`, 'system');
                };
                
            } catch (error) {
                addMessage(`Connection error: ${error.message}`, 'system');
            }
        }

        function disconnect() {
            if (ws) {
                ws.close();
                ws = null;
            }
        }

        function sendMessage() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                addMessage('Not connected to WebSocket server', 'system');
                return;
            }

            const type = document.getElementById('messageType').value;
            const dataInput = document.getElementById('messageData').value;
            
            let data = {};
            if (dataInput.trim()) {
                try {
                    data = JSON.parse(dataInput);
                } catch (error) {
                    addMessage(`Invalid JSON data: ${error.message}`, 'system');
                    return;
                }
            }

            const message = {
                type: type,
                data: data,
                timestamp: Math.floor(Date.now() / 1000)
            };

            const messageStr = JSON.stringify(message);
            ws.send(messageStr);
            addMessage(`Sent: ${messageStr}`, 'sent');
        }

        function clearMessages() {
            messagesDiv.innerHTML = '';
        }

        // Test connection on page load
        window.onload = function() {
            addMessage('WebSocket test client loaded. Click Connect to start.', 'system');
        };
    </script>
</body>
</html>
