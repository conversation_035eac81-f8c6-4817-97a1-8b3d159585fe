package test

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/services"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/suite"
)

// AnalyticsInventoryTestSuite contains tests for analytics and inventory features
type AnalyticsInventoryTestSuite struct {
	BaseTestSuite
	testBranchID uuid.UUID
	testUserID   uuid.UUID
}

// SetupTest sets up test data for each test
func (suite *AnalyticsInventoryTestSuite) SetupTest() {
	suite.BaseTestSuite.SetupTest()
	
	// Create test branch
	branch := &models.Branch{
		MerchantID:  suite.testMerchantID,
		Name:        "Test Branch Analytics",
		Address:     "123 Analytics St",
		Phone:       "+1234567890",
		Email:       "<EMAIL>",
		IsActive:    true,
	}
	err := suite.db.Create(branch).Error
	assert.NoError(suite.T(), err)
	suite.testBranchID = branch.ID

	// Create test user
	user := &models.User{
		BranchID:    suite.testBranchID,
		Name:        "Test Analytics User",
		Email:       "<EMAIL>",
		Phone:       "+1234567890",
		Role:        "manager",
		Status:      "active",
		Password:    "hashedpassword",
	}
	err = suite.db.Create(user).Error
	assert.NoError(suite.T(), err)
	suite.testUserID = user.ID
}

// TestAnalyticsDashboard tests the analytics dashboard endpoint
func (suite *AnalyticsInventoryTestSuite) TestAnalyticsDashboard() {
	// Create some test sales metrics
	salesMetric := &models.SalesMetric{
		BranchID:   suite.testBranchID,
		Date:       time.Now().Truncate(24 * time.Hour),
		Hour:       12,
		MetricType: models.SalesMetricRevenue,
		Value:      1500.00,
		Count:      1,
	}
	err := suite.db.Create(salesMetric).Error
	assert.NoError(suite.T(), err)

	// Test GET /analytics/dashboard
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/analytics/dashboard", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var dashboard services.DashboardData
	err = json.Unmarshal(w.Body.Bytes(), &dashboard)
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), dashboard.TodayStats)
}

// TestSalesReport tests the sales report generation
func (suite *AnalyticsInventoryTestSuite) TestSalesReport() {
	// Create test data
	startDate := time.Now().AddDate(0, 0, -7)
	endDate := time.Now()

	// Test GET /analytics/sales-report
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/analytics/sales-report?start_date=%s&end_date=%s",
		suite.testMerchantID, suite.testBranchID,
		startDate.Format("2006-01-02"), endDate.Format("2006-01-02"))
	
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var report services.SalesReport
	err := json.Unmarshal(w.Body.Bytes(), &report)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), startDate.Format("2006-01-02")+" to "+endDate.Format("2006-01-02"), report.Period)
}

// TestInventoryDashboard tests the inventory dashboard endpoint
func (suite *AnalyticsInventoryTestSuite) TestInventoryDashboard() {
	// Create test supplier
	supplier := &models.Supplier{
		Name:           "Test Supplier",
		ContactPerson:  "John Doe",
		Email:          "<EMAIL>",
		Phone:          "+1234567890",
		Status:         models.SupplierStatusActive,
	}
	err := suite.db.Create(supplier).Error
	assert.NoError(suite.T(), err)

	// Create test ingredient
	ingredient := &models.Ingredient{
		SupplierID:    &supplier.ID,
		Name:          "Test Ingredient",
		Category:      "Vegetables",
		Unit:          "kg",
		CostPerUnit:   5.50,
		MinStockLevel: 10.0,
		MaxStockLevel: 100.0,
		ReorderPoint:  20.0,
		IsActive:      true,
	}
	err = suite.db.Create(ingredient).Error
	assert.NoError(suite.T(), err)

	// Create test inventory item
	inventoryItem := &models.InventoryItem{
		BranchID:       suite.testBranchID,
		IngredientID:   ingredient.ID,
		CurrentStock:   50.0,
		ReservedStock:  5.0,
		AvailableStock: 45.0,
		Status:         models.InventoryStatusAvailable,
		CostPerUnit:    5.50,
		TotalValue:     275.0,
	}
	err = suite.db.Create(inventoryItem).Error
	assert.NoError(suite.T(), err)

	// Test GET /inventory/dashboard
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/inventory/dashboard", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var dashboard services.InventoryDashboard
	err = json.Unmarshal(w.Body.Bytes(), &dashboard)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, dashboard.TotalItems)
	assert.Equal(suite.T(), 275.0, dashboard.TotalValue)
}

// TestCreateIngredient tests ingredient creation
func (suite *AnalyticsInventoryTestSuite) TestCreateIngredient() {
	// Create test supplier first
	supplier := &models.Supplier{
		Name:   "Test Supplier",
		Status: models.SupplierStatusActive,
	}
	err := suite.db.Create(supplier).Error
	assert.NoError(suite.T(), err)

	// Test POST /ingredients
	ingredientReq := services.CreateIngredientRequest{
		Name:          "New Test Ingredient",
		Category:      "Dairy",
		Unit:          "liter",
		CostPerUnit:   3.25,
		MinStockLevel: 5.0,
		MaxStockLevel: 50.0,
		ReorderPoint:  10.0,
		SupplierID:    &supplier.ID,
		AllergenInfo:  []string{"milk"},
	}

	jsonData, _ := json.Marshal(ingredientReq)
	req, _ := http.NewRequest("POST", "/api/v1/ingredients", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var ingredient models.Ingredient
	err = json.Unmarshal(w.Body.Bytes(), &ingredient)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "New Test Ingredient", ingredient.Name)
	assert.Equal(suite.T(), "Dairy", ingredient.Category)
}

// TestUpdateStock tests stock level updates
func (suite *AnalyticsInventoryTestSuite) TestUpdateStock() {
	// Create test ingredient and inventory item
	ingredient := &models.Ingredient{
		Name:          "Stock Test Ingredient",
		Category:      "Test",
		Unit:          "kg",
		CostPerUnit:   10.0,
		MinStockLevel: 5.0,
		MaxStockLevel: 100.0,
		ReorderPoint:  15.0,
		IsActive:      true,
	}
	err := suite.db.Create(ingredient).Error
	assert.NoError(suite.T(), err)

	inventoryItem := &models.InventoryItem{
		BranchID:       suite.testBranchID,
		IngredientID:   ingredient.ID,
		CurrentStock:   20.0,
		ReservedStock:  0.0,
		AvailableStock: 20.0,
		Status:         models.InventoryStatusAvailable,
		CostPerUnit:    10.0,
		TotalValue:     200.0,
	}
	err = suite.db.Create(inventoryItem).Error
	assert.NoError(suite.T(), err)

	// Test PUT /inventory/stock (stock in)
	stockReq := services.UpdateStockRequest{
		IngredientID: ingredient.ID,
		MovementType: models.MovementTypeIn,
		Quantity:     30.0,
		Reason:       "Delivery received",
		CostPerUnit:  10.0,
	}

	jsonData, _ := json.Marshal(stockReq)
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/inventory/stock", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var updatedItem models.InventoryItem
	err = json.Unmarshal(w.Body.Bytes(), &updatedItem)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 50.0, updatedItem.CurrentStock) // 20 + 30
}

// TestCreatePurchaseOrder tests purchase order creation
func (suite *AnalyticsInventoryTestSuite) TestCreatePurchaseOrder() {
	// Create test supplier and ingredient
	supplier := &models.Supplier{
		Name:   "PO Test Supplier",
		Status: models.SupplierStatusActive,
	}
	err := suite.db.Create(supplier).Error
	assert.NoError(suite.T(), err)

	ingredient := &models.Ingredient{
		Name:        "PO Test Ingredient",
		Category:    "Test",
		Unit:        "kg",
		CostPerUnit: 15.0,
		IsActive:    true,
	}
	err = suite.db.Create(ingredient).Error
	assert.NoError(suite.T(), err)

	// Test POST /inventory/purchase-orders
	poReq := services.CreatePurchaseOrderRequest{
		SupplierID: supplier.ID,
		Items: []services.CreatePurchaseOrderItemRequest{
			{
				IngredientID: ingredient.ID,
				Quantity:     20.0,
				UnitPrice:    15.0,
			},
		},
		Notes: "Test purchase order",
	}

	jsonData, _ := json.Marshal(poReq)
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/inventory/purchase-orders", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusCreated, w.Code)

	var po models.PurchaseOrder
	err = json.Unmarshal(w.Body.Bytes(), &po)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), supplier.ID, po.SupplierID)
	assert.Equal(suite.T(), 300.0, po.Subtotal) // 20 * 15
	assert.Equal(suite.T(), models.POStatusPending, po.Status)
}

// TestLowStockItems tests low stock alerts
func (suite *AnalyticsInventoryTestSuite) TestLowStockItems() {
	// Create ingredient with low stock
	ingredient := &models.Ingredient{
		Name:          "Low Stock Ingredient",
		Category:      "Test",
		Unit:          "kg",
		CostPerUnit:   8.0,
		MinStockLevel: 20.0,
		ReorderPoint:  25.0,
		IsActive:      true,
	}
	err := suite.db.Create(ingredient).Error
	assert.NoError(suite.T(), err)

	// Create inventory item with stock below reorder point
	inventoryItem := &models.InventoryItem{
		BranchID:       suite.testBranchID,
		IngredientID:   ingredient.ID,
		CurrentStock:   15.0, // Below reorder point of 25
		AvailableStock: 15.0,
		Status:         models.InventoryStatusAvailable,
		CostPerUnit:    8.0,
		TotalValue:     120.0,
	}
	err = suite.db.Create(inventoryItem).Error
	assert.NoError(suite.T(), err)

	// Test GET /inventory/low-stock
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/inventory/low-stock", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var lowStockItems []services.LowStockAlert
	err = json.Unmarshal(w.Body.Bytes(), &lowStockItems)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), lowStockItems, 1)
	assert.Equal(suite.T(), ingredient.ID, lowStockItems[0].IngredientID)
	assert.Equal(suite.T(), "Low Stock Ingredient", lowStockItems[0].IngredientName)
}

// TestExpiringItems tests expiring items alerts
func (suite *AnalyticsInventoryTestSuite) TestExpiringItems() {
	// Create ingredient
	ingredient := &models.Ingredient{
		Name:         "Expiring Ingredient",
		Category:     "Perishable",
		Unit:         "kg",
		CostPerUnit:  12.0,
		IsPerishable: true,
		IsActive:     true,
	}
	err := suite.db.Create(ingredient).Error
	assert.NoError(suite.T(), err)

	// Create inventory item expiring in 3 days
	expiryDate := time.Now().AddDate(0, 0, 3)
	inventoryItem := &models.InventoryItem{
		BranchID:       suite.testBranchID,
		IngredientID:   ingredient.ID,
		CurrentStock:   10.0,
		AvailableStock: 10.0,
		ExpiryDate:     &expiryDate,
		Status:         models.InventoryStatusAvailable,
		CostPerUnit:    12.0,
		TotalValue:     120.0,
	}
	err = suite.db.Create(inventoryItem).Error
	assert.NoError(suite.T(), err)

	// Test GET /inventory/expiring?days=7
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/inventory/expiring?days=7", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var expiringItems []services.ExpiryAlert
	err = json.Unmarshal(w.Body.Bytes(), &expiringItems)
	assert.NoError(suite.T(), err)
	assert.Len(suite.T(), expiringItems, 1)
	assert.Equal(suite.T(), inventoryItem.ID, expiringItems[0].InventoryItemID)
	assert.Equal(suite.T(), "Expiring Ingredient", expiringItems[0].IngredientName)
	assert.Equal(suite.T(), 3, expiringItems[0].DaysUntilExpiry)
}

// TestAnalyticsExport tests analytics report export
func (suite *AnalyticsInventoryTestSuite) TestAnalyticsExport() {
	// Test POST /analytics/export
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/analytics/export?report_type=sales&format=pdf&start_date=2024-01-01&end_date=2024-01-31", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ := http.NewRequest("POST", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), http.StatusOK, w.Code)

	var exportInfo map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &exportInfo)
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Report export initiated", exportInfo["message"])
	assert.Equal(suite.T(), "sales", exportInfo["report_type"])
	assert.Equal(suite.T(), "pdf", exportInfo["format"])
}

// TestInvalidRequests tests various invalid request scenarios
func (suite *AnalyticsInventoryTestSuite) TestInvalidRequests() {
	// Test invalid branch ID
	req, _ := http.NewRequest("GET", "/api/v1/merchants/invalid-id/branches/invalid-id/analytics/dashboard", nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w := httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid date format in sales report
	url := fmt.Sprintf("/api/v1/merchants/%s/branches/%s/analytics/sales-report?start_date=invalid&end_date=invalid", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ = http.NewRequest("GET", url, nil)
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)

	// Test invalid stock update request
	invalidStockReq := map[string]interface{}{
		"ingredient_id": "invalid-id",
		"movement_type": "invalid",
		"quantity":      -10, // Negative quantity
	}

	jsonData, _ := json.Marshal(invalidStockReq)
	url = fmt.Sprintf("/api/v1/merchants/%s/branches/%s/inventory/stock", 
		suite.testMerchantID, suite.testBranchID)
	
	req, _ = http.NewRequest("PUT", url, bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+suite.authToken)
	
	w = httptest.NewRecorder()
	suite.router.ServeHTTP(w, req)
	assert.Equal(suite.T(), http.StatusBadRequest, w.Code)
}

// TestAnalyticsInventoryTestSuite runs the test suite
func TestAnalyticsInventoryTestSuite(t *testing.T) {
	suite.Run(t, new(AnalyticsInventoryTestSuite))
}
