package test

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"restaurant-backend/internal/api/routes"
	"restaurant-backend/internal/config"
	"restaurant-backend/internal/database"
	"restaurant-backend/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func setupTestRouter() (*gin.Engine, error) {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Load test configuration
	expiresIn, _ := time.ParseDuration("24h")
	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:    "test-secret",
			ExpiresIn: expiresIn,
		},
		CORS: config.CORSConfig{
			AllowedOrigins: []string{"*"},
			AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
			AllowedHeaders: []string{"*"},
		},
	}

	// Initialize test logger
	testLogger := logger.NewLogger("debug", "json")

	// Initialize in-memory database for testing
	db, err := database.InitializeTest()
	if err != nil {
		return nil, err
	}

	// Run test migrations
	if err := database.MigrateTest(db); err != nil {
		return nil, err
	}

	// Create router
	router := gin.New()

	// Setup routes
	routes.SetupRoutes(router, db, cfg, testLogger)

	return router, nil
}

func TestHealthEndpoints(t *testing.T) {
	router, err := setupTestRouter()
	require.NoError(t, err)

	tests := []struct {
		name           string
		endpoint       string
		expectedStatus int
	}{
		{
			name:           "Health check",
			endpoint:       "/health",
			expectedStatus: http.StatusOK,
		},
		{
			name:           "Ready check",
			endpoint:       "/ready",
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, _ := http.NewRequest("GET", tt.endpoint, nil)
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			assert.Equal(t, tt.expectedStatus, w.Code)
		})
	}
}

func TestShopsEndpoints(t *testing.T) {
	router, err := setupTestRouter()
	require.NoError(t, err)

	// Test data
	shopData := map[string]interface{}{
		"name":        "Test Restaurant",
		"slug":        "test-restaurant",
		"description": "A test restaurant",
		"shop_type":   "restaurant",
		"email":       "<EMAIL>",
		"phone":       "+**********",
		"address": map[string]string{
			"street":   "123 Test St",
			"city":     "Test City",
			"state":    "TS",
			"zip_code": "12345",
			"country":  "Test Country",
		},
		"cuisine_type": "italian",
		"price_range":  "$$",
		"business_hours": map[string]string{
			"monday":    "9:00-22:00",
			"tuesday":   "9:00-22:00",
			"wednesday": "9:00-22:00",
			"thursday":  "9:00-22:00",
			"friday":    "9:00-23:00",
			"saturday":  "9:00-23:00",
			"sunday":    "10:00-21:00",
		},
		"social_media": map[string]string{
			"facebook":  "https://facebook.com/testrestaurant",
			"instagram": "https://instagram.com/testrestaurant",
		},
	}

	jsonData, _ := json.Marshal(shopData)

	// Test GET /api/v1/shops (should require auth, so expect 401)
	t.Run("Get shops without auth", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/shops", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	// Test POST /api/v1/shops (should require auth, so expect 401)
	t.Run("Create shop without auth", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/api/v1/shops", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

func TestServicesEndpoints(t *testing.T) {
	router, err := setupTestRouter()
	require.NoError(t, err)

	// Test data
	serviceData := map[string]interface{}{
		"name":             "Hair Cut",
		"description":      "Professional hair cutting service",
		"category":         "hair",
		"price":            25.00,
		"duration":         30,
		"max_capacity":     1,
		"requires_staff":   true,
		"preparation_time": 5,
		"cleanup_time":     5,
	}

	jsonData, _ := json.Marshal(serviceData)

	// Test GET /api/v1/merchants/{merchantId}/services (should require auth, so expect 401)
	t.Run("Get services without auth", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/merchants/123e4567-e89b-12d3-a456-426614174000/services", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	// Test POST /api/v1/merchants/{merchantId}/services (should require auth, so expect 401)
	t.Run("Create service without auth", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/api/v1/merchants/123e4567-e89b-12d3-a456-426614174000/services", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

func TestCampaignsEndpoints(t *testing.T) {
	router, err := setupTestRouter()
	require.NoError(t, err)

	// Test data
	campaignData := map[string]interface{}{
		"name":            "Welcome Campaign",
		"description":     "Welcome new customers",
		"type":            "email",
		"content":         "Welcome to our restaurant!",
		"target_audience": "all",
		"settings": map[string]interface{}{
			"send_time":         "09:00",
			"timezone":          "UTC",
			"track_opens":       true,
			"track_clicks":      true,
			"allow_unsubscribe": true,
			"from_name":         "Test Restaurant",
		},
	}

	jsonData, _ := json.Marshal(campaignData)

	// Test GET /api/v1/merchants/{merchantId}/campaigns (should require auth, so expect 401)
	t.Run("Get campaigns without auth", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/api/v1/merchants/123e4567-e89b-12d3-a456-426614174000/campaigns", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})

	// Test POST /api/v1/merchants/{merchantId}/campaigns (should require auth, so expect 401)
	t.Run("Create campaign without auth", func(t *testing.T) {
		req, _ := http.NewRequest("POST", "/api/v1/merchants/123e4567-e89b-12d3-a456-426614174000/campaigns", bytes.NewBuffer(jsonData))
		req.Header.Set("Content-Type", "application/json")
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusUnauthorized, w.Code)
	})
}

func TestDocumentationEndpoint(t *testing.T) {
	router, err := setupTestRouter()
	require.NoError(t, err)

	// Test documentation redirect
	t.Run("Root redirect to docs", func(t *testing.T) {
		req, _ := http.NewRequest("GET", "/", nil)
		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		assert.Equal(t, http.StatusMovedPermanently, w.Code)
		assert.Contains(t, w.Header().Get("Location"), "/docs/")
	})
}
