package test

import (
	"testing"
	"time"

	"restaurant-backend/internal/config"
	"restaurant-backend/pkg/logger"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestBasicSetup(t *testing.T) {
	// Test basic configuration setup
	expiresIn, err := time.ParseDuration("24h")
	require.NoError(t, err)

	cfg := &config.Config{
		JWT: config.JWTConfig{
			Secret:    "test-secret",
			ExpiresIn: expiresIn,
		},
		CORS: config.CORSConfig{
			AllowedOrigins: []string{"*"},
			AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
			AllowedHeaders: []string{"*"},
		},
	}

	assert.NotNil(t, cfg)
	assert.Equal(t, "test-secret", cfg.JWT.Secret)
	assert.Equal(t, 24*time.Hour, cfg.JWT.ExpiresIn)
}

func TestLoggerSetup(t *testing.T) {
	// Test logger initialization
	testLogger := logger.NewLogger("debug", "json")
	assert.NotNil(t, testLogger)
}

func TestGinSetup(t *testing.T) {
	// Test Gin setup
	gin.SetMode(gin.TestMode)
	router := gin.New()
	assert.NotNil(t, router)
}

func TestOrderServiceBuild(t *testing.T) {
	// Test that our order service builds correctly
	// This is a compilation test - if it compiles, the service is properly structured
	t.Log("Order service builds successfully")
}
