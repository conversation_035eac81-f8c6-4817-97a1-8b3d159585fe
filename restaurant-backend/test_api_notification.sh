#!/bin/bash

# Test script to create a notification via API
echo "Creating a new notification via API..."

# Create notification using the slug-based API
curl -X POST "http://localhost:8080/api/v1/shops/slug/weerawat-poseeya/branches/slug/the-green-terrace/notifications" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer ********************************************************************************************************************************************************************************" \
  -H "X-User-ID: 117587654995884969073" \
  -H "X-User-Email: <EMAIL>" \
  -H "X-User-Role: user" \
  -H "X-Auth-Source: nextauth" \
  -d '{
    "title": "🚀 API Test: Real-time Notification!",
    "message": "This notification was created via API call to test real-time functionality. Timestamp: '$(date)'",
    "type": "system",
    "priority": "urgent",
    "link": "/app/restaurant/weerawat-poseeya/the-green-terrace/dashboard",
    "action_label": "View Dashboard",
    "data": {
      "test": true,
      "created_via": "api_script",
      "timestamp": "'$(date -Iseconds)'",
      "feature": "real-time notifications"
    }
  }' \
  -w "\nHTTP Status: %{http_code}\n"

echo -e "\nNotification creation request sent!"
echo "Check the notification bell in the frontend to see if it appears in real-time."
