#!/bin/bash

# <PERSON>ript to check notifications for a specific shop and branch
# Usage: ./check_notifications.sh <shop_slug> <branch_slug>

# Check if arguments are provided
if [ $# -ne 2 ]; then
    echo "Usage: $0 <shop_slug> <branch_slug>"
    echo "Example: $0 weerawat-<PERSON><PERSON>a posriya"
    exit 1
fi

SHOP_SLUG=$1
BRANCH_SLUG=$2

# Database URL from .env file
DATABASE_URL="postgresql://postgres.sqzzpwirpwdlxzuvztey:<EMAIL>:5432/postgres"

echo "📋 Checking notifications for shop: $SHOP_SLUG, branch: $BRANCH_SLUG"
echo "=================================================="

# Query notifications
psql "$DATABASE_URL" -c "
SELECT 
    type, 
    priority, 
    title, 
    is_read,
    TO_CHAR(timestamp, 'YYYY-MM-DD HH24:MI') as time
FROM notifications n
JOIN shops s ON n.shop_id = s.id
JOIN shop_branches sb ON n.branch_id = sb.id
WHERE s.slug = '$SHOP_SLUG' AND sb.slug = '$BRANCH_SLUG'
ORDER BY timestamp DESC;"

echo ""
echo "📊 Summary by type:"
psql "$DATABASE_URL" -c "
SELECT 
    type,
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE is_read = false) as unread,
    COUNT(*) FILTER (WHERE priority = 'urgent') as urgent,
    COUNT(*) FILTER (WHERE priority = 'high') as high
FROM notifications n
JOIN shops s ON n.shop_id = s.id
JOIN shop_branches sb ON n.branch_id = sb.id
WHERE s.slug = '$SHOP_SLUG' AND sb.slug = '$BRANCH_SLUG'
GROUP BY type
ORDER BY total DESC;"

echo ""
echo "🔗 View in browser: http://localhost:4000/en/app/restaurant/$SHOP_SLUG/$BRANCH_SLUG/notifications"
