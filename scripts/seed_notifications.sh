#!/bin/bash

# Script to seed notifications for a specific shop and branch
# Usage: ./seed_notifications.sh <shop_slug> <branch_slug>

# Check if arguments are provided
if [ $# -ne 2 ]; then
    echo "Usage: $0 <shop_slug> <branch_slug>"
    echo "Example: $0 weerawat-<PERSON><PERSON>a posriya"
    exit 1
fi

SHOP_SLUG=$1
BRANCH_SLUG=$2

# Database URL from .env file
DATABASE_URL="postgresql://postgres.sqzzpwirpwdlxzuvztey:<EMAIL>:5432/postgres"

echo "Seeding notifications for shop: $SHOP_SLUG, branch: $BRANCH_SLUG"

# Create temporary SQL file
TEMP_SQL=$(mktemp)

cat > "$TEMP_SQL" << EOF
-- Seed notifications for $SHOP_SLUG/$BRANCH_SLUG
DO \$\$
DECLARE
    shop_uuid UUID;
    branch_uuid UUID;
BEGIN
    -- Get shop and branch IDs
    SELECT s.id, sb.id INTO shop_uuid, branch_uuid
    FROM shops s
    JOIN shop_branches sb ON s.id = sb.shop_id
    WHERE s.slug = '$SHOP_SLUG' AND sb.slug = '$BRANCH_SLUG'
    LIMIT 1;

    -- Check if shop and branch exist
    IF shop_uuid IS NULL OR branch_uuid IS NULL THEN
        RAISE NOTICE 'Shop or branch not found with slugs: $SHOP_SLUG/$BRANCH_SLUG';
        RETURN;
    END IF;

    -- Clear existing notifications for this shop/branch (optional)
    -- DELETE FROM notifications WHERE shop_id = shop_uuid AND branch_id = branch_uuid;

    -- Insert notifications for each type
    INSERT INTO notifications (shop_id, branch_id, title, message, type, priority, is_read, link, action_label, data, timestamp) VALUES
    -- ORDER notifications
    (shop_uuid, branch_uuid, 'New Order Received', 'Order #ORD-2024-001 has been placed and requires preparation. Customer: John Smith, Total: \$45.99', 'order', 'high', false, '/orders/ORD-2024-001', 'View Order', '{"order_id": "ORD-2024-001", "customer_name": "John Smith", "amount": 45.99, "items_count": 3}'::jsonb, NOW() - INTERVAL '5 minutes'),
    (shop_uuid, branch_uuid, 'Order Ready for Pickup', 'Order #ORD-2024-002 is ready for pickup. Customer has been notified.', 'order', 'medium', false, '/orders/ORD-2024-002', 'Mark as Collected', '{"order_id": "ORD-2024-002", "customer": "Sarah Wilson", "pickup_time": "18:30"}'::jsonb, NOW() - INTERVAL '15 minutes'),
    
    -- RESERVATION notifications
    (shop_uuid, branch_uuid, 'Table Reservation Confirmed', 'New reservation for 4 people at 7:00 PM tonight. Table 5 has been reserved for the Smith family.', 'reservation', 'medium', false, '/reservations/RES-2024-001', 'View Reservation', '{"reservation_id": "RES-2024-001", "party_size": 4, "time": "19:00", "table": "T5", "customer": "Smith Family"}'::jsonb, NOW() - INTERVAL '25 minutes'),
    (shop_uuid, branch_uuid, 'Reservation Reminder', 'Upcoming reservation in 30 minutes. Table 3 for Johnson party of 6.', 'reservation', 'low', true, '/reservations/RES-2024-002', 'View Details', '{"reservation_id": "RES-2024-002", "party_size": 6, "time": "20:00", "table": "T3", "customer": "Johnson Family"}'::jsonb, NOW() - INTERVAL '2 hours'),
    
    -- REVIEW notifications
    (shop_uuid, branch_uuid, 'New Customer Review', 'Emma Thompson left a 5-star review: "Amazing food and excellent service!"', 'review', 'low', false, '/reviews/REV-2024-001', 'View Review', '{"review_id": "REV-2024-001", "customer_name": "Emma Thompson", "rating": 5, "comment": "Amazing food and excellent service!"}'::jsonb, NOW() - INTERVAL '1 hour'),
    (shop_uuid, branch_uuid, 'Review Response Needed', 'Mike Davis left a 2-star review that requires your attention and response.', 'review', 'high', false, '/reviews/REV-2024-002', 'Respond Now', '{"review_id": "REV-2024-002", "customer_name": "Mike Davis", "rating": 2, "requires_response": true}'::jsonb, NOW() - INTERVAL '3 hours'),
    
    -- SYSTEM notifications
    (shop_uuid, branch_uuid, 'Critical: Equipment Malfunction', 'Kitchen freezer temperature is rising. Current temperature: 5°C. Immediate attention required!', 'system', 'urgent', false, '/equipment/freezer-01', 'Check Equipment', '{"equipment": "freezer-01", "current_temp": 5, "normal_temp": -18, "alert_level": "critical"}'::jsonb, NOW() - INTERVAL '10 minutes'),
    (shop_uuid, branch_uuid, 'System Maintenance Scheduled', 'Scheduled maintenance will occur tonight from 2:00 AM to 4:00 AM. POS system may be temporarily unavailable.', 'system', 'medium', true, '/system/maintenance', 'View Details', '{"start_time": "02:00", "end_time": "04:00", "date": "2024-12-22", "affected_systems": ["POS", "Inventory"]}'::jsonb, NOW() - INTERVAL '4 hours'),
    
    -- STAFF notifications
    (shop_uuid, branch_uuid, 'Staff Schedule Update', 'Maria Garcia has requested a shift change for tomorrow. Please review and approve.', 'staff', 'medium', false, '/staff/schedule', 'Review Schedule', '{"staff_member": "Maria Garcia", "requested_date": "2024-12-22", "shift_type": "evening", "reason": "family event"}'::jsonb, NOW() - INTERVAL '45 minutes'),
    (shop_uuid, branch_uuid, 'Staff Time-Off Request', 'David Wilson requested time off for next Friday. Approval needed.', 'staff', 'low', true, '/staff/requests', 'Review Request', '{"staff_member": "David Wilson", "request_type": "time_off", "date": "2024-12-29", "reason": "personal"}'::jsonb, NOW() - INTERVAL '6 hours'),
    
    -- INVENTORY notifications
    (shop_uuid, branch_uuid, 'Critical: Low Inventory Alert', 'Tomatoes are running low. Only 5 units remaining. Please restock immediately!', 'inventory', 'urgent', false, '/inventory/tomatoes', 'Restock Now', '{"item": "tomatoes", "remaining": 5, "threshold": 10, "supplier": "Fresh Produce Co."}'::jsonb, NOW() - INTERVAL '30 minutes'),
    (shop_uuid, branch_uuid, 'Inventory Restock Reminder', 'Weekly inventory check due. Several items need restocking.', 'inventory', 'medium', true, '/inventory/dashboard', 'View Inventory', '{"items_low": 8, "items_critical": 2, "last_check": "2024-12-15"}'::jsonb, NOW() - INTERVAL '8 hours'),
    
    -- PAYMENT notifications
    (shop_uuid, branch_uuid, 'Payment Processing Failed', 'Payment for Order #ORD-2024-003 failed. Customer payment method was declined.', 'payment', 'high', false, '/orders/ORD-2024-003', 'Contact Customer', '{"order_id": "ORD-2024-003", "amount": 32.50, "payment_method": "Credit Card", "error": "Card Declined"}'::jsonb, NOW() - INTERVAL '20 minutes'),
    (shop_uuid, branch_uuid, 'Payment Refund Processed', 'Refund of \$28.75 has been processed for Order #ORD-2024-004.', 'payment', 'low', true, '/orders/ORD-2024-004', 'View Details', '{"order_id": "ORD-2024-004", "refund_amount": 28.75, "reason": "customer_request"}'::jsonb, NOW() - INTERVAL '5 hours'),
    
    -- PROMOTION notifications
    (shop_uuid, branch_uuid, 'Holiday Promotion Active', 'Your Christmas special promotion is now live! 20% off all desserts until December 25th.', 'promotion', 'low', false, '/promotions/christmas-2024', 'View Promotion', '{"promotion_id": "christmas-2024", "discount": 20, "category": "desserts", "end_date": "2024-12-25"}'::jsonb, NOW() - INTERVAL '1 hour'),
    (shop_uuid, branch_uuid, 'Flash Sale Starting Soon', 'Your flash sale starts in 15 minutes! 30% off lunch specials for the next 2 hours.', 'promotion', 'medium', false, '/promotions/flash-sale-lunch', 'Activate Sale', '{"promotion_id": "flash-sale-lunch", "discount": 30, "duration": "2 hours", "category": "lunch_specials"}'::jsonb, NOW() - INTERVAL '5 minutes');

    RAISE NOTICE 'Successfully inserted 16 notifications for shop: $SHOP_SLUG, branch: $BRANCH_SLUG';
END \$\$;
EOF

# Execute the SQL
psql "$DATABASE_URL" -f "$TEMP_SQL"

# Clean up
rm "$TEMP_SQL"

echo "✅ Notification seeding completed!"
echo "🔗 View notifications at: http://localhost:4000/en/app/restaurant/$SHOP_SLUG/$BRANCH_SLUG/notifications"
