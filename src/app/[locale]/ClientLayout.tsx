'use client';

import { NextIntlClientProvider } from 'next-intl';

interface ClientLayoutProps {
  children: React.ReactNode;
  locale: string;
  messages: any;
  fontClasses: string;
}

export default function ClientLayout({ children, locale, messages, fontClasses }: ClientLayoutProps) {
  return (
    <html lang={locale}>
      <body className={fontClasses}>
        <NextIntlClientProvider locale={locale} messages={messages} timeZone="Asia/Bangkok">
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
