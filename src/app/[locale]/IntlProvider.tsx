'use client';

import { NextIntlClientProvider } from 'next-intl';
import { ReactNode } from 'react';

type IntlProviderProps = {
  locale: string;
  messages: any;
  children: ReactNode;
};

export default function IntlProvider({ locale, messages, children }: IntlProviderProps) {
  return (
    <NextIntlClientProvider locale={locale} messages={messages} timeZone="Asia/Bangkok">
      {children}
    </NextIntlClientProvider>
  );
}
