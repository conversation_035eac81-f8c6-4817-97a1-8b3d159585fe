'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  useGetMerchantsQuery,
  useUpdateMerchantMutation
} from '@/lib/redux/api/endpoints/restaurant/shopApi';
import {
  merchantSchema,
  restaurantSettingsSchema,
  retailSettingsSchema,
  serviceSettingsSchema,
  digitalSettingsSchema,
  convenienceSettingsSchema
} from '@/lib/validations/merchantSchema';

// Create a schema for the edit form (without type-specific settings)
const editMerchantSchema = merchantSchema.omit({ settings: true });
type EditMerchantInput = z.infer<typeof editMerchantSchema>;

interface EditMerchantPageProps {
  params: {
    type: string;
    id: string;
  };
}

export default function EditMerchantPage({ params }: EditMerchantPageProps) {
  const { type, id } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const [updateMerchant, { isLoading: isUpdating }] = useUpdateMerchantMutation();

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Find the merchant to edit
  const merchant = merchants?.find(m => m.id === id);

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset,
    setValue
  } = useForm<EditMerchantInput>({
    resolver: zodResolver(editMerchantSchema),
    defaultValues: {
      name: '',
      email: '',
      phoneNumber: '',
      address: '',
      ownerId: '',
      status: 'pending',
      logo: '',
      type: type as any,
    }
  });

  // Update form when merchant data is loaded
  useEffect(() => {
    if (merchant) {
      reset({
        name: merchant.name,
        email: merchant.email,
        phoneNumber: merchant.phoneNumber,
        address: merchant.address,
        ownerId: merchant.ownerId,
        status: merchant.status as any,
        logo: merchant.logo || '',
        type: merchant.type as any,
      });
    }
  }, [merchant, reset]);

  // Form submission handler
  const onSubmit: SubmitHandler<EditMerchantInput> = async (data) => {
    try {
      await updateMerchant({
        id,
        data: {
          ...data,
          // Keep the existing settings
          settings: merchant?.settings || {},
        },
      }).unwrap();

      setSuccess('Merchant updated successfully');
      setError(null);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError('Failed to update merchant. Please try again.');
      setSuccess(null);
      console.error('Error updating merchant:', err);
    }
  };

  // Get type display name
  const getTypeDisplayName = () => {
    switch (type) {
      case 'restaurant':
        return 'Restaurant';
      case 'retail':
        return 'Retail Store';
      case 'service':
        return 'Service Provider';
      case 'digital':
        return 'Digital Store';
      case 'convenience':
        return 'Convenience Store';
      case 'custom':
        return 'Custom Merchant';
      default:
        return 'Merchant';
    }
  };

  if (isLoadingMerchants) {
    return <div className="p-6">Loading merchant data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Merchant not found. The merchant may have been deleted or you may not have permission to edit it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  // Verify that the merchant type matches the URL type
  if (merchant.type !== type) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Merchant type mismatch. This merchant is of type "{merchant.type}" but you're trying to edit it as a "{type}".
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/${merchant.type}/${merchant.id}/edit`} className="text-blue-600 hover:underline">
          Go to correct edit page
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/${type}/${id}`} className="text-blue-600 hover:underline">
          ← Back to merchant details
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Edit {getTypeDisplayName()}</h1>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-400">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-400">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-sm">
        <h2 className="text-lg font-semibold mb-4">
          Basic Information
        </h2>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                Merchant Name *
              </label>
              <input
                id="name"
                {...register('name')}
                className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
              />
              {errors.name && (
                <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address *
              </label>
              <input
                id="email"
                type="email"
                {...register('email')}
                className={`w-full p-2 border rounded-md ${errors.email ? 'border-red-500' : ''}`}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number *
              </label>
              <input
                id="phoneNumber"
                {...register('phoneNumber')}
                className={`w-full p-2 border rounded-md ${errors.phoneNumber ? 'border-red-500' : ''}`}
              />
              {errors.phoneNumber && (
                <p className="mt-1 text-sm text-red-600">{errors.phoneNumber.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="ownerId" className="block text-sm font-medium text-gray-700 mb-1">
                Owner ID *
              </label>
              <input
                id="ownerId"
                {...register('ownerId')}
                className={`w-full p-2 border rounded-md ${errors.ownerId ? 'border-red-500' : ''}`}
              />
              {errors.ownerId && (
                <p className="mt-1 text-sm text-red-600">{errors.ownerId.message}</p>
              )}
            </div>

            <div className="md:col-span-2">
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 mb-1">
                Address *
              </label>
              <textarea
                id="address"
                {...register('address')}
                rows={3}
                className={`w-full p-2 border rounded-md ${errors.address ? 'border-red-500' : ''}`}
              />
              {errors.address && (
                <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="logo" className="block text-sm font-medium text-gray-700 mb-1">
                Logo URL
              </label>
              <input
                id="logo"
                type="url"
                {...register('logo')}
                className={`w-full p-2 border rounded-md ${errors.logo ? 'border-red-500' : ''}`}
              />
              {errors.logo && (
                <p className="mt-1 text-sm text-red-600">{errors.logo.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                id="status"
                {...register('status')}
                className={`w-full p-2 border rounded-md ${errors.status ? 'border-red-500' : ''}`}
              >
                <option value="pending">Pending</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
              {errors.status && (
                <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-4">
            <Link
              href={`/admin/merchants/${type}/${id}`}
              className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={isUpdating || !isDirty}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
            >
              {isUpdating ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
