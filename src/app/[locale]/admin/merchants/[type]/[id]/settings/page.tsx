'use client';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import Link from 'next/link';
import RestaurantSettingsForm from '@/components/admin/forms/RestaurantSettingsForm';
import RetailSettingsForm from '@/components/admin/forms/RetailSettingsForm';
import ServiceSettingsForm from '@/components/admin/forms/ServiceSettingsForm';
import DigitalSettingsForm from '@/components/admin/forms/DigitalSettingsForm';

interface MerchantSettingsPageProps {
  params: {
    type: string;
    id: string;
  };
}

export default function MerchantSettingsPage({ params }: MerchantSettingsPageProps) {
  const { type, id } = params;
  const { data: merchants, isLoading } = useGetMerchantsQuery();

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id);

  // Get type display name
  const getTypeDisplayName = () => {
    switch (type) {
      case 'restaurant':
        return 'Restaurant';
      case 'retail':
        return 'Retail Store';
      case 'service':
        return 'Service Provider';
      case 'digital':
        return 'Digital Store';
      case 'convenience':
        return 'Convenience Store';
      case 'custom':
        return 'Custom Merchant';
      default:
        return 'Merchant';
    }
  };

  if (isLoading) {
    return <div className="p-6">Loading merchant data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Merchant not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  // Verify that the merchant type matches the URL type
  if (merchant.type !== type) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Merchant type mismatch. This merchant is of type "{merchant.type}" but you're viewing the "{type}" settings.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/${merchant.type}/${merchant.id}/settings`} className="text-blue-600 hover:underline">
          Go to correct settings page
        </Link>
      </div>
    );
  }

  // Initialize settings if they don't exist
  const settings = merchant.settings || {};

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/${type}/${id}`} className="text-blue-600 hover:underline">
          ← Back to merchant details
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">{merchant.name} Settings</h1>

      <div className="mb-6">
        <p className="text-gray-600">
          Configure settings specific to this {getTypeDisplayName().toLowerCase()}.
        </p>
      </div>

      {/* Render the appropriate settings form based on merchant type */}
      {type === 'restaurant' && (
        <RestaurantSettingsForm
          merchantId={id}
          initialSettings={settings as any}
        />
      )}

      {type === 'retail' && (
        <RetailSettingsForm
          merchantId={id}
          initialSettings={settings as any}
        />
      )}

      {type === 'service' && (
        <ServiceSettingsForm
          merchantId={id}
          initialSettings={settings as any}
        />
      )}

      {type === 'digital' && (
        <DigitalSettingsForm
          merchantId={id}
          initialSettings={settings as any}
        />
      )}

      {(type === 'convenience' || type === 'custom') && (
        <div className="bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold mb-4">{getTypeDisplayName()} Settings</h2>
          <p className="text-gray-600">
            Settings for this merchant type are not yet implemented.
          </p>
        </div>
      )}
    </div>
  );
}
