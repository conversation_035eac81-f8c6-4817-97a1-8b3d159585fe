'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import DigitalProductForm from '@/components/admin/forms/DigitalProductForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetDigitalProductsQuery } from '@/lib/redux/api/endpoints/digitalApi';

interface EditDigitalProductPageProps {
  params: {
    id: string;
    productId: string;
  };
}

export default function EditDigitalProductPage({ params }: EditDigitalProductPageProps) {
  const { id, productId } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: products, isLoading: isLoadingProducts } = useGetDigitalProductsQuery(id);

  const isLoading = isLoadingMerchants || isLoadingProducts;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'digital');

  // Find the product
  const product = products?.find(p => p.id === productId);

  const handleSuccess = () => {
    // Redirect to the products list
    router.push(`/admin/merchants/digital/${id}`);
  };

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Digital store not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Digital product not found. The product may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/digital/${id}`} className="text-blue-600 hover:underline">
          ← Back to digital store
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/digital/${id}`} className="text-blue-600 hover:underline">
          ← Back to digital store
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Edit Digital Product - {product.name}</h1>

      <DigitalProductForm
        merchantId={id}
        initialData={product}
        mode="edit"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
