'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  useGetReservationByIdQuery,
  useGetTablesQuery
} from '@/lib/redux/api/endpoints/restaurant/restaurantApi';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import ReservationForm from '@/components/admin/forms/ReservationForm';
import { Button } from '@/components/ui/button';

interface EditReservationPageProps {
  params: {
    id: string;
    reservationId: string;
  };
}

export default function EditReservationPage({ params }: EditReservationPageProps) {
  const { id, reservationId } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: reservation, isLoading: isLoadingReservation } = useGetReservationByIdQuery({
    merchantId: id,
    reservationId,
  });

  // Get the merchant
  const merchant = merchants?.find(m => m.id === id);

  // Handle successful update
  const handleUpdateSuccess = () => {
    router.push(`/admin/merchants/restaurant/${id}/reservations`);
  };

  if (isLoadingMerchants || isLoadingReservation) {
    return <div className="p-4">Loading reservation data...</div>;
  }

  if (!merchant) {
    return <div className="p-4">Merchant not found.</div>;
  }

  if (!reservation) {
    return <div className="p-4">Reservation not found.</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Edit Reservation</h1>
          <p className="text-gray-500">{merchant.name}</p>
        </div>
        <Link href={`/admin/merchants/restaurant/${id}/reservations`}>
          <Button variant="outline">Back to Reservations</Button>
        </Link>
      </div>

      <ReservationForm
        merchantId={id}
        initialData={reservation}
        mode="edit"
        onSuccess={handleUpdateSuccess}
      />
    </div>
  );
}
