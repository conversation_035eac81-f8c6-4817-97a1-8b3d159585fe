'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import {
  useGetReservationsQuery,
  useGetTablesQuery,
  useDeleteReservationMutation
} from '@/lib/redux/api/endpoints/restaurant/restaurantApi';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { format, parseISO, isToday, isFuture, isPast } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import ReservationForm from '@/components/admin/forms/ReservationForm';
import { toast } from 'sonner';

interface ReservationsPageProps {
  params: {
    id: string;
  };
}

export default function ReservationsPage({ params }: ReservationsPageProps) {
  const { id } = params;
  const router = useRouter();

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: reservations, isLoading: isLoadingReservations, refetch } = useGetReservationsQuery(id);
  const { data: tables, isLoading: isLoadingTables } = useGetTablesQuery(id);
  const [deleteReservation, { isLoading: isDeleting }] = useDeleteReservationMutation();

  // Get the merchant
  const merchant = merchants?.find(m => m.id === id);

  // Filter reservations
  const filteredReservations = reservations?.filter(reservation => {
    // Filter by search term (customer name or email)
    const matchesSearch = searchTerm === '' ||
      reservation.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reservation.customerEmail.toLowerCase().includes(searchTerm.toLowerCase());

    // Filter by status
    const matchesStatus = statusFilter === null || reservation.status === statusFilter;

    // Filter by date
    let matchesDate = true;
    if (dateFilter === 'today') {
      matchesDate = isToday(parseISO(reservation.date));
    } else if (dateFilter === 'upcoming') {
      matchesDate = isFuture(parseISO(reservation.date)) ||
        (isToday(parseISO(reservation.date)) && reservation.status !== 'completed');
    } else if (dateFilter === 'past') {
      matchesDate = isPast(parseISO(reservation.date)) && !isToday(parseISO(reservation.date));
    }

    return matchesSearch && matchesStatus && matchesDate;
  }) || [];

  // Handle reservation deletion
  const handleDeleteReservation = async (reservationId: string) => {
    if (!confirm('Are you sure you want to delete this reservation?')) return;

    try {
      await deleteReservation({
        merchantId: id,
        reservationId,
      }).unwrap();

      toast.success('Reservation deleted successfully');
      refetch();
    } catch (err) {
      console.error('Error deleting reservation:', err);
      toast.error('Failed to delete reservation. Please try again.');
    }
  };

  // Handle reservation creation success
  const handleCreateSuccess = () => {
    setIsCreateDialogOpen(false);
    refetch();
  };

  // Get table name by ID
  const getTableName = (tableId: string) => {
    const table = tables?.find(t => t.id === tableId);
    return table ? `Table ${table.number}` : 'Unknown Table';
  };

  if (isLoadingMerchants || isLoadingReservations || isLoadingTables) {
    return <div className="p-4">Loading reservation data...</div>;
  }

  if (!merchant) {
    return <div className="p-4">Merchant not found.</div>;
  }

  if (merchant.type !== 'restaurant') {
    return <div className="p-4">This feature is only available for restaurant merchants.</div>;
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{merchant.name} - Reservations</h1>
          <p className="text-gray-500">Manage restaurant reservations</p>
        </div>
        <div className="flex space-x-2">
          <Link href={`/admin/merchants/restaurant/${id}`}>
            <Button variant="outline">Back to Dashboard</Button>
          </Link>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>New Reservation</Button>
            </DialogTrigger>
            <DialogContent className="max-w-3xl">
              <DialogHeader>
                <DialogTitle>Create New Reservation</DialogTitle>
              </DialogHeader>
              <ReservationForm
                merchantId={id}
                mode="create"
                onSuccess={handleCreateSuccess}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md">
        <div className="p-4 border-b">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by customer name or email"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="max-w-md"
              />
            </div>
            <div className="flex flex-wrap gap-2">
              <Select value={statusFilter || 'all'} onValueChange={(value) => setStatusFilter(value === 'all' ? null : value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>

              <Select value={dateFilter || 'all'} onValueChange={(value) => setDateFilter(value === 'all' ? null : value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by date" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Dates</SelectItem>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="upcoming">Upcoming</SelectItem>
                  <SelectItem value="past">Past</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <div className="p-4">
          {filteredReservations.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date & Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Table
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Party Size
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredReservations.map((reservation) => (
                    <tr key={reservation.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{reservation.customerName}</div>
                        <div className="text-sm text-gray-500">{reservation.customerEmail}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {format(parseISO(reservation.date), 'MMM d, yyyy')}
                        </div>
                        <div className="text-sm text-gray-500">{reservation.time}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getTableName(reservation.tableId)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {reservation.partySize}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                          ${reservation.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                            reservation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            reservation.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                            'bg-blue-100 text-blue-800'}`}>
                          {reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Link
                          href={`/admin/merchants/restaurant/${id}/reservations/${reservation.id}`}
                          className="text-indigo-600 hover:text-indigo-900 mr-4"
                        >
                          Edit
                        </Link>
                        <button
                          onClick={() => handleDeleteReservation(reservation.id)}
                          className="text-red-600 hover:text-red-900"
                          disabled={isDeleting}
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <p>No reservations found. Create your first reservation to get started.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
