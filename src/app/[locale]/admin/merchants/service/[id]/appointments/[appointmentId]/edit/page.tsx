'use client';

import { useRouter } from 'next/navigation';
import Link from 'next/link';
import AppointmentForm from '@/components/admin/forms/AppointmentForm';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetAppointmentsQuery } from '@/lib/redux/api/endpoints/serviceApi';

interface EditAppointmentPageProps {
  params: {
    id: string;
    appointmentId: string;
  };
}

export default function EditAppointmentPage({ params }: EditAppointmentPageProps) {
  const { id, appointmentId } = params;
  const router = useRouter();

  const { data: merchants, isLoading: isLoadingMerchants } = useGetMerchantsQuery();
  const { data: appointments, isLoading: isLoadingAppointments } = useGetAppointmentsQuery(id);

  const isLoading = isLoadingMerchants || isLoadingAppointments;

  // Find the merchant
  const merchant = merchants?.find(m => m.id === id && m.type === 'service');

  // Find the appointment
  const appointment = appointments?.find(a => a.id === appointmentId);

  const handleSuccess = () => {
    // Redirect to the appointments list
    router.push(`/admin/merchants/service/${id}/appointments`);
  };

  if (isLoading) {
    return <div className="p-6">Loading data...</div>;
  }

  if (!merchant) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Service provider not found. The merchant may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href="/admin/merchants" className="text-blue-600 hover:underline">
          ← Back to merchants
        </Link>
      </div>
    );
  }

  if (!appointment) {
    return (
      <div className="p-6">
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Appointment not found. The appointment may have been deleted or you may not have permission to view it.
              </p>
            </div>
          </div>
        </div>
        <Link href={`/admin/merchants/service/${id}/appointments`} className="text-blue-600 hover:underline">
          ← Back to appointments
        </Link>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Link href={`/admin/merchants/service/${id}/appointments`} className="text-blue-600 hover:underline">
          ← Back to appointments
        </Link>
      </div>

      <h1 className="text-2xl font-bold mb-6">Edit Appointment - {appointment.customerName}</h1>

      <AppointmentForm
        merchantId={id}
        initialData={appointment}
        mode="edit"
        onSuccess={handleSuccess}
      />
    </div>
  );
}
