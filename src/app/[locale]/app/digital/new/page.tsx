'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Monitor } from 'lucide-react';
import { Link } from '@/i18n/navigation';
import { useCreateMerchantMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { toast } from 'sonner';

export default function NewDigitalBusinessPage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [createMerchant, { isLoading }] = useCreateMerchantMutation();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    email: '',
    phone: '',
    website: '',
    timezone: 'Asia/Bangkok',
    currency: 'THB'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Business name is required');
      return;
    }

    try {
      const result = await createMerchant({
        ...formData,
        settings: {
          businessType: 'digital'
        }
      }).unwrap();

      toast.success('Digital business created successfully!');
      router.push(`/app/digital/${result.slug || result.id}`);
    } catch (error) {
      console.error('Error creating digital business:', error);
      toast.error('Failed to create digital business. Please try again.');
    }
  };

  return (
    <div className="font-be-vietnam min-h-screen bg-background">
      <div className="container mx-auto p-4 max-w-2xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href="/app">
            <Button variant="outline" size="sm" className="bg-background border-border">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-foreground text-[32px] font-bold leading-tight">Create Digital Business</h1>
            <p className="text-muted-foreground text-sm">Set up your digital business to sell products and courses online</p>
          </div>
        </div>

        {/* Form */}
        <Card className="bg-white border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center">
              <Monitor className="h-5 w-5 mr-2 text-[#8a745c]" />
              Business Information
            </CardTitle>
            <CardDescription className="text-[#8a745c]">
              Enter your digital business details to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="text-[#181510]">Business Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your business name"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description" className="text-[#181510]">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Describe your digital business"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    rows={3}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-[#181510] font-medium">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email" className="text-[#181510]">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-[#181510]">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+66 2 123 4567"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="website" className="text-[#181510]">Website</Label>
                  <Input
                    id="website"
                    name="website"
                    type="url"
                    value={formData.website}
                    onChange={handleInputChange}
                    placeholder="https://www.example.com"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                  />
                </div>
              </div>

              {/* Business Settings */}
              <div className="space-y-4">
                <h3 className="text-[#181510] font-medium">Business Settings</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="timezone" className="text-[#181510]">Timezone</Label>
                    <Input
                      id="timezone"
                      name="timezone"
                      value={formData.timezone}
                      onChange={handleInputChange}
                      placeholder="Asia/Bangkok"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="currency" className="text-[#181510]">Currency</Label>
                    <Input
                      id="currency"
                      name="currency"
                      value={formData.currency}
                      onChange={handleInputChange}
                      placeholder="THB"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end gap-4 pt-6">
                <Link href="/app">
                  <Button variant="outline" className="bg-[#f1edea] text-[#181510] border-none">
                    Cancel
                  </Button>
                </Link>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
                >
                  {isLoading ? 'Creating...' : 'Create Business'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
