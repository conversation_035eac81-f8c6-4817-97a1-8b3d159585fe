'use client';

import React, { useState } from 'react';
import { useTranslations } from 'next-intl';
import { 
  Bell, 
  Mail, 
  Smartphone, 
  Monitor, 
  Save,
  Volume2,
  VolumeX,
  Clock,
  Settings,
  Shield,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface NotificationSetting {
  id: string;
  category: string;
  title: string;
  description: string;
  channels: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
  };
  priority: 'low' | 'medium' | 'high';
  enabled: boolean;
}

interface GeneralSettings {
  doNotDisturb: {
    enabled: boolean;
    startTime: string;
    endTime: string;
  };
  soundEnabled: boolean;
  emailDigest: {
    enabled: boolean;
    frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
  };
  pushNotifications: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
}

export default function NotificationSettingsPage() {
  const t = useTranslations('notifications');

  // State
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'new-orders',
      category: 'Orders',
      title: 'New Orders',
      description: 'Get notified when new orders are placed',
      channels: { email: true, push: true, sms: false, inApp: true },
      priority: 'high',
      enabled: true
    },
    {
      id: 'order-updates',
      category: 'Orders',
      title: 'Order Updates',
      description: 'Status changes, cancellations, and modifications',
      channels: { email: true, push: true, sms: false, inApp: true },
      priority: 'medium',
      enabled: true
    },
    {
      id: 'new-reservations',
      category: 'Reservations',
      title: 'New Reservations',
      description: 'Get notified when customers make reservations',
      channels: { email: true, push: true, sms: false, inApp: true },
      priority: 'high',
      enabled: true
    },
    {
      id: 'reservation-changes',
      category: 'Reservations',
      title: 'Reservation Changes',
      description: 'Modifications and cancellations',
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'medium',
      enabled: true
    },
    {
      id: 'new-reviews',
      category: 'Reviews',
      title: 'New Reviews',
      description: 'Customer reviews and ratings',
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'low',
      enabled: true
    },
    {
      id: 'low-stock',
      category: 'Inventory',
      title: 'Low Stock Alerts',
      description: 'When inventory items are running low',
      channels: { email: true, push: true, sms: true, inApp: true },
      priority: 'high',
      enabled: true
    },
    {
      id: 'staff-updates',
      category: 'Staff',
      title: 'Staff Updates',
      description: 'Schedule changes and staff notifications',
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'medium',
      enabled: true
    },
    {
      id: 'system-updates',
      category: 'System',
      title: 'System Updates',
      description: 'Platform updates and maintenance notifications',
      channels: { email: true, push: false, sms: false, inApp: true },
      priority: 'low',
      enabled: true
    },
    {
      id: 'marketing',
      category: 'Marketing',
      title: 'Marketing Insights',
      description: 'Campaign performance and analytics',
      channels: { email: true, push: false, sms: false, inApp: false },
      priority: 'low',
      enabled: false
    }
  ]);

  const [generalSettings, setGeneralSettings] = useState<GeneralSettings>({
    doNotDisturb: {
      enabled: false,
      startTime: '22:00',
      endTime: '08:00'
    },
    soundEnabled: true,
    emailDigest: {
      enabled: true,
      frequency: 'daily'
    },
    pushNotifications: true,
    emailNotifications: true,
    smsNotifications: false
  });

  const [saving, setSaving] = useState(false);

  // Handlers
  const updateSetting = (id: string, updates: Partial<NotificationSetting>) => {
    setSettings(prev => 
      prev.map(setting => 
        setting.id === id ? { ...setting, ...updates } : setting
      )
    );
  };

  const updateChannel = (id: string, channel: keyof NotificationSetting['channels'], value: boolean) => {
    setSettings(prev => 
      prev.map(setting => 
        setting.id === id 
          ? { ...setting, channels: { ...setting.channels, [channel]: value } }
          : setting
      )
    );
  };

  const updateGeneralSetting = (key: keyof GeneralSettings, value: any) => {
    setGeneralSettings(prev => ({ ...prev, [key]: value }));
  };

  const saveSettings = async () => {
    setSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setSaving(false);
    // Show success message
  };

  const categories = [...new Set(settings.map(s => s.category))];

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Settings className="w-8 h-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Notification Settings</h1>
            <p className="text-gray-600">Manage how and when you receive notifications</p>
          </div>
        </div>

        <button
          onClick={saveSettings}
          disabled={saving}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
        >
          <Save className="w-4 h-4" />
          {saving ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* General Settings */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <Shield className="w-5 h-5" />
          General Settings
        </h2>

        <div className="space-y-6">
          {/* Global Toggles */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-gray-600" />
                <div>
                  <div className="font-medium">Email Notifications</div>
                  <div className="text-sm text-gray-600">Receive notifications via email</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={generalSettings.emailNotifications}
                  onChange={(e) => updateGeneralSetting('emailNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <Smartphone className="w-5 h-5 text-gray-600" />
                <div>
                  <div className="font-medium">Push Notifications</div>
                  <div className="text-sm text-gray-600">Receive push notifications</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={generalSettings.pushNotifications}
                  onChange={(e) => updateGeneralSetting('pushNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div className="flex items-center gap-3">
                <Smartphone className="w-5 h-5 text-gray-600" />
                <div>
                  <div className="font-medium">SMS Notifications</div>
                  <div className="text-sm text-gray-600">Receive text messages</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={generalSettings.smsNotifications}
                  onChange={(e) => updateGeneralSetting('smsNotifications', e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>
          </div>

          {/* Sound Settings */}
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center gap-3">
              {generalSettings.soundEnabled ? (
                <Volume2 className="w-5 h-5 text-gray-600" />
              ) : (
                <VolumeX className="w-5 h-5 text-gray-600" />
              )}
              <div>
                <div className="font-medium">Sound Notifications</div>
                <div className="text-sm text-gray-600">Play sound for notifications</div>
              </div>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={generalSettings.soundEnabled}
                onChange={(e) => updateGeneralSetting('soundEnabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* Do Not Disturb */}
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-gray-600" />
                <div>
                  <div className="font-medium">Do Not Disturb</div>
                  <div className="text-sm text-gray-600">Silence notifications during specific hours</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={generalSettings.doNotDisturb.enabled}
                  onChange={(e) => updateGeneralSetting('doNotDisturb', {
                    ...generalSettings.doNotDisturb,
                    enabled: e.target.checked
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {generalSettings.doNotDisturb.enabled && (
              <div className="flex items-center gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">From</label>
                  <input
                    type="time"
                    value={generalSettings.doNotDisturb.startTime}
                    onChange={(e) => updateGeneralSetting('doNotDisturb', {
                      ...generalSettings.doNotDisturb,
                      startTime: e.target.value
                    })}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">To</label>
                  <input
                    type="time"
                    value={generalSettings.doNotDisturb.endTime}
                    onChange={(e) => updateGeneralSetting('doNotDisturb', {
                      ...generalSettings.doNotDisturb,
                      endTime: e.target.value
                    })}
                    className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Email Digest */}
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Mail className="w-5 h-5 text-gray-600" />
                <div>
                  <div className="font-medium">Email Digest</div>
                  <div className="text-sm text-gray-600">Receive summary emails instead of individual notifications</div>
                </div>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={generalSettings.emailDigest.enabled}
                  onChange={(e) => updateGeneralSetting('emailDigest', {
                    ...generalSettings.emailDigest,
                    enabled: e.target.checked
                  })}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
              </label>
            </div>

            {generalSettings.emailDigest.enabled && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Frequency</label>
                <select
                  value={generalSettings.emailDigest.frequency}
                  onChange={(e) => updateGeneralSetting('emailDigest', {
                    ...generalSettings.emailDigest,
                    frequency: e.target.value as any
                  })}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="immediate">Immediate</option>
                  <option value="hourly">Hourly</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                </select>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Notification Categories */}
      {categories.map(category => (
        <div key={category} className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
            <Zap className="w-5 h-5" />
            {category} Notifications
          </h2>

          <div className="space-y-4">
            {settings.filter(s => s.category === category).map(setting => (
              <div key={setting.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-1">
                      <h3 className="font-medium text-gray-900">{setting.title}</h3>
                      <span className={cn(
                        'px-2 py-1 text-xs rounded-full',
                        setting.priority === 'high' ? 'bg-red-100 text-red-700' :
                        setting.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                        'bg-green-100 text-green-700'
                      )}>
                        {setting.priority} priority
                      </span>
                    </div>
                    <p className="text-sm text-gray-600">{setting.description}</p>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={setting.enabled}
                      onChange={(e) => updateSetting(setting.id, { enabled: e.target.checked })}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>

                {setting.enabled && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    <label className="flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        checked={setting.channels.email}
                        onChange={(e) => updateChannel(setting.id, 'email', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Mail className="w-4 h-4 text-gray-600" />
                      Email
                    </label>
                    <label className="flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        checked={setting.channels.push}
                        onChange={(e) => updateChannel(setting.id, 'push', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Smartphone className="w-4 h-4 text-gray-600" />
                      Push
                    </label>
                    <label className="flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        checked={setting.channels.sms}
                        onChange={(e) => updateChannel(setting.id, 'sms', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Smartphone className="w-4 h-4 text-gray-600" />
                      SMS
                    </label>
                    <label className="flex items-center gap-2 text-sm">
                      <input
                        type="checkbox"
                        checked={setting.channels.inApp}
                        onChange={(e) => updateChannel(setting.id, 'inApp', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <Monitor className="w-4 h-4 text-gray-600" />
                      In-App
                    </label>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}
