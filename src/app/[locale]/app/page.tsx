'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ShoppingBag, Utensils, Wrench, Monitor, ShoppingCart } from 'lucide-react';
import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { AppLoading } from '@/components/ui/app-loading';

// Define types for shop categories
interface ShopCategory {
  id: string;
  name: string;
  description: string;
  image: string;
  icon: React.ReactNode;
  href: string;
}

interface ShopCategoriesByType {
  [key: string]: ShopCategory[];
}

// Default shop categories for empty states (not mock data)
const defaultShopCategories: ShopCategoriesByType = {
  restaurant: [
    {
      id: 'create-restaurant',
      name: 'Create Restaurant',
      description: 'Set up your restaurant, menu items, tables, and reservations',
      image: 'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=2070&auto=format&fit=crop',
      icon: <Utensils className="h-5 w-5" />,
      href: '/app/restaurant/new'
    }
  ],
  retail: [
    {
      id: 'create-retail',
      name: 'Create Retail Store',
      description: 'Set up your retail store, inventory, and sales management',
      image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?q=80&w=2070&auto=format&fit=crop',
      icon: <ShoppingBag className="h-5 w-5" />,
      href: '/app/retail/new'
    }
  ],
  service: [
    {
      id: 'create-service',
      name: 'Create Service Business',
      description: 'Set up your service business, appointments, and staff management',
      image: 'https://images.unsplash.com/photo-1560066984-138dadb4c035?q=80&w=2074&auto=format&fit=crop',
      icon: <Wrench className="h-5 w-5" />,
      href: '/app/service/new'
    }
  ],
  digital: [
    {
      id: 'create-digital',
      name: 'Create Digital Business',
      description: 'Set up your digital products, courses, and content management',
      image: 'https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=2070&auto=format&fit=crop',
      icon: <Monitor className="h-5 w-5" />,
      href: '/app/digital/new'
    }
  ],
  convenience: [
    {
      id: 'create-convenience',
      name: 'Create Convenience Store',
      description: 'Set up your convenience store, inventory, and quick sales',
      image: 'https://images.unsplash.com/photo-1515706886582-54c73c5eaf41?q=80&w=2070&auto=format&fit=crop',
      icon: <ShoppingCart className="h-5 w-5" />,
      href: '/app/convenience/new'
    }
  ]
};

export default function AppPage() {
  const [activeTab, setActiveTab] = useState('restaurant');

  // Fetch real merchants data from backend
  const { data: merchantsData, isLoading, isError } = useGetMerchantsQuery({});

  // Process merchants data by type
  const merchantsByType = merchantsData?.data?.reduce((acc, merchant) => {
    // Determine merchant type based on business category or default to restaurant
    const type = merchant.settings?.businessType || 'restaurant';
    if (!acc[type]) {
      acc[type] = [];
    }

    acc[type].push({
      id: merchant.id,
      name: merchant.name,
      description: merchant.description || `Manage your ${merchant.name} business`,
      image: merchant.logo || `https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?q=80&w=2070&auto=format&fit=crop`,
      icon: getIconForType(type),
      href: `/app/${type}/${merchant.slug || merchant.id}`
    });

    return acc;
  }, {} as Record<string, ShopCategory[]>) || {};

  // Helper function to get icon for merchant type
  function getIconForType(type: string) {
    switch (type) {
      case 'restaurant':
      case 'cafe':
      case 'bakery':
      case 'food-truck':
        return <Utensils className="h-5 w-5" />;
      case 'retail':
      case 'clothing':
      case 'electronics':
      case 'home':
      case 'beauty':
        return <ShoppingBag className="h-5 w-5" />;
      case 'service':
      case 'salon':
      case 'fitness':
      case 'auto':
      case 'professional':
        return <Wrench className="h-5 w-5" />;
      case 'digital':
      case 'software':
      case 'courses':
      case 'subscriptions':
        return <Monitor className="h-5 w-5" />;
      case 'convenience':
      case 'mini-mart':
      case 'kiosk':
      case 'gas-station':
        return <ShoppingCart className="h-5 w-5" />;
      default:
        return <Utensils className="h-5 w-5" />;
    }
  }

  if (isLoading) {
    return <AppLoading />;
  }

  if (isError) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-center text-red-600">
          <p>Error loading merchants. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam container mx-auto">
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181510] text-[32px] font-bold leading-tight">Shop Categories</p>
          <p className="text-[#8a745c] text-sm font-normal leading-normal">Browse and manage your shops by category.</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <div className="pb-3">
          <TabsList className="flex border-b border-[#e2dcd4] px-4 gap-8 bg-transparent h-auto">
            <TabsTrigger
              value="restaurant"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Restaurant</p>
            </TabsTrigger>
            <TabsTrigger
              value="retail"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Retail</p>
            </TabsTrigger>
            <TabsTrigger
              value="service"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Service</p>
            </TabsTrigger>
            <TabsTrigger
              value="digital"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Digital</p>
            </TabsTrigger>
            <TabsTrigger
              value="convenience"
              className="flex flex-col items-center justify-center border-b-[3px] data-[state=active]:border-b-[#e5ccb2] data-[state=active]:text-[#181510] data-[state=inactive]:border-b-transparent data-[state=inactive]:text-[#8a745c] pb-[13px] pt-4 bg-transparent"
            >
              <p className="text-sm font-bold leading-normal tracking-[0.015em]">Convenience</p>
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Show tabs for available merchant types or default create options */}
        {Object.keys(merchantsByType).length > 0 ? (
          Object.keys(merchantsByType).map((type) => (
            <TabsContent key={type} value={type} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
                {merchantsByType[type].map((merchant) => (
                  <Link href={merchant.href} key={merchant.id}>
                    <Card className="h-full pt-0 overflow-hidden hover:shadow-md transition-shadow bg-[#fbfaf9] border-[#e5e1dc]">
                      <div
                        className="h-40 bg-cover bg-center"
                        style={{ backgroundImage: `url(${merchant.image})` }}
                      />
                      <CardHeader className="pb-2">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-[#8a745c]">{merchant.icon}</span>
                          <CardTitle className="text-[#181510]">{merchant.name}</CardTitle>
                        </div>
                        <CardDescription className="text-[#8a745c]">{merchant.description}</CardDescription>
                      </CardHeader>
                    </Card>
                  </Link>
                ))}
              </div>
            </TabsContent>
          ))
        ) : (
          /* Show default create options when no merchants exist */
          Object.keys(defaultShopCategories).map((type) => (
            <TabsContent key={type} value={type} className="mt-0">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 p-4">
                {defaultShopCategories[type].map((category) => (
                  <Link href={category.href} key={category.id}>
                    <Card className="h-full pt-0 overflow-hidden hover:shadow-md transition-shadow bg-card border-border">
                      <div
                        className="h-40 bg-cover bg-center"
                        style={{ backgroundImage: `url(${category.image})` }}
                      />
                      <CardHeader className="pb-2">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-primary">{category.icon}</span>
                          <CardTitle className="text-foreground">{category.name}</CardTitle>
                        </div>
                        <CardDescription className="text-muted-foreground">{category.description}</CardDescription>
                      </CardHeader>
                    </Card>
                  </Link>
                ))}
              </div>
            </TabsContent>
          ))
        )}
      </Tabs>
    </div>
  );
}
