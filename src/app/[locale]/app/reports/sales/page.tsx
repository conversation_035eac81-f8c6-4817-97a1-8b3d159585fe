'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { 
  ArrowLeft,
  Download,
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Users,
  Clock,
  Filter,
  RefreshCw
} from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

// Mock Chart Component (replace with actual chart library)
function Chart({ data, type, className }: { data: any[], type: string, className?: string }) {
  return (
    <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-64', className)}>
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📊</div>
        <p>{type} Chart</p>
        <p className="text-sm">Chart visualization would go here</p>
      </div>
    </div>
  );
}

interface SalesMetric {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
}

interface TopItem {
  name: string;
  sales: number;
  revenue: string;
  change: string;
  trend: 'up' | 'down';
}

export default function SalesReportPage() {
  const t = useTranslations('reports');
  
  // State
  const [dateRange, setDateRange] = useState('7d');
  const [loading, setLoading] = useState(true);

  // Mock data
  const salesMetrics: SalesMetric[] = [
    {
      label: 'Total Revenue',
      value: '$24,580.50',
      change: '+12.5%',
      trend: 'up',
      icon: <DollarSign className="w-5 h-5" />
    },
    {
      label: 'Total Orders',
      value: '1,247',
      change: '+8.2%',
      trend: 'up',
      icon: <ShoppingCart className="w-5 h-5" />
    },
    {
      label: 'Average Order Value',
      value: '$32.45',
      change: '-2.3%',
      trend: 'down',
      icon: <TrendingUp className="w-5 h-5" />
    },
    {
      label: 'Unique Customers',
      value: '892',
      change: '+15.7%',
      trend: 'up',
      icon: <Users className="w-5 h-5" />
    }
  ];

  const topItems: TopItem[] = [
    { name: 'Margherita Pizza', sales: 156, revenue: '$2,340', change: '+12%', trend: 'up' },
    { name: 'Caesar Salad', sales: 134, revenue: '$1,876', change: '+8%', trend: 'up' },
    { name: 'Grilled Salmon', sales: 98, revenue: '$2,156', change: '-3%', trend: 'down' },
    { name: 'Pasta Carbonara', sales: 87, revenue: '$1,653', change: '+5%', trend: 'up' },
    { name: 'Chicken Wings', sales: 76, revenue: '$1,292', change: '+18%', trend: 'up' }
  ];

  const revenueData = [
    { date: '2024-01-01', revenue: 2400 },
    { date: '2024-01-02', revenue: 2800 },
    { date: '2024-01-03', revenue: 3200 },
    { date: '2024-01-04', revenue: 2900 },
    { date: '2024-01-05', revenue: 3500 },
    { date: '2024-01-06', revenue: 3800 },
    { date: '2024-01-07', revenue: 4200 }
  ];

  const ordersByHour = [
    { hour: '6 AM', orders: 12 },
    { hour: '7 AM', orders: 28 },
    { hour: '8 AM', orders: 45 },
    { hour: '9 AM', orders: 32 },
    { hour: '10 AM', orders: 18 },
    { hour: '11 AM', orders: 25 },
    { hour: '12 PM', orders: 78 },
    { hour: '1 PM', orders: 92 },
    { hour: '2 PM', orders: 65 },
    { hour: '3 PM', orders: 34 },
    { hour: '4 PM', orders: 28 },
    { hour: '5 PM', orders: 45 },
    { hour: '6 PM', orders: 89 },
    { hour: '7 PM', orders: 112 },
    { hour: '8 PM', orders: 98 },
    { hour: '9 PM', orders: 67 },
    { hour: '10 PM', orders: 34 },
    { hour: '11 PM', orders: 12 }
  ];

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const dateRangeOptions = [
    { value: '1d', label: 'Today' },
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: 'custom', label: 'Custom range' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link
            href="/app/reports"
            className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
          </Link>
          <div className="flex items-center gap-3">
            <DollarSign className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Sales Report</h1>
              <p className="text-gray-600">Revenue analytics and sales performance</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {dateRangeOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
          <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {salesMetrics.map((metric, index) => (
          <div key={index} className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-50 text-blue-600 rounded-lg">
                  {metric.icon}
                </div>
                <div>
                  <p className="text-sm text-gray-600">{metric.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                </div>
              </div>
              <div className={cn(
                'flex items-center gap-1 text-sm font-medium',
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
              )}>
                {metric.trend === 'up' ? (
                  <TrendingUp className="w-4 h-4" />
                ) : metric.trend === 'down' ? (
                  <TrendingDown className="w-4 h-4" />
                ) : null}
                {metric.change}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Calendar className="w-4 h-4" />
              Last 7 days
            </div>
          </div>
          <Chart data={revenueData} type="Line" />
        </div>

        {/* Orders by Hour */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900">Orders by Hour</h3>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              Today
            </div>
          </div>
          <Chart data={ordersByHour} type="Bar" />
        </div>
      </div>

      {/* Top Performing Items */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Top Performing Items</h3>
          <p className="text-gray-600">Best selling menu items by revenue</p>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Sales Count
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Change
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {topItems.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-medium text-sm">
                        {index + 1}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{item.name}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.sales}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {item.revenue}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={cn(
                      'flex items-center gap-1 text-sm font-medium',
                      item.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    )}>
                      {item.trend === 'up' ? (
                        <TrendingUp className="w-4 h-4" />
                      ) : (
                        <TrendingDown className="w-4 h-4" />
                      )}
                      {item.change}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Sales Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment Methods */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Methods</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Credit Card</span>
              <span className="text-sm font-medium">65%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-blue-600 h-2 rounded-full" style={{ width: '65%' }}></div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Cash</span>
              <span className="text-sm font-medium">25%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-green-600 h-2 rounded-full" style={{ width: '25%' }}></div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Digital Wallet</span>
              <span className="text-sm font-medium">10%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div className="bg-purple-600 h-2 rounded-full" style={{ width: '10%' }}></div>
            </div>
          </div>
        </div>

        {/* Order Types */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Types</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Dine-in</span>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Takeaway</span>
              <span className="text-sm font-medium">35%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Delivery</span>
              <span className="text-sm font-medium">20%</span>
            </div>
          </div>
        </div>

        {/* Peak Hours */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Peak Hours</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Lunch (12-2 PM)</span>
              <span className="text-sm font-medium">35%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Dinner (6-8 PM)</span>
              <span className="text-sm font-medium">45%</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Other</span>
              <span className="text-sm font-medium">20%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
