'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  BarChart3, 
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  Package,
  DollarSign,
  Calendar,
  AlertTriangle,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Mock Chart Component
function Chart({ data, type, className }: { data: any[], type: string, className?: string }) {
  return (
    <div className={cn('bg-gray-100 rounded-lg flex items-center justify-center h-64', className)}>
      <div className="text-center text-gray-500">
        <div className="text-2xl mb-2">📊</div>
        <p>{type} Chart</p>
        <p className="text-sm">Chart visualization would go here</p>
      </div>
    </div>
  );
}

// Types
interface InventoryMetric {
  label: string;
  value: string;
  change: string;
  trend: 'up' | 'down' | 'neutral';
  icon: React.ReactNode;
}

interface UsageData {
  item: string;
  category: string;
  used: number;
  unit: string;
  cost: number;
  trend: 'up' | 'down' | 'neutral';
  change: string;
}

interface WasteData {
  item: string;
  category: string;
  wasted: number;
  unit: string;
  cost: number;
  reason: string;
  date: Date;
}

export default function InventoryReportsPage() {
  const t = useTranslations('inventory');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30d');

  // Mock data
  const inventoryMetrics: InventoryMetric[] = [
    {
      label: 'Total Inventory Value',
      value: '$12,450.00',
      change: '+8.2%',
      trend: 'up',
      icon: <DollarSign className="w-5 h-5" />
    },
    {
      label: 'Items Consumed',
      value: '1,247',
      change: '+12.5%',
      trend: 'up',
      icon: <Package className="w-5 h-5" />
    },
    {
      label: 'Waste Cost',
      value: '$342.50',
      change: '-15.3%',
      trend: 'down',
      icon: <AlertTriangle className="w-5 h-5" />
    },
    {
      label: 'Turnover Rate',
      value: '2.4x',
      change: '+5.1%',
      trend: 'up',
      icon: <TrendingUp className="w-5 h-5" />
    }
  ];

  const usageData: UsageData[] = [
    {
      item: 'Tomatoes',
      category: 'Vegetables',
      used: 45,
      unit: 'kg',
      cost: 157.50,
      trend: 'up',
      change: '+12%'
    },
    {
      item: 'Chicken Breast',
      category: 'Meat',
      used: 32,
      unit: 'kg',
      cost: 415.68,
      trend: 'up',
      change: '+8%'
    },
    {
      item: 'Mozzarella Cheese',
      category: 'Dairy',
      used: 28,
      unit: 'kg',
      cost: 266.00,
      trend: 'down',
      change: '-5%'
    },
    {
      item: 'Olive Oil',
      category: 'Oils & Condiments',
      used: 15,
      unit: 'bottles',
      cost: 134.85,
      trend: 'neutral',
      change: '0%'
    },
    {
      item: 'Flour',
      category: 'Baking',
      used: 38,
      unit: 'kg',
      cost: 85.50,
      trend: 'up',
      change: '+18%'
    }
  ];

  const wasteData: WasteData[] = [
    {
      item: 'Lettuce',
      category: 'Vegetables',
      wasted: 3,
      unit: 'kg',
      cost: 12.50,
      reason: 'Expired',
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
    },
    {
      item: 'Milk',
      category: 'Dairy',
      wasted: 2,
      unit: 'liters',
      cost: 8.00,
      reason: 'Spoiled',
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
    },
    {
      item: 'Bread',
      category: 'Baking',
      wasted: 5,
      unit: 'loaves',
      cost: 15.00,
      reason: 'Stale',
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
    }
  ];

  const stockLevelsData = [
    { category: 'Vegetables', inStock: 85, lowStock: 12, outOfStock: 3 },
    { category: 'Meat', inStock: 92, lowStock: 6, outOfStock: 2 },
    { category: 'Dairy', inStock: 78, lowStock: 15, outOfStock: 7 },
    { category: 'Dry Goods', inStock: 95, lowStock: 4, outOfStock: 1 },
    { category: 'Beverages', inStock: 88, lowStock: 8, outOfStock: 4 }
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const dateRangeOptions = [
    { value: '7d', label: 'Last 7 days' },
    { value: '30d', label: 'Last 30 days' },
    { value: '90d', label: 'Last 3 months' },
    { value: '1y', label: 'Last year' }
  ];

  // Get trend icon
  const getTrendIcon = (trend: 'up' | 'down' | 'neutral') => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-600" />;
      default:
        return <div className="w-4 h-4" />;
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/inventory`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Inventory
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <BarChart3 className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Inventory Reports</h1>
              <p className="text-gray-600">Analytics and insights for inventory management</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select date range" />
            </SelectTrigger>
            <SelectContent>
              {dateRangeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {inventoryMetrics.map((metric, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{metric.label}</CardTitle>
              <div className="p-2 bg-blue-50 text-blue-600 rounded-lg">
                {metric.icon}
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metric.value}</div>
              <div className={cn(
                'flex items-center gap-1 text-sm font-medium',
                metric.trend === 'up' ? 'text-green-600' : 
                metric.trend === 'down' ? 'text-red-600' : 'text-gray-600'
              )}>
                {getTrendIcon(metric.trend)}
                {metric.change}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Inventory Usage Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Inventory Usage Trend</CardTitle>
            <p className="text-sm text-gray-600">Daily consumption over time</p>
          </CardHeader>
          <CardContent>
            <Chart data={[]} type="Line" />
          </CardContent>
        </Card>

        {/* Stock Levels by Category */}
        <Card>
          <CardHeader>
            <CardTitle>Stock Levels by Category</CardTitle>
            <p className="text-sm text-gray-600">Current inventory status</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stockLevelsData.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="font-medium">{category.category}</span>
                    <span className="text-gray-500">
                      {category.inStock + category.lowStock + category.outOfStock} items
                    </span>
                  </div>
                  <div className="flex h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div 
                      className="bg-green-500" 
                      style={{ width: `${category.inStock}%` }}
                    />
                    <div 
                      className="bg-yellow-500" 
                      style={{ width: `${category.lowStock}%` }}
                    />
                    <div 
                      className="bg-red-500" 
                      style={{ width: `${category.outOfStock}%` }}
                    />
                  </div>
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>In Stock: {category.inStock}%</span>
                    <span>Low: {category.lowStock}%</span>
                    <span>Out: {category.outOfStock}%</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Usage Items */}
      <Card>
        <CardHeader>
          <CardTitle>Top Usage Items</CardTitle>
          <p className="text-sm text-gray-600">Most consumed inventory items</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Usage</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead>Trend</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {usageData.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.item}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.category}</Badge>
                  </TableCell>
                  <TableCell>
                    {item.used} {item.unit}
                  </TableCell>
                  <TableCell className="font-medium">
                    ${item.cost.toFixed(2)}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getTrendIcon(item.trend)}
                      <span className={cn(
                        'text-sm font-medium',
                        item.trend === 'up' ? 'text-green-600' : 
                        item.trend === 'down' ? 'text-red-600' : 'text-gray-600'
                      )}>
                        {item.change}
                      </span>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Waste Report */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Waste Report</CardTitle>
          <p className="text-sm text-gray-600">Items that were wasted or expired</p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Item</TableHead>
                <TableHead>Category</TableHead>
                <TableHead>Amount Wasted</TableHead>
                <TableHead>Cost</TableHead>
                <TableHead>Reason</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {wasteData.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.item}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{item.category}</Badge>
                  </TableCell>
                  <TableCell>
                    {item.wasted} {item.unit}
                  </TableCell>
                  <TableCell className="font-medium text-red-600">
                    ${item.cost.toFixed(2)}
                  </TableCell>
                  <TableCell>
                    <Badge className="bg-red-100 text-red-800">
                      {item.reason}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-gray-600">
                    {formatDate(item.date)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Summary Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Cost Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Total Purchases</span>
              <span className="font-medium">$8,450.00</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Total Usage</span>
              <span className="font-medium">$7,890.50</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Waste Cost</span>
              <span className="font-medium text-red-600">$342.50</span>
            </div>
            <div className="flex justify-between border-t pt-2">
              <span className="text-sm font-medium">Efficiency Rate</span>
              <span className="font-bold text-green-600">95.9%</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Turnover Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Fast Moving</span>
              <span className="font-medium">68%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Medium Moving</span>
              <span className="font-medium">25%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-600">Slow Moving</span>
              <span className="font-medium">7%</span>
            </div>
            <div className="flex justify-between border-t pt-2">
              <span className="text-sm font-medium">Avg Turnover</span>
              <span className="font-bold">2.4x/month</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recommendations</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium">Reduce lettuce orders</p>
                <p className="text-gray-600">High waste rate detected</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <TrendingUp className="w-4 h-4 text-green-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium">Increase chicken orders</p>
                <p className="text-gray-600">High demand trend</p>
              </div>
            </div>
            <div className="flex items-start gap-2">
              <Package className="w-4 h-4 text-blue-600 mt-0.5" />
              <div className="text-sm">
                <p className="font-medium">Optimize storage</p>
                <p className="text-gray-600">Improve FIFO rotation</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
