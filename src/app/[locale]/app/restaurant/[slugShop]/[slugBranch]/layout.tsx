'use client';

import { NavigationProvider } from '@/lib/context/NavigationContext';
import { NavigationType } from '@/lib/types/navigation';
import { addIconsToNavItems } from '@/components/navigation/NavigationIcons';
import ExpandableHeaderNavigation from '@/components/navigation/ExpandableHeaderNavigation';
import SheetSidebarNavigation from '@/components/navigation/SheetSidebarNavigation';
import DrawerNavigation from '@/components/navigation/DrawerNavigation';
import BottomNavigation from '@/components/navigation/BottomNavigation';
import { useNavigation } from '@/lib/context/NavigationContext';
import { cn } from '@/lib/utils';
import { useState, use } from 'react';
import React from 'react';
import AuthHeader from '@/components/navigation/AuthHeader';
import { getBranchWithShop } from '@/mock/shopData';
import LayoutSwitcher from '@/components/layout/LayoutSwitcher';
import { NotificationProvider } from '@/contexts/NotificationContext';

// Navigation wrapper component that uses the navigation context
function NavigationWrapper({
  children,
  slugShop,
  slugBranch
}: {
  children: React.ReactNode;
  slugShop: string;
  slugBranch: string;
}) {
  const { settings } = useNavigation();
  const [isCollapsed, setIsCollapsed] = useState(settings.isCollapsed || false);
  // Initialize with null to ensure proper type checking
  const [branchWithShop] = useState(() => {
    const result = getBranchWithShop(slugShop, slugBranch);
    return result || null;
  });

  // Basic nav items without icons
  const basicNavItems = [
    { name: 'Dashboard', href: `/app/restaurant/${slugShop}/${slugBranch}/dashboard` },
    { name: 'Orders', href: `/app/restaurant/${slugShop}/${slugBranch}/orders` },
    { name: 'Menu', href: `/app/restaurant/${slugShop}/${slugBranch}/menu` },
    { name: 'Tables', href: `/app/restaurant/${slugShop}/${slugBranch}/tables` },
    { name: 'Reviews', href: `/app/restaurant/${slugShop}/${slugBranch}/reviews` },
    { name: 'Staff', href: `/app/restaurant/${slugShop}/${slugBranch}/staff` },
    { name: 'Reports', href: `/app/restaurant/${slugShop}/${slugBranch}/reports` },
    { name: 'Settings', href: `/app/restaurant/${slugShop}/${slugBranch}/settings` },
  ];

  // Add icons to nav items
  const navItems = addIconsToNavItems(basicNavItems);

  // Function to render the appropriate navigation based on settings
  const renderNavigation = () => {
    // Header for search and profile
    const renderHeader = () => {
      const title = branchWithShop && branchWithShop.shop && branchWithShop.branch
        ? `${branchWithShop.shop.name} - ${branchWithShop.branch.name}`
        : 'Restaurant Dashboard';

      return (
        <div className="relative">
          <AuthHeader
            title={title}
            showSearch={true}
            showNotifications={true}
          />
          {/* Layout Switcher positioned absolutely */}
          <div className="absolute top-4 right-20 z-10">
            <LayoutSwitcher variant="compact" showLabel={false} />
          </div>
        </div>
      );
    };

    switch (settings.type) {
      case NavigationType.HEADER:
        return (
          <>
            {renderHeader()}
            <ExpandableHeaderNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
              autoHide={settings.autoHide}
            />
          </>
        );
      case NavigationType.SIDEBAR_LEFT:
      case NavigationType.SIDEBAR_RIGHT:
        return (
          <>
            {renderHeader()}
            <SheetSidebarNavigation
              navItems={navItems}
              position={settings.type === NavigationType.SIDEBAR_LEFT ? 'left' : 'right'}
              isCollapsed={isCollapsed}
              setIsCollapsed={setIsCollapsed}
              showIcons={settings.showIcons}
            />
          </>
        );
      case NavigationType.DRAWER:
        return (
          <>
            {renderHeader()}
            <DrawerNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
            />
          </>
        );
      case NavigationType.BOTTOM:
        return (
          <>
            {renderHeader()}
            <BottomNavigation
              navItems={navItems}
              showLabels={settings.showLabels}
              showIcons={settings.showIcons}
              autoHide={settings.autoHide}
            />
          </>
        );
      default:
        return (
          <>
            {renderHeader()}
            <ExpandableHeaderNavigation
              navItems={navItems}
              showIcons={settings.showIcons}
              autoHide={settings.autoHide}
            />
          </>
        );
    }
  };

  // Function to get the content class based on navigation type
  const getContentClass = () => {
    switch (settings.type) {
      case NavigationType.SIDEBAR_LEFT:
        return isCollapsed
          ? "ml-[80px]"
          : "ml-[240px]";
      case NavigationType.SIDEBAR_RIGHT:
        return isCollapsed
          ? "mr-[80px]"
          : "mr-[240px]";
      default:
        return "";
    }
  };

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-[#fbfaf9] font-be-vietnam overflow-x-hidden">
      {renderNavigation()}
      <div
        className={cn(
          "flex-1 transition-all duration-300",
          getContentClass()
        )}
      >
        <div className="px-4 md:px-6 lg:px-8 py-6">
          <div className="layout-content-container max-w-7xl mx-auto">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Main layout component that provides the navigation context
export default function BranchLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}) {
  // Use the React 'use' hook to unwrap the params promise in client component
  const { slugShop, slugBranch } = use(params);

  return (
    <NavigationProvider>
      <NotificationProvider shopSlug={slugShop} branchSlug={slugBranch}>
        <NavigationWrapper slugShop={slugShop} slugBranch={slugBranch}>
          {children}
        </NavigationWrapper>
      </NotificationProvider>
    </NavigationProvider>
  );
}
