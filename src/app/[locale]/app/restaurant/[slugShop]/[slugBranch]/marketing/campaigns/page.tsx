'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  Megaphone, 
  ArrowLeft,
  Plus, 
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Play,
  Pause,
  Stop,
  Calendar,
  DollarSign,
  Users,
  TrendingUp,
  Mail,
  MessageSquare,
  Share2,
  Gift,
  Star,
  RefreshCw,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types
interface Campaign {
  id: string;
  name: string;
  type: 'email' | 'social' | 'sms' | 'promotion' | 'loyalty';
  status: 'draft' | 'active' | 'paused' | 'completed' | 'scheduled';
  startDate: Date;
  endDate?: Date;
  budget: number;
  spent: number;
  reach: number;
  engagement: number;
  conversions: number;
  revenue: number;
  description: string;
  targetAudience: string;
  createdAt: Date;
}

export default function CampaignsPage() {
  const t = useTranslations('marketing');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // Mock data
  const mockCampaigns: Campaign[] = [
    {
      id: '1',
      name: 'Summer Special Menu',
      type: 'email',
      status: 'active',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
      budget: 500,
      spent: 320,
      reach: 2450,
      engagement: 18.5,
      conversions: 156,
      revenue: 3240,
      description: 'Promote new summer menu items with 20% discount',
      targetAudience: 'All customers',
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    },
    {
      id: '2',
      name: 'Happy Hour Social Media',
      type: 'social',
      status: 'active',
      startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000),
      budget: 300,
      spent: 180,
      reach: 5200,
      engagement: 12.3,
      conversions: 89,
      revenue: 1780,
      description: 'Daily happy hour promotion on social media',
      targetAudience: 'Young professionals',
      createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    },
    {
      id: '3',
      name: 'Loyalty Program Launch',
      type: 'loyalty',
      status: 'completed',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      budget: 1000,
      spent: 950,
      reach: 3800,
      engagement: 25.7,
      conversions: 342,
      revenue: 8500,
      description: 'Launch new customer loyalty rewards program',
      targetAudience: 'Frequent customers',
      createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
    },
    {
      id: '4',
      name: 'Weekend Brunch Promo',
      type: 'sms',
      status: 'scheduled',
      startDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
      budget: 200,
      spent: 0,
      reach: 0,
      engagement: 0,
      conversions: 0,
      revenue: 0,
      description: 'SMS campaign for weekend brunch special offers',
      targetAudience: 'Weekend diners',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    },
    {
      id: '5',
      name: 'Valentine\'s Day Special',
      type: 'promotion',
      status: 'draft',
      startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 35 * 24 * 60 * 60 * 1000),
      budget: 800,
      spent: 0,
      reach: 0,
      engagement: 0,
      conversions: 0,
      revenue: 0,
      description: 'Special Valentine\'s Day dinner packages for couples',
      targetAudience: 'Couples',
      createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    },
    {
      id: '6',
      name: 'Customer Feedback Survey',
      type: 'email',
      status: 'paused',
      startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000),
      budget: 150,
      spent: 75,
      reach: 1200,
      engagement: 8.5,
      conversions: 45,
      revenue: 0,
      description: 'Collect customer feedback with incentive offers',
      targetAudience: 'Recent customers',
      createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
    },
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setCampaigns(mockCampaigns);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter campaigns
  const filteredCampaigns = campaigns.filter(campaign => {
    const matchesSearch = campaign.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         campaign.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || campaign.status === statusFilter;
    const matchesType = typeFilter === 'all' || campaign.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // Get campaign type icon
  const getCampaignTypeIcon = (type: Campaign['type']) => {
    const icons = {
      email: Mail,
      social: Share2,
      sms: MessageSquare,
      promotion: Gift,
      loyalty: Star,
    };
    const Icon = icons[type];
    return <Icon className="w-4 h-4" />;
  };

  // Get status badge
  const getStatusBadge = (status: Campaign['status']) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800' },
      active: { color: 'bg-green-100 text-green-800' },
      paused: { color: 'bg-yellow-100 text-yellow-800' },
      completed: { color: 'bg-blue-100 text-blue-800' },
      scheduled: { color: 'bg-purple-100 text-purple-800' },
    };

    const config = statusConfig[status];

    return (
      <Badge className={config.color}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Calculate ROI
  const calculateROI = (revenue: number, spent: number) => {
    if (spent === 0) return 0;
    return ((revenue - spent) / spent * 100);
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Calculate campaign stats
  const campaignStats = {
    total: filteredCampaigns.length,
    active: filteredCampaigns.filter(c => c.status === 'active').length,
    totalBudget: filteredCampaigns.reduce((sum, c) => sum + c.budget, 0),
    totalSpent: filteredCampaigns.reduce((sum, c) => sum + c.spent, 0),
    totalRevenue: filteredCampaigns.reduce((sum, c) => sum + c.revenue, 0),
    totalReach: filteredCampaigns.reduce((sum, c) => sum + c.reach, 0),
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Marketing
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Megaphone className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Marketing Campaigns</h1>
              <p className="text-gray-600">Manage all your marketing campaigns</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/create`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              New Campaign
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
            <Megaphone className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaignStats.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <Play className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{campaignStats.active}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
            <DollarSign className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${campaignStats.totalBudget.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Spent</CardTitle>
            <DollarSign className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${campaignStats.totalSpent.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">${campaignStats.totalRevenue.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reach</CardTitle>
            <Users className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaignStats.totalReach.toLocaleString()}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search campaigns..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="paused">Paused</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="email">Email</SelectItem>
            <SelectItem value="social">Social Media</SelectItem>
            <SelectItem value="sms">SMS</SelectItem>
            <SelectItem value="promotion">Promotion</SelectItem>
            <SelectItem value="loyalty">Loyalty</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Campaigns Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Campaign</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Period</TableHead>
              <TableHead>Budget</TableHead>
              <TableHead>Performance</TableHead>
              <TableHead>ROI</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredCampaigns.map((campaign) => (
              <TableRow key={campaign.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{campaign.name}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {campaign.description}
                    </div>
                    <div className="text-xs text-gray-400 mt-1">
                      Target: {campaign.targetAudience}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    {getCampaignTypeIcon(campaign.type)}
                    <span className="capitalize">{campaign.type}</span>
                  </div>
                </TableCell>
                <TableCell>{getStatusBadge(campaign.status)}</TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{formatDate(campaign.startDate)}</div>
                    {campaign.endDate && (
                      <div className="text-gray-500">to {formatDate(campaign.endDate)}</div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">${campaign.budget.toLocaleString()}</div>
                    <div className="text-sm text-gray-500">
                      ${campaign.spent.toLocaleString()} spent
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                      <div 
                        className="bg-blue-600 h-1 rounded-full" 
                        style={{ width: `${Math.min((campaign.spent / campaign.budget) * 100, 100)}%` }}
                      ></div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">
                      {campaign.reach.toLocaleString()} reach
                    </div>
                    <div className="text-sm text-gray-500">
                      {campaign.conversions} conversions
                    </div>
                    {campaign.engagement > 0 && (
                      <div className="text-sm text-gray-500">
                        {campaign.engagement}% engagement
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  {campaign.spent > 0 ? (
                    <div>
                      <div className="font-medium">
                        ${campaign.revenue.toLocaleString()}
                      </div>
                      <div className={cn(
                        "text-sm font-medium",
                        calculateROI(campaign.revenue, campaign.spent) > 0 
                          ? "text-green-600" 
                          : "text-red-600"
                      )}>
                        {calculateROI(campaign.revenue, campaign.spent).toFixed(0)}% ROI
                      </div>
                    </div>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/${campaign.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      {(campaign.status === 'draft' || campaign.status === 'paused') && (
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/${campaign.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Campaign
                          </Link>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      {campaign.status === 'draft' && (
                        <DropdownMenuItem>
                          <Play className="mr-2 h-4 w-4" />
                          Start Campaign
                        </DropdownMenuItem>
                      )}
                      {campaign.status === 'active' && (
                        <DropdownMenuItem>
                          <Pause className="mr-2 h-4 w-4" />
                          Pause Campaign
                        </DropdownMenuItem>
                      )}
                      {campaign.status === 'paused' && (
                        <DropdownMenuItem>
                          <Play className="mr-2 h-4 w-4" />
                          Resume Campaign
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Campaign
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {filteredCampaigns.length === 0 && (
        <div className="text-center py-12">
          <Megaphone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No campaigns found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start by creating your first marketing campaign'}
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/campaigns/create`}>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create First Campaign
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
