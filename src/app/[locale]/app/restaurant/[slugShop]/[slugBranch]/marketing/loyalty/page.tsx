'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  Star, 
  ArrowLeft,
  Plus, 
  Search,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Gift,
  Crown,
  Award,
  Users,
  TrendingUp,
  DollarSign,
  Calendar,
  Settings,
  RefreshCw,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { <PERSON>, CardContent, CardHeader, <PERSON>Title } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

// Types
interface LoyaltyMember {
  id: string;
  customerName: string;
  email: string;
  phone?: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
  points: number;
  totalSpent: number;
  visits: number;
  joinDate: Date;
  lastVisit: Date;
  status: 'active' | 'inactive';
}

interface LoyaltyTier {
  name: string;
  minSpent: number;
  pointsMultiplier: number;
  benefits: string[];
  color: string;
  icon: React.ReactNode;
}

interface LoyaltyStats {
  totalMembers: number;
  activeMembers: number;
  pointsIssued: number;
  pointsRedeemed: number;
  totalRewards: number;
  avgSpendPerMember: number;
}

export default function LoyaltyPage() {
  const t = useTranslations('marketing');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [members, setMembers] = useState<LoyaltyMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  // Loyalty tiers configuration
  const loyaltyTiers: LoyaltyTier[] = [
    {
      name: 'Bronze',
      minSpent: 0,
      pointsMultiplier: 1,
      benefits: ['1 point per $1 spent', 'Birthday discount'],
      color: 'text-orange-600',
      icon: <Award className="w-4 h-4" />
    },
    {
      name: 'Silver',
      minSpent: 500,
      pointsMultiplier: 1.25,
      benefits: ['1.25 points per $1 spent', 'Priority reservations', '5% discount'],
      color: 'text-gray-600',
      icon: <Star className="w-4 h-4" />
    },
    {
      name: 'Gold',
      minSpent: 1500,
      pointsMultiplier: 1.5,
      benefits: ['1.5 points per $1 spent', 'Free appetizer monthly', '10% discount'],
      color: 'text-yellow-600',
      icon: <Crown className="w-4 h-4" />
    },
    {
      name: 'Platinum',
      minSpent: 3000,
      pointsMultiplier: 2,
      benefits: ['2 points per $1 spent', 'Free meal on birthday', '15% discount', 'VIP events'],
      color: 'text-purple-600',
      icon: <Crown className="w-4 h-4" />
    }
  ];

  // Mock data
  const mockMembers: LoyaltyMember[] = [
    {
      id: '1',
      customerName: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+1234567890',
      tier: 'gold',
      points: 2450,
      totalSpent: 1850,
      visits: 47,
      joinDate: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      status: 'active',
    },
    {
      id: '2',
      customerName: 'Michael Chen',
      email: '<EMAIL>',
      phone: '+1234567891',
      tier: 'platinum',
      points: 4200,
      totalSpent: 3240,
      visits: 89,
      joinDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      status: 'active',
    },
    {
      id: '3',
      customerName: 'Emily Davis',
      email: '<EMAIL>',
      tier: 'silver',
      points: 890,
      totalSpent: 720,
      visits: 28,
      joinDate: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      status: 'active',
    },
    {
      id: '4',
      customerName: 'David Wilson',
      email: '<EMAIL>',
      tier: 'bronze',
      points: 340,
      totalSpent: 280,
      visits: 12,
      joinDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      status: 'active',
    },
    {
      id: '5',
      customerName: 'Lisa Anderson',
      email: '<EMAIL>',
      tier: 'silver',
      points: 1120,
      totalSpent: 950,
      visits: 35,
      joinDate: new Date(Date.now() - 200 * 24 * 60 * 60 * 1000),
      lastVisit: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      status: 'inactive',
    },
  ];

  const mockStats: LoyaltyStats = {
    totalMembers: mockMembers.length,
    activeMembers: mockMembers.filter(m => m.status === 'active').length,
    pointsIssued: mockMembers.reduce((sum, m) => sum + m.points, 0) + 5000, // Including redeemed points
    pointsRedeemed: 5000,
    totalRewards: 125, // Number of rewards redeemed
    avgSpendPerMember: mockMembers.reduce((sum, m) => sum + m.totalSpent, 0) / mockMembers.length,
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setMembers(mockMembers);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter members
  const filteredMembers = members.filter(member => {
    const matchesSearch = member.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesSearch;
  });

  // Get tier badge
  const getTierBadge = (tier: LoyaltyMember['tier']) => {
    const tierConfig = loyaltyTiers.find(t => t.name.toLowerCase() === tier);
    if (!tierConfig) return null;

    return (
      <Badge className={cn('flex items-center gap-1', tierConfig.color)}>
        {tierConfig.icon}
        {tierConfig.name}
      </Badge>
    );
  };

  // Get tier progress
  const getTierProgress = (member: LoyaltyMember) => {
    const currentTierIndex = loyaltyTiers.findIndex(t => t.name.toLowerCase() === member.tier);
    const nextTier = loyaltyTiers[currentTierIndex + 1];
    
    if (!nextTier) return null;

    const progress = (member.totalSpent / nextTier.minSpent) * 100;
    const remaining = nextTier.minSpent - member.totalSpent;

    return {
      nextTier: nextTier.name,
      progress: Math.min(progress, 100),
      remaining: Math.max(remaining, 0)
    };
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return 'Today';
    if (days === 1) return 'Yesterday';
    if (days < 30) return `${days} days ago`;
    if (days < 365) return `${Math.floor(days / 30)} months ago`;
    return `${Math.floor(days / 365)} years ago`;
  };

  // Get tier distribution
  const tierDistribution = loyaltyTiers.map(tier => ({
    ...tier,
    count: members.filter(m => m.tier === tier.name.toLowerCase()).length
  }));

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Marketing
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Star className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Loyalty Program</h1>
              <p className="text-gray-600">Manage customer loyalty and rewards</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/loyalty/settings`}>
            <Button variant="outline" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Settings
            </Button>
          </Link>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/loyalty/members/add`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              Add Member
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalMembers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Members</CardTitle>
            <Star className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{mockStats.activeMembers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Points Issued</CardTitle>
            <Gift className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.pointsIssued.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Points Redeemed</CardTitle>
            <Award className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.pointsRedeemed.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Rewards Given</CardTitle>
            <Gift className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalRewards}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Spend</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockStats.avgSpendPerMember.toFixed(0)}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tier Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Loyalty Tiers</CardTitle>
          <p className="text-sm text-gray-600">Member distribution across loyalty tiers</p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {tierDistribution.map((tier) => (
              <div key={tier.name} className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <div className={tier.color}>
                    {tier.icon}
                  </div>
                  <h3 className="font-semibold">{tier.name}</h3>
                </div>
                <div className="space-y-2">
                  <div className="text-2xl font-bold">{tier.count}</div>
                  <div className="text-sm text-gray-600">
                    {tier.count > 0 ? `${((tier.count / mockStats.totalMembers) * 100).toFixed(1)}%` : '0%'} of members
                  </div>
                  <div className="text-xs text-gray-500">
                    Min spend: ${tier.minSpent}
                  </div>
                  <div className="text-xs text-gray-500">
                    {tier.pointsMultiplier}x points multiplier
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Search */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search members..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </div>

      {/* Members Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Member</TableHead>
              <TableHead>Tier</TableHead>
              <TableHead>Points</TableHead>
              <TableHead>Total Spent</TableHead>
              <TableHead>Visits</TableHead>
              <TableHead>Progress</TableHead>
              <TableHead>Last Visit</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMembers.map((member) => {
              const tierProgress = getTierProgress(member);
              
              return (
                <TableRow key={member.id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{member.customerName}</div>
                      <div className="text-sm text-gray-500">{member.email}</div>
                      {member.phone && (
                        <div className="text-sm text-gray-500">{member.phone}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getTierBadge(member.tier)}</TableCell>
                  <TableCell>
                    <div className="font-medium">{member.points.toLocaleString()}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">${member.totalSpent.toLocaleString()}</div>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{member.visits}</div>
                  </TableCell>
                  <TableCell>
                    {tierProgress ? (
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span>To {tierProgress.nextTier}</span>
                          <span>${tierProgress.remaining} left</span>
                        </div>
                        <Progress value={tierProgress.progress} className="h-2" />
                      </div>
                    ) : (
                      <Badge className="bg-purple-100 text-purple-800">
                        Max Tier
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatTimeAgo(member.lastVisit)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/loyalty/members/${member.id}`}>
                            <Eye className="mr-2 h-4 w-4" />
                            View Profile
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/loyalty/members/${member.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Member
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Gift className="mr-2 h-4 w-4" />
                          Award Points
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="mr-2 h-4 w-4" />
                          Remove Member
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {filteredMembers.length === 0 && (
        <div className="text-center py-12">
          <Star className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No loyalty members found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery
              ? 'Try adjusting your search terms'
              : 'Start building customer loyalty by adding your first member'}
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/loyalty/members/add`}>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add First Member
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
