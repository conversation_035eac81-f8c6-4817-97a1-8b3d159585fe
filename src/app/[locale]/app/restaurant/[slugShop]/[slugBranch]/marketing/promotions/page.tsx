'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  Gift, 
  ArrowLeft,
  Plus, 
  Search,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Calendar,
  DollarSign,
  Percent,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Copy
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Types
interface Promotion {
  id: string;
  name: string;
  code: string;
  type: 'percentage' | 'fixed' | 'bogo' | 'free-item' | 'free-delivery';
  value: number;
  description: string;
  status: 'active' | 'inactive' | 'expired' | 'scheduled';
  startDate: Date;
  endDate: Date;
  usageLimit?: number;
  usageCount: number;
  minOrderValue?: number;
  applicableItems?: string[];
  customerSegment?: string;
  createdAt: Date;
}

interface PromotionStats {
  totalPromotions: number;
  activePromotions: number;
  totalUsage: number;
  totalSavings: number;
  conversionRate: number;
}

export default function PromotionsPage() {
  const t = useTranslations('marketing');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  // Mock data
  const mockPromotions: Promotion[] = [
    {
      id: '1',
      name: 'Summer Special 20% Off',
      code: 'SUMMER20',
      type: 'percentage',
      value: 20,
      description: '20% off on all summer menu items',
      status: 'active',
      startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000),
      usageLimit: 500,
      usageCount: 156,
      minOrderValue: 25,
      applicableItems: ['Summer Salad', 'Iced Coffee', 'Fruit Bowl'],
      customerSegment: 'All customers',
      createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    },
    {
      id: '2',
      name: 'Happy Hour Buy One Get One',
      code: 'HAPPYBOGO',
      type: 'bogo',
      value: 1,
      description: 'Buy one drink, get one free during happy hour',
      status: 'active',
      startDate: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 16 * 24 * 60 * 60 * 1000),
      usageLimit: 200,
      usageCount: 89,
      applicableItems: ['Beer', 'Wine', 'Cocktails'],
      customerSegment: 'All customers',
      createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
    },
    {
      id: '3',
      name: 'First Order $10 Off',
      code: 'WELCOME10',
      type: 'fixed',
      value: 10,
      description: '$10 off for new customers on their first order',
      status: 'active',
      startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
      usageLimit: 1000,
      usageCount: 342,
      minOrderValue: 30,
      customerSegment: 'New customers',
      createdAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
    },
    {
      id: '4',
      name: 'Free Delivery Weekend',
      code: 'FREEDEL',
      type: 'free-delivery',
      value: 0,
      description: 'Free delivery on all orders during weekends',
      status: 'scheduled',
      startDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
      usageLimit: 300,
      usageCount: 0,
      minOrderValue: 20,
      customerSegment: 'All customers',
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    },
    {
      id: '5',
      name: 'Loyalty Member 15% Off',
      code: 'LOYAL15',
      type: 'percentage',
      value: 15,
      description: 'Exclusive 15% discount for loyalty program members',
      status: 'active',
      startDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      usageCount: 234,
      customerSegment: 'Loyalty members',
      createdAt: new Date(Date.now() - 65 * 24 * 60 * 60 * 1000),
    },
    {
      id: '6',
      name: 'Student Discount',
      code: 'STUDENT',
      type: 'percentage',
      value: 10,
      description: '10% discount for students with valid ID',
      status: 'expired',
      startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
      endDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      usageCount: 156,
      customerSegment: 'Students',
      createdAt: new Date(Date.now() - 95 * 24 * 60 * 60 * 1000),
    },
  ];

  const mockStats: PromotionStats = {
    totalPromotions: mockPromotions.length,
    activePromotions: mockPromotions.filter(p => p.status === 'active').length,
    totalUsage: mockPromotions.reduce((sum, p) => sum + p.usageCount, 0),
    totalSavings: 8450, // Mock total savings amount
    conversionRate: 12.5, // Mock conversion rate percentage
  };

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setPromotions(mockPromotions);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Filter promotions
  const filteredPromotions = promotions.filter(promotion => {
    const matchesSearch = promotion.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         promotion.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         promotion.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || promotion.status === statusFilter;
    const matchesType = typeFilter === 'all' || promotion.type === typeFilter;
    
    return matchesSearch && matchesStatus && matchesType;
  });

  // Get promotion type badge
  const getTypeBadge = (type: Promotion['type']) => {
    const typeConfig = {
      percentage: { color: 'bg-blue-100 text-blue-800', label: 'Percentage' },
      fixed: { color: 'bg-green-100 text-green-800', label: 'Fixed Amount' },
      bogo: { color: 'bg-purple-100 text-purple-800', label: 'BOGO' },
      'free-item': { color: 'bg-orange-100 text-orange-800', label: 'Free Item' },
      'free-delivery': { color: 'bg-yellow-100 text-yellow-800', label: 'Free Delivery' },
    };

    const config = typeConfig[type];

    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Get status badge
  const getStatusBadge = (status: Promotion['status']) => {
    const statusConfig = {
      active: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      inactive: { color: 'bg-gray-100 text-gray-800', icon: XCircle },
      expired: { color: 'bg-red-100 text-red-800', icon: Clock },
      scheduled: { color: 'bg-purple-100 text-purple-800', icon: Calendar },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  // Format promotion value
  const formatPromotionValue = (type: Promotion['type'], value: number) => {
    switch (type) {
      case 'percentage':
        return `${value}%`;
      case 'fixed':
        return `$${value}`;
      case 'bogo':
        return 'Buy 1 Get 1';
      case 'free-item':
        return 'Free Item';
      case 'free-delivery':
        return 'Free Delivery';
      default:
        return value.toString();
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Copy promotion code
  const copyPromotionCode = (code: string) => {
    navigator.clipboard.writeText(code);
    // You could add a toast notification here
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Marketing
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Gift className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Promotions</h1>
              <p className="text-gray-600">Create and manage promotional offers</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/promotions/create`}>
            <Button className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              New Promotion
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Promotions</CardTitle>
            <Gift className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalPromotions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{mockStats.activePromotions}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.totalUsage.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${mockStats.totalSavings.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Conversion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{mockStats.conversionRate}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Search promotions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
            <SelectItem value="expired">Expired</SelectItem>
            <SelectItem value="scheduled">Scheduled</SelectItem>
          </SelectContent>
        </Select>

        <Select value={typeFilter} onValueChange={setTypeFilter}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="percentage">Percentage</SelectItem>
            <SelectItem value="fixed">Fixed Amount</SelectItem>
            <SelectItem value="bogo">BOGO</SelectItem>
            <SelectItem value="free-item">Free Item</SelectItem>
            <SelectItem value="free-delivery">Free Delivery</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Promotions Table */}
      <div className="bg-white rounded-lg border border-gray-200">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Promotion</TableHead>
              <TableHead>Code</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Value</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Usage</TableHead>
              <TableHead>Period</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPromotions.map((promotion) => (
              <TableRow key={promotion.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{promotion.name}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {promotion.description}
                    </div>
                    {promotion.customerSegment && (
                      <div className="text-xs text-gray-400 mt-1">
                        Target: {promotion.customerSegment}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <code className="px-2 py-1 bg-gray-100 rounded text-sm font-mono">
                      {promotion.code}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyPromotionCode(promotion.code)}
                      className="h-6 w-6 p-0"
                    >
                      <Copy className="h-3 w-3" />
                    </Button>
                  </div>
                </TableCell>
                <TableCell>{getTypeBadge(promotion.type)}</TableCell>
                <TableCell>
                  <div className="font-medium">
                    {formatPromotionValue(promotion.type, promotion.value)}
                  </div>
                  {promotion.minOrderValue && (
                    <div className="text-sm text-gray-500">
                      Min: ${promotion.minOrderValue}
                    </div>
                  )}
                </TableCell>
                <TableCell>{getStatusBadge(promotion.status)}</TableCell>
                <TableCell>
                  <div>
                    <div className="font-medium">{promotion.usageCount}</div>
                    {promotion.usageLimit && (
                      <div className="text-sm text-gray-500">
                        of {promotion.usageLimit} limit
                      </div>
                    )}
                    {promotion.usageLimit && (
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div 
                          className="bg-blue-600 h-1 rounded-full" 
                          style={{ width: `${Math.min((promotion.usageCount / promotion.usageLimit) * 100, 100)}%` }}
                        ></div>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    <div>{formatDate(promotion.startDate)}</div>
                    <div className="text-gray-500">to {formatDate(promotion.endDate)}</div>
                  </div>
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem asChild>
                        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/promotions/${promotion.id}`}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Details
                        </Link>
                      </DropdownMenuItem>
                      {promotion.status !== 'expired' && (
                        <DropdownMenuItem asChild>
                          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/promotions/${promotion.id}/edit`}>
                            <Edit className="mr-2 h-4 w-4" />
                            Edit Promotion
                          </Link>
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={() => copyPromotionCode(promotion.code)}>
                        <Copy className="mr-2 h-4 w-4" />
                        Copy Code
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem className="text-red-600">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Delete Promotion
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Empty State */}
      {filteredPromotions.length === 0 && (
        <div className="text-center py-12">
          <Gift className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No promotions found</h3>
          <p className="text-gray-600 mb-4">
            {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Start by creating your first promotional offer'}
          </p>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/marketing/promotions/create`}>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create First Promotion
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
}
