'use client';

import React from 'react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMenuItemByShopBranchSlugQuery } from '@/lib/redux/api/endpoints/restaurant/menuApi';
import { useGetShopBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { MenuItemForm } from '../../_components';

interface MenuItemEditPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    itemSlug: string;
  }>;
}

export default function MenuItemEditPage({ params }: MenuItemEditPageProps) {
  const { slugShop, slugBranch, itemSlug } = React.use(params);

  // Get shop data by slug to get the shop ID for the update mutation
  const {
    data: shopData,
    isLoading: isLoadingShop,
    error: shopError
  } = useGetShopBySlugQuery(slugShop);

  // Get menu item data from backend by slug - this API call validates shop, branch, and item existence
  const {
    data: menuItem,
    isLoading: isLoadingMenuItem,
    error: menuItemError
  } = useGetMenuItemByShopBranchSlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    itemSlug: itemSlug,
  });

  // Show loading state
  if (isLoadingShop || isLoadingMenuItem) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  // Show error state if shop or menu item not found
  if (!menuItem || menuItemError || !shopData || shopError) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Menu Item Not Found</h1>
          <p className="text-muted-foreground text-sm">The menu item you are looking for does not exist or the shop/branch combination is invalid.</p>
        </div>
      </div>
    );
  }

  // Get branchId from shop data - find the branch by slug
  const branch = shopData.branches?.find(b => b.slug === slugBranch);
  if (!branch?.id) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-muted-foreground text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <MenuItemForm
      mode="edit"
      slugShop={slugShop}
      slugBranch={slugBranch}
      shopId={shopData.id}
      branchId={branch.id}
      initialData={menuItem}
    />
  );
}
