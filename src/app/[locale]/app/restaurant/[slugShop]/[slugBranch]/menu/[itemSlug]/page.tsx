'use client';

import React, { use } from 'react';
import { Link } from '@/i18n/navigation';
import { AppLoading } from '@/components/ui/app-loading';
import { FallbackBackgroundImage } from '@/components/ui/fallback-image';
import { Button } from '@/components/ui/button';
import { Edit } from 'lucide-react';
import { useGetMenuItemByShopBranchSlugQuery } from '@/lib/redux/api/endpoints/restaurant/menuApi';

interface MenuItemDetailPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
    itemSlug: string;
  }>;
}

export default function MenuItemDetailPage({ params }: MenuItemDetailPageProps) {
  const { slugShop, slugBranch, itemSlug } = use(params);

  // Get menu item data from backend by slug - this API call validates shop, branch, and item existence
  const {
    data: menuItem,
    isLoading: isLoadingMenuItem,
    error: menuItemError
  } = useGetMenuItemByShopBranchSlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    itemSlug: itemSlug,
  });

  if (isLoadingMenuItem) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!menuItem || menuItemError) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-[#161412] text-[32px] font-bold leading-tight mb-2">Menu Item Not Found</h1>
          <p className="text-[#81766a] text-sm">The menu item you are looking for does not exist or the shop/branch combination is invalid.</p>
        </div>
      </div>
    );
  }

  // Extract data from the backend response (using actual backend structure)
  const tags = menuItem.tags || [];
  const ingredients = menuItem.ingredients?.join(', ') || 'No ingredients listed';
  const allergens = menuItem.allergens?.join(', ') || 'No allergens listed';

  // Handle nutritional info from backend structure
  const nutritionalInfo = menuItem.nutritionalInfo
    ? `Calories: ${menuItem.nutritionalInfo.calories || 'N/A'}, Fat: ${menuItem.nutritionalInfo.fat || 'N/A'}g, Protein: ${menuItem.nutritionalInfo.protein || 'N/A'}g, Carbs: ${menuItem.nutritionalInfo.carbohydrates || 'N/A'}g`
    : 'No nutritional information available';

  const preparationTime = menuItem.preparationTime
    ? `${menuItem.preparationTime} minutes`
    : 'Preparation time not specified';

  // Use image from backend structure
  const primaryImage = menuItem.primaryImage ||
    (menuItem.images && menuItem.images.length > 0 ? menuItem.images[0] : '') ||
    menuItem.image || '';

  // Get category name from backend structure
  const categoryName = menuItem.category?.name || menuItem.categoryName || 'Uncategorized';

  // Get dietary information from boolean flags and dietaryInfo object
  const dietaryLabels = [];
  if (menuItem.isVegetarian || menuItem.dietaryInfo?.vegetarian) dietaryLabels.push('Vegetarian');
  if (menuItem.isVegan || menuItem.dietaryInfo?.vegan) dietaryLabels.push('Vegan');
  if (menuItem.isGlutenFree || menuItem.dietaryInfo?.gluten_free) dietaryLabels.push('Gluten Free');
  if (menuItem.isSpicy) {
    const spiceText = menuItem.spiceLevelText || (menuItem.spiceLevel ? `Level ${menuItem.spiceLevel}` : '');
    dietaryLabels.push(`Spicy ${spiceText}`.trim());
  }

  // Get availability status
  const isAvailable = menuItem.isAvailable;

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-white group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-5">
          {/* Main Content */}
          <div className="layout-content-container flex flex-col max-w-[960px] flex-1">
            {/* Breadcrumb */}
            <div className="flex flex-wrap gap-2 p-4">
              <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu`} className="text-[#81766a] text-base font-medium leading-normal">
                Menu
              </Link>
              <span className="text-[#81766a] text-base font-medium leading-normal">/</span>
              <span className="text-[#161412] text-base font-medium leading-normal">{menuItem.name}</span>
            </div>

            {/* Header */}
            <div className="flex flex-wrap justify-between gap-3 p-4">
              <div className="flex min-w-72 flex-col gap-3">
                <p className="text-[#161412] tracking-light text-[32px] font-bold leading-tight">{menuItem.name}</p>
                <p className="text-[#81766a] text-sm font-normal leading-normal">
                  {menuItem.description}
                </p>
              </div>
              <div className="flex items-start">
                <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${itemSlug}/edit`}>
                  <Button className="bg-[#887663] hover:bg-[#6d5a48] text-white flex items-center gap-2">
                    <Edit className="h-4 w-4" />
                    Edit Menu Item
                  </Button>
                </Link>
              </div>
            </div>

            {/* Image */}
            <div className="flex w-full grow bg-background p-4">
              <div className="w-full gap-1 overflow-hidden bg-background aspect-[3/2] rounded-xl flex">
                <FallbackBackgroundImage
                  src={primaryImage}
                  className="w-full bg-center bg-no-repeat bg-cover aspect-auto rounded-none flex-1"
                  fallbackSrc="/placeholder-image.svg"
                />
              </div>
            </div>

            {/* Description */}
            <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Description</h3>
            <p className="text-foreground text-base font-normal leading-normal pb-3 pt-1 px-4">
              {menuItem.description}
            </p>

            {/* Tags */}
            <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Tags</h3>
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              {tags.length > 0 ? (
                tags.map((tag, index) => (
                  <div key={index} className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-muted pl-4 pr-4">
                    <p className="text-foreground text-sm font-medium leading-normal">{tag}</p>
                  </div>
                ))
              ) : (
                <p className="text-muted-foreground text-sm px-4">No tags available</p>
              )}
            </div>

            {/* Category */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Category</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{categoryName}</p>

            {/* Ingredients */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Ingredients</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{ingredients}</p>

            {/* Allergens */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Allergens</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{allergens}</p>

            {/* Dietary Information */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Dietary Information</h3>
            <div className="flex gap-3 p-3 flex-wrap pr-4">
              {dietaryLabels.length > 0 ? (
                dietaryLabels.map((label, index) => (
                  <div key={index} className="flex h-8 shrink-0 items-center justify-center gap-x-2 rounded-full bg-[#e8f5e8] pl-4 pr-4">
                    <p className="text-[#2d5a2d] text-sm font-medium leading-normal">{label}</p>
                  </div>
                ))
              ) : (
                <p className="text-[#81766a] text-sm px-4">No dietary information available</p>
              )}
            </div>

            {/* Pricing */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Pricing</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">${menuItem.price.toFixed(2)}</p>

            {/* Availability Status */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Availability</h3>
            <div className="px-4 pb-3 pt-1">
              <div className={`inline-flex h-8 items-center justify-center gap-x-2 rounded-full px-4 ${
                isAvailable
                  ? 'bg-[#e8f5e8] text-[#2d5a2d]'
                  : 'bg-[#fce8e8] text-[#8b2635]'
              }`}>
                <p className="text-sm font-medium leading-normal">
                  {isAvailable ? 'Available' : 'Currently Unavailable'}
                </p>
              </div>
            </div>

            {/* Nutritional Information */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Nutritional Information</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{nutritionalInfo}</p>

            {/* Preparation Time */}
            <h3 className="text-[#161412] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Preparation Time</h3>
            <p className="text-[#161412] text-base font-normal leading-normal pb-3 pt-1 px-4">{preparationTime}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
