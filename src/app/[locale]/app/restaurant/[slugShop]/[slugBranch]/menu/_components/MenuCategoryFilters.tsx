'use client';

import React from 'react';
import { Badge } from '@/components/ui/badge';

interface MenuCategoryFiltersProps {
  categories: string[];
  selectedCategory?: string;
  onCategoryChange: (category?: string) => void;
}

export function MenuCategoryFilters({
  categories,
  selectedCategory,
  onCategoryChange
}: MenuCategoryFiltersProps) {
  // Add some default categories if none exist, similar to the design
  const defaultCategories = ['Pizza', 'Salads', 'Pasta', 'Seafood', 'Desserts'];
  const displayCategories = categories.length > 0 ? categories : defaultCategories;
  const allCategories = ['All', ...displayCategories];

  return (
    <div className="flex gap-3 p-4 flex-wrap">
      {allCategories.map((category) => {
        const isSelected = category === 'All' ? !selectedCategory : selectedCategory === category;

        return (
          <button
            key={category}
            onClick={() => onCategoryChange(category === 'All' ? undefined : category)}
            className="transition-all duration-200"
          >
            <Badge
              variant={isSelected ? "default" : "secondary"}
              className={`
                h-8 px-4 text-sm font-medium cursor-pointer rounded-full
                ${isSelected
                  ? 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm'
                  : 'bg-muted text-muted-foreground hover:bg-muted/80 hover:text-foreground border border-border'
                }
              `}
            >
              {category}
            </Badge>
          </button>
        );
      })}
    </div>
  );
}
