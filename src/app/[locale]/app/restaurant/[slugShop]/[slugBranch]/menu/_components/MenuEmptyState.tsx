'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Plus, UtensilsCrossed, Search } from 'lucide-react';

interface MenuEmptyStateProps {
  slugShop: string;
  slugBranch: string;
  searchTerm?: string;
  hasFilters?: boolean;
}

export function MenuEmptyState({
  slugShop,
  slugBranch,
  searchTerm,
  hasFilters = false
}: MenuEmptyStateProps) {
  const isSearchResult = searchTerm || hasFilters;

  return (
    <div className="text-center py-12 px-4">
      {isSearchResult ? (
        <Search className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
      ) : (
        <UtensilsCrossed className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
      )}

      <h3 className="text-lg font-medium text-foreground mb-2">
        {isSearchResult ? 'No menu items match your search' : 'No menu items found'}
      </h3>

      <p className="text-muted-foreground mb-6 max-w-md mx-auto">
        {isSearchResult
          ? 'Try adjusting your search terms or filters to find what you\'re looking for.'
          : 'Start building your menu by adding your first menu item. You can organize items by categories and set prices.'
        }
      </p>

      {!isSearchResult && (
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/add`}>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Add First Menu Item
          </Button>
        </Link>
      )}
    </div>
  );
}
