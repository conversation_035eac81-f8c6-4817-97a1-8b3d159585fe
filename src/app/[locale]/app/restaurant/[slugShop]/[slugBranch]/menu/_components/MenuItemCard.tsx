'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { MenuItemImage } from '@/components/ui/image-with-fallback';

interface MenuItem {
  id: string;
  name: string;
  slug: string;
  description?: string;
  category: string;
  price: number;
  image?: string;
  available: boolean;
}

interface MenuItemCardProps {
  item: MenuItem;
  slugShop: string;
  slugBranch: string;
}

export function MenuItemCard({ item, slugShop, slugBranch }: MenuItemCardProps) {
  return (
    <Link key={item.id} href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.slug}`}>
      <div className="group flex flex-col gap-3 p-4 bg-card rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-primary/20 transition-all duration-300 hover:-translate-y-1">
        <MenuItemImage
          src={item.image || ''}
          alt={item.name}
          containerClassName="aspect-square rounded-lg overflow-hidden bg-muted"
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />
        <div className="space-y-2">
          <h3 className="text-foreground text-base font-semibold leading-tight line-clamp-2 group-hover:text-primary transition-colors duration-200">
            {item.name}
          </h3>
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground text-sm font-medium bg-muted px-2 py-1 rounded-md">
              {item.category}
            </span>
            <span className="text-foreground text-lg font-bold">
              ${item.price.toFixed(2)}
            </span>
          </div>
          {!item.available && (
            <div className="text-destructive text-xs font-medium bg-destructive/10 px-2 py-1 rounded-md">
              Currently Unavailable
            </div>
          )}
        </div>
      </div>
    </Link>
  );
}
