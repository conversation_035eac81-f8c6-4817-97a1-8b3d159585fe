'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from '@/i18n/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { ArrowLeft, Save, Upload, X, Plus } from 'lucide-react';
import { Link } from '@/i18n/navigation';
import { useCreateMenuItemMutation, useUpdateMenuItemMutation, useUploadMenuItemImageMutation, useGetMenuCategoriesQuery, useCreateMenuCategoryMutation } from '@/lib/redux/api/endpoints/restaurant/menuApi';
import { MenuItem } from '@/lib/redux/api/endpoints/restaurant/menuApi';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import Image from 'next/image';

// Form schema
const menuItemFormSchema = z.object({
  name: z.string().min(1, 'Item name is required'),
  description: z.string().optional(),
  price: z.coerce.number().min(0, 'Price must be a positive number'),
  cost: z.coerce.number().min(0).optional(),
  categoryId: z.string().min(1, 'Category is required'),
  preparationTime: z.coerce.number().min(0).optional(),
  isAvailable: z.boolean().optional(),
  isVegetarian: z.boolean().optional(),
  isVegan: z.boolean().optional(),
  isGlutenFree: z.boolean().optional(),
  isSpicy: z.boolean().optional(),
  spiceLevel: z.coerce.number().min(0).max(5).optional(),
  ingredients: z.array(z.string()).optional(),
  allergens: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
});

type MenuItemFormValues = z.infer<typeof menuItemFormSchema>;

interface MenuItemFormProps {
  mode: 'create' | 'edit';
  slugShop: string;
  slugBranch: string;
  shopId: string;
  branchId: string;
  initialData?: MenuItem;
  onSuccess?: () => void;
}

export function MenuItemForm({
  mode,
  slugShop,
  slugBranch,
  shopId,
  branchId,
  initialData,
  onSuccess
}: MenuItemFormProps) {
  const router = useRouter();
  const [createMenuItem, { isLoading: isCreating }] = useCreateMenuItemMutation();
  const [updateMenuItem, { isLoading: isUpdating }] = useUpdateMenuItemMutation();
  const [uploadMenuItemImage, { isLoading: isUploading }] = useUploadMenuItemImageMutation();
  const [createMenuCategory, { isLoading: isCreatingCategory }] = useCreateMenuCategoryMutation();

  // Fetch categories
  const { data: categoriesResponse, isLoading: isLoadingCategories } = useGetMenuCategoriesQuery({
    merchantId: shopId,
    branchId: branchId
  });

  const categories = categoriesResponse?.data || [];

  // Helper function to create default categories manually
  const createDefaultCategories = useCallback(async () => {
    const defaultCategories = [
      {
        name: 'Appetizers',
        description: 'Start your meal with our delicious appetizers',
        sortOrder: 1
      },
      {
        name: 'Main Courses',
        description: 'Our signature main dishes',
        sortOrder: 2
      },
      {
        name: 'Desserts',
        description: 'Sweet endings to your meal',
        sortOrder: 3
      },
      {
        name: 'Beverages',
        description: 'Refreshing drinks and beverages',
        sortOrder: 4
      }
    ];

    try {
      for (const category of defaultCategories) {
        await createMenuCategory({
          merchantId: shopId,
          branchId: branchId,
          categoryData: category
        }).unwrap();
      }
      toast.success('Created default menu categories');
    } catch (error) {
      console.error('Failed to create default categories:', error);
      toast.error('Failed to create default categories');
    }
  }, [createMenuCategory, shopId, branchId]);

  // Auto-create initial categories if none exist
  useEffect(() => {
    if (categoriesResponse && categories.length === 0 && !isCreatingCategory) {
      createDefaultCategories();
    }
  }, [categoriesResponse, categories.length, isCreatingCategory, createDefaultCategories]);

  // State for image upload
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // State for dynamic arrays
  const [newIngredient, setNewIngredient] = useState('');
  const [newAllergen, setNewAllergen] = useState('');
  const [newTag, setNewTag] = useState('');

  const isLoading = isCreating || isUpdating || isUploading || isCreatingCategory;

  // Form setup
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
    getValues
  } = useForm<MenuItemFormValues>({
    resolver: zodResolver(menuItemFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      price: initialData?.price || 0,
      cost: initialData?.cost,
      categoryId: initialData?.categoryId || '',
      preparationTime: initialData?.preparationTime,
      isAvailable: initialData?.isAvailable,
      isVegetarian: initialData?.isVegetarian,
      isVegan: initialData?.isVegan,
      isGlutenFree: initialData?.isGlutenFree,
      isSpicy: initialData?.isSpicy,
      spiceLevel: initialData?.spiceLevel,
      ingredients: initialData?.ingredients || [],
      allergens: initialData?.allergens || [],
      tags: initialData?.tags || [],
    },
  });

  // Watch form values for dynamic updates
  const isSpicy = watch('isSpicy');

  // Helper functions for managing arrays
  const addIngredient = () => {
    if (newIngredient.trim()) {
      const currentIngredients = getValues('ingredients') || [];
      setValue('ingredients', [...currentIngredients, newIngredient.trim()]);
      setNewIngredient('');
    }
  };

  const removeIngredient = (index: number) => {
    const currentIngredients = getValues('ingredients') || [];
    setValue('ingredients', currentIngredients.filter((_, i) => i !== index));
  };

  const addAllergen = () => {
    if (newAllergen.trim()) {
      const currentAllergens = getValues('allergens') || [];
      setValue('allergens', [...currentAllergens, newAllergen.trim()]);
      setNewAllergen('');
    }
  };

  const removeAllergen = (index: number) => {
    const currentAllergens = getValues('allergens') || [];
    setValue('allergens', currentAllergens.filter((_, i) => i !== index));
  };

  const addTag = () => {
    if (newTag.trim()) {
      const currentTags = getValues('tags') || [];
      setValue('tags', [...currentTags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (index: number) => {
    const currentTags = getValues('tags') || [];
    setValue('tags', currentTags.filter((_, i) => i !== index));
  };

  // Set initial image preview for edit mode
  useEffect(() => {
    if (mode === 'edit' && initialData?.primaryImage) {
      setImagePreview(initialData.primaryImage);
    }
  }, [mode, initialData]);

  // Cleanup blob URLs on component unmount
  useEffect(() => {
    return () => {
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }

      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('File size must be less than 5MB');
        return;
      }

      // Clean up previous blob URL to prevent memory leaks
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }

      // Create blob URL for preview
      const blobUrl = URL.createObjectURL(file);
      setImagePreview(blobUrl);
      setSelectedFile(file);
    }
  };

  // Remove image
  const handleRemoveImage = () => {
    if (imagePreview && imagePreview.startsWith('blob:')) {
      URL.revokeObjectURL(imagePreview);
    }
    setImagePreview(null);
    setSelectedFile(null);
  };

  // Handle form submission
  const onSubmit = async (data: MenuItemFormValues) => {
    try {
      let result;

      if (mode === 'create') {
        // Create menu item first
        result = await createMenuItem({
          merchantId: shopId,
          branchId,
          itemData: {
            name: data.name,
            description: data.description,
            price: data.price,
            categoryId: data.categoryId,
            preparationTime: data.preparationTime,
            isVegetarian: data.isVegetarian,
            isVegan: data.isVegan,
            isGlutenFree: data.isGlutenFree,
            isSpicy: data.isSpicy,
            spiceLevel: data.spiceLevel,
            ingredients: data.ingredients,
            allergens: data.allergens,
            tags: data.tags,
          }
        }).unwrap();

        // If there's a selected file, upload it
        if (selectedFile) {
          try {
            await uploadMenuItemImage({
              merchantId: shopId,
              branchId,
              itemId: result.id,
              file: selectedFile
            }).unwrap();
          } catch (uploadError) {
            console.error('Failed to upload image:', uploadError);
            toast.error('Menu item created but image upload failed');
          }
        }

        toast.success('Menu item created successfully');
        router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu`);
      } else {
        // Update existing menu item
        if (!initialData) {
          throw new Error('Initial data is required for edit mode');
        }

        // Handle image upload if new file selected
        let uploadedImageUrl = imagePreview;
        if (selectedFile) {
          try {
            const imageResult = await uploadMenuItemImage({
              merchantId: shopId,
              branchId,
              itemId: initialData.id,
              file: selectedFile
            }).unwrap();
            uploadedImageUrl = imageResult.imageUrl;
          } catch (uploadError) {
            console.error('Failed to upload image:', uploadError);
            toast.error('Failed to upload image');
            return;
          }
        }

        await updateMenuItem({
          merchantId: shopId,
          branchId,
          itemData: {
            id: initialData.id,
            name: data.name,
            description: data.description,
            price: data.price,
            categoryId: data.categoryId,
            preparationTime: data.preparationTime,
            isAvailable: data.isAvailable,
            isVegetarian: data.isVegetarian,
            isVegan: data.isVegan,
            isGlutenFree: data.isGlutenFree,
            isSpicy: data.isSpicy,
            spiceLevel: data.spiceLevel,
            ingredients: data.ingredients,
            allergens: data.allergens,
            tags: data.tags,
            image: uploadedImageUrl || initialData.primaryImage,
          }
        }).unwrap();

        toast.success('Menu item updated successfully');

        // Wait for toast then redirect
        setTimeout(() => {
          router.push(`/app/restaurant/${slugShop}/${slugBranch}/menu/${initialData.slug}`);
        }, 1000);
      }

      // Clean up blob URL
      if (imagePreview && imagePreview.startsWith('blob:')) {
        URL.revokeObjectURL(imagePreview);
      }

      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error(`Failed to ${mode} menu item:`, error);
      toast.error(`Failed to ${mode} menu item`);
    }
  };

  const backUrl = mode === 'edit' && initialData
    ? `/app/restaurant/${slugShop}/${slugBranch}/menu/${initialData.slug}`
    : `/app/restaurant/${slugShop}/${slugBranch}/menu`;

  return (
    <div className="font-be-vietnam">
      {/* Back Button */}
      <div className="flex items-center mb-6 p-4">
        <Link href={backUrl}>
          <Button variant="outline" className="border-border text-foreground">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to {mode === 'edit' ? 'Menu Item' : 'Menu'}
          </Button>
        </Link>
      </div>

      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground tracking-tight text-[32px] font-bold leading-tight">
            {mode === 'create' ? 'Add New Menu Item' : 'Edit Menu Item'}
          </h1>
          <p className="text-muted-foreground text-sm font-normal leading-normal">
            {mode === 'create'
              ? 'Create a new menu item for your restaurant'
              : `Update the details for "${initialData?.name}"`
            }
          </p>
        </div>
      </div>

      {/* Form */}
      <Card className="max-w-2xl mx-auto m-4">
        <CardContent className="p-6">
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-foreground text-sm font-medium leading-normal">Item Name *</label>
                <Input
                  {...register('name')}
                  placeholder="Enter item name"
                  className="border-border"
                />
                {errors.name && <p className="text-destructive text-sm">{errors.name.message}</p>}
              </div>

              <div className="space-y-2">
                <label className="text-foreground text-sm font-medium leading-normal">Category *</label>
                <Select
                  value={watch('categoryId') || ''}
                  onValueChange={(value) => setValue('categoryId', value)}
                  disabled={isCreatingCategory}
                >
                  <SelectTrigger className="border-border">
                    <SelectValue placeholder={
                      isCreatingCategory
                        ? "Creating default categories..."
                        : categories.length === 0
                          ? "No categories available"
                          : "Select category"
                    } />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {isCreatingCategory && (
                  <p className="text-muted-foreground text-sm">Creating default categories for your menu...</p>
                )}
                {!isCreatingCategory && categories.length === 0 && categoriesResponse && (
                  <div className="flex items-center gap-2">
                    <p className="text-muted-foreground text-sm">No categories found.</p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={createDefaultCategories}
                      disabled={isCreatingCategory}
                    >
                      Create Default Categories
                    </Button>
                  </div>
                )}
                {errors.categoryId && <p className="text-destructive text-sm">{errors.categoryId.message}</p>}
              </div>

              <div className="space-y-2">
                <label className="text-foreground text-sm font-medium leading-normal">Price *</label>
                <Input
                  {...register('price')}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="border-border"
                />
                {errors.price && <p className="text-destructive text-sm">{errors.price.message}</p>}
              </div>

              <div className="space-y-2">
                <label className="text-foreground text-sm font-medium leading-normal">Cost</label>
                <Input
                  {...register('cost')}
                  type="number"
                  step="0.01"
                  min="0"
                  placeholder="0.00"
                  className="border-border"
                />
                {errors.cost && <p className="text-destructive text-sm">{errors.cost.message}</p>}
              </div>

              <div className="space-y-2">
                <label className="text-foreground text-sm font-medium leading-normal">Preparation Time (minutes)</label>
                <Input
                  {...register('preparationTime')}
                  type="number"
                  min="0"
                  placeholder="15"
                  className="border-border"
                />
                {errors.preparationTime && <p className="text-destructive text-sm">{errors.preparationTime.message}</p>}
              </div>

              <div className="space-y-2">
                <label className="text-foreground text-sm font-medium leading-normal">Availability</label>
                <Select
                  value={watch('isAvailable') ? 'true' : 'false'}
                  onValueChange={(value) => setValue('isAvailable', value === 'true')}
                >
                  <SelectTrigger className="border-border">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="true">Available</SelectItem>
                    <SelectItem value="false">Not Available</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-foreground text-sm font-medium leading-normal">Description</label>
              <Textarea
                {...register('description')}
                placeholder="Enter item description"
                className="border-border min-h-24"
              />
              {errors.description && <p className="text-destructive text-sm">{errors.description.message}</p>}
            </div>

            {/* Dietary Information */}
            <div className="space-y-4">
              <h3 className="text-foreground text-lg font-semibold">Dietary Information</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isVegetarian"
                    checked={watch('isVegetarian')}
                    onCheckedChange={(checked) => setValue('isVegetarian', !!checked)}
                  />
                  <label htmlFor="isVegetarian" className="text-sm font-medium">Vegetarian</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isVegan"
                    checked={watch('isVegan')}
                    onCheckedChange={(checked) => setValue('isVegan', !!checked)}
                  />
                  <label htmlFor="isVegan" className="text-sm font-medium">Vegan</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isGlutenFree"
                    checked={watch('isGlutenFree')}
                    onCheckedChange={(checked) => setValue('isGlutenFree', !!checked)}
                  />
                  <label htmlFor="isGlutenFree" className="text-sm font-medium">Gluten Free</label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isSpicy"
                    checked={watch('isSpicy')}
                    onCheckedChange={(checked) => setValue('isSpicy', !!checked)}
                  />
                  <label htmlFor="isSpicy" className="text-sm font-medium">Spicy</label>
                </div>
              </div>

              {/* Spice Level - only show if spicy is checked */}
              {isSpicy && (
                <div className="space-y-2">
                  <label className="text-foreground text-sm font-medium leading-normal">Spice Level</label>
                  <Select
                    value={(watch('spiceLevel') || 0).toString()}
                    onValueChange={(value) => setValue('spiceLevel', parseInt(value))}
                  >
                    <SelectTrigger className="border-border">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0">Not Spicy</SelectItem>
                      <SelectItem value="1">Mild</SelectItem>
                      <SelectItem value="2">Medium</SelectItem>
                      <SelectItem value="3">Hot</SelectItem>
                      <SelectItem value="4">Very Hot</SelectItem>
                      <SelectItem value="5">Extremely Hot</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
            </div>

            {/* Ingredients */}
            <div className="space-y-4">
              <h3 className="text-foreground text-lg font-semibold">Ingredients</h3>
              <div className="flex gap-2">
                <Input
                  value={newIngredient}
                  onChange={(e) => setNewIngredient(e.target.value)}
                  placeholder="Add ingredient"
                  className="border-border"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addIngredient())}
                />
                <Button type="button" onClick={addIngredient} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {(watch('ingredients') || []).map((ingredient, index) => (
                  <Badge key={index} variant="secondary" className="flex items-center gap-1">
                    {ingredient}
                    <button
                      type="button"
                      onClick={() => removeIngredient(index)}
                      className="ml-1 hover:text-destructive"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Allergens */}
            <div className="space-y-4">
              <h3 className="text-foreground text-lg font-semibold">Allergens</h3>
              <div className="flex gap-2">
                <Input
                  value={newAllergen}
                  onChange={(e) => setNewAllergen(e.target.value)}
                  placeholder="Add allergen"
                  className="border-border"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addAllergen())}
                />
                <Button type="button" onClick={addAllergen} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {(watch('allergens') || []).map((allergen, index) => (
                  <Badge key={index} variant="destructive" className="flex items-center gap-1">
                    {allergen}
                    <button
                      type="button"
                      onClick={() => removeAllergen(index)}
                      className="ml-1 hover:text-destructive-foreground"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4">
              <h3 className="text-foreground text-lg font-semibold">Tags</h3>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add tag"
                  className="border-border"
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {(watch('tags') || []).map((tag, index) => (
                  <Badge key={index} variant="default" className="flex items-center gap-1">
                    {tag}
                    <button
                      type="button"
                      onClick={() => removeTag(index)}
                      className="ml-1 hover:text-primary-foreground"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Image Upload */}
            <div className="space-y-2">
              <label className="text-foreground text-sm font-medium leading-normal">Image</label>
              <div className="border-2 border-dashed border-border rounded-lg p-6">
                {imagePreview ? (
                  <div className="flex flex-col items-center gap-4">
                    <Image
                      src={imagePreview}
                      alt="Preview"
                      width={200}
                      height={200}
                      className="max-w-[200px] max-h-[200px] rounded-lg object-cover"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleRemoveImage}
                      className="flex items-center gap-2"
                    >
                      <X className="h-4 w-4" />
                      Remove Image
                    </Button>
                  </div>
                ) : (
                  <div className="flex flex-col items-center gap-4">
                    <div className="text-center">
                      <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                      <p className="text-foreground text-lg font-medium">Upload Image</p>
                      <p className="text-muted-foreground text-sm">Click or drag an image here to upload</p>
                    </div>
                    <input
                      type="file"
                      id="image-upload"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      asChild
                    >
                      <label htmlFor="image-upload" className="cursor-pointer">
                        <Upload className="h-4 w-4 mr-2" />
                        Choose Image
                      </label>
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-4">
              <Button
                type="submit"
                disabled={isLoading}
                className="flex items-center gap-2"
              >
                <Save className="h-4 w-4" />
                {isLoading
                  ? (mode === 'create' ? 'Creating...' : 'Updating...')
                  : (mode === 'create' ? 'Create Item' : 'Update Item')
                }
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
