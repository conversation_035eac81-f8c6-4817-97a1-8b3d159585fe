'use client';

import React from 'react';
import { Grid3X3, Table } from 'lucide-react';

interface MenuViewToggleProps {
  viewMode: 'table' | 'grid';
  onViewModeChange: (mode: 'table' | 'grid') => void;
}

export function MenuViewToggle({ viewMode, onViewModeChange }: MenuViewToggleProps) {
  return (
    <div className="flex items-center bg-muted rounded-lg p-1 border border-border">
      <button
        onClick={() => onViewModeChange('table')}
        className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
          viewMode === 'table'
            ? 'bg-background text-foreground shadow-sm border border-border'
            : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
        }`}
      >
        <Table size={16} />
        <span>Table</span>
      </button>
      <button
        onClick={() => onViewModeChange('grid')}
        className={`flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
          viewMode === 'grid'
            ? 'bg-background text-foreground shadow-sm border border-border'
            : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
        }`}
      >
        <Grid3X3 size={16} />
        <span>Grid</span>
      </button>
    </div>
  );
}
