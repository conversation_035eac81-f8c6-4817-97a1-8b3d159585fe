'use client';

import React from 'react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { MenuItemForm } from '../_components';

interface AddMenuItemPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function AddMenuItemPage({ params }: AddMenuItemPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend by slug
  const { data: shop, isLoading: isLoadingShop, error: shopError } = useGetShopBySlugQuery(slugShop);
  const { data: branch, isLoading: isLoadingBranch, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  });

  // Show loading state
  if (isLoadingShop || isLoadingBranch) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  // Show error state if shop or branch not found
  if (!shop || !branch || shopError || branchError) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <MenuItemForm
      mode="create"
      slugShop={slugShop}
      slugBranch={slugBranch}
      shopId={shop.id}
      branchId={branch.id}
    />
  );
}
