'use client';

import React, { useState, useMemo } from 'react';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetMenuItemsBySlugQuery, MenuFilters } from '@/lib/redux/api/endpoints/restaurant/menuApi';
import {
  <PERSON>uHeader,
  MenuSearch,
  MenuViewToggle,
  MenuTableView,
  MenuGridView,
  MenuPagination,
  MenuCategoryFilters
} from './_components';

interface MenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

// Type for API response data (handles both snake_case and camelCase)
interface ApiMenuItem {
  id: string;
  name: string;
  slug: string;
  description: string;
  category?: {
    name: string;
  };
  categoryName?: string;
  price: number;
  primary_image?: string;
  primaryImage?: string;
  image?: string;
  images?: string[];
  is_available?: boolean;
  isAvailable?: boolean;
}

export default function MenuPage({ params }: MenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('grid');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState<string | undefined>();

  // Create filters object for backend
  const filters = useMemo((): MenuFilters => ({
    search: searchTerm || undefined,
    page: currentPage,
    limit: 20
  }), [searchTerm, currentPage]);

  // Get menu items from backend with filters using slugs
  const {
    data: menuItemsResponse,
    isLoading: isLoadingMenuItems,
    isError: isMenuItemsError
  } = useGetMenuItemsBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    filters
  });

  const isLoading = isLoadingMenuItems;

  // Extract menu items and pagination info from backend response (before early returns)
  const menuItems = menuItemsResponse?.data || [];
  const totalItems = menuItemsResponse?.pagination?.totalItems || 0;
  const totalPages = menuItemsResponse?.pagination?.totalPages || 1;

  // Transform API data to match component interface
  const allItems = menuItems.map((item: ApiMenuItem) => {
    const imageUrl = item.primary_image || item.primaryImage || item.image || (item.images && item.images[0]) || '';

    // Debug logging to help troubleshoot image issues
    if (process.env.NODE_ENV === 'development') {
      console.log('Menu item image mapping:', {
        itemName: item.name,
        primary_image: item.primary_image,
        primaryImage: item.primaryImage,
        image: item.image,
        images: item.images,
        finalImageUrl: imageUrl
      });
    }

    return {
      id: item.id,
      name: item.name,
      slug: item.slug,
      description: item.description,
      category: item.category?.name || item.categoryName || 'Uncategorized',
      price: item.price,
      image: imageUrl,
      available: item.is_available !== undefined ? item.is_available : (item.isAvailable ?? true)
    };
  });

  // Extract unique categories for filtering
  const categories = useMemo(() => {
    const uniqueCategories = Array.from(new Set(allItems.map(item => item.category)));
    return uniqueCategories.filter(category => category && category !== 'Uncategorized');
  }, [allItems]);

  // Apply frontend category filtering
  const filteredItems = useMemo(() => {
    if (!selectedCategory) {
      return allItems;
    }
    return allItems.filter(item => item.category === selectedCategory);
  }, [allItems, selectedCategory]);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (isMenuItemsError) {
    return (
      <div className="font-be-vietnam">
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Error Loading Menu</h1>
          <p className="text-muted-foreground text-sm">There was an error loading the menu items. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex size-full min-h-screen flex-col bg-background group/design-root overflow-x-hidden font-be-vietnam">
      <div className="layout-container flex h-full grow flex-col">
        <div className="gap-1 px-6 flex flex-1 justify-center py-6">

          {/* Main Content */}
          <div className="layout-content-container flex flex-col max-w-[1400px] flex-1 space-y-6">
            <MenuHeader slugShop={slugShop} slugBranch={slugBranch} />

            <div className="flex items-center justify-between gap-4">
              <MenuSearch
                searchTerm={searchTerm}
                onSearchChange={setSearchTerm}
              />
              <MenuViewToggle
                viewMode={viewMode}
                onViewModeChange={setViewMode}
              />
            </div>

            {/* Category Filters */}
            <MenuCategoryFilters
              categories={categories}
              selectedCategory={selectedCategory}
              onCategoryChange={(category) => {
                setSelectedCategory(category);
                setCurrentPage(1); // Reset to first page when category changes
              }}
            />

            {/* Content based on view mode */}
            {viewMode === 'table' ? (
              <MenuTableView
                items={filteredItems}
                slugShop={slugShop}
                slugBranch={slugBranch}
                searchTerm={searchTerm}
              />
            ) : (
              <MenuGridView
                items={filteredItems}
                slugShop={slugShop}
                slugBranch={slugBranch}
                searchTerm={searchTerm}
              />
            )}

            {/* Pagination - only show if there are items and no category filter is applied */}
            {filteredItems.length > 0 && !selectedCategory && (
              <MenuPagination
                currentPage={currentPage}
                totalPages={totalPages}
                totalItems={totalItems}
                itemsPerPage={20}
                onPageChange={(page) => {
                  setCurrentPage(page);
                  // Scroll to top when page changes
                  window.scrollTo({ top: 0, behavior: 'smooth' });
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
