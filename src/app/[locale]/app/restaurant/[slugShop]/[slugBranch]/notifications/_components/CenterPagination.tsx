import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { generatePageNumbers } from '@/lib/utils/pagination';
import { cn } from '@/lib/utils';

interface CenterPaginationProps {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  onPageChange: (page: number) => void;
  showInfo?: boolean;
  maxVisible?: number;
  className?: string;
}

export function CenterPagination({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  showInfo = true,
  maxVisible = 5,
  className
}: CenterPaginationProps) {
  if (totalPages <= 1) return null;

  const startItem = (currentPage - 1) * itemsPerPage + 1;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);
  const pageNumbers = generatePageNumbers(currentPage, totalPages, maxVisible);

  const handlePageClick = (page: number) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      onPageChange(page);
    }
  };

  return (
    <div className={cn("flex flex-col items-center space-y-4", className)}>
      {/* Pagination Info */}
      {showInfo && (
        <div className="text-sm text-muted-foreground">
          {totalItems === 0 ? (
            'No results found'
          ) : totalItems === 1 ? (
            '1 result'
          ) : (
            `Showing ${startItem}-${endItem} of ${totalItems} results`
          )}
        </div>
      )}

      {/* Pagination Controls */}
      <Pagination>
        <PaginationContent>
          {/* Previous Button */}
          <PaginationItem>
            <PaginationPrevious
              onClick={() => handlePageClick(currentPage - 1)}
              className={cn(
                currentPage === 1 && 'pointer-events-none opacity-50 cursor-not-allowed'
              )}
              aria-disabled={currentPage === 1}
            />
          </PaginationItem>

          {/* Page Numbers */}
          {pageNumbers.map((pageNumber, index) => (
            <PaginationItem key={index}>
              {pageNumber === 'ellipsis' ? (
                <PaginationEllipsis />
              ) : (
                <PaginationLink
                  onClick={() => handlePageClick(pageNumber as number)}
                  isActive={currentPage === pageNumber}
                  className="cursor-pointer"
                  aria-label={`Go to page ${pageNumber}`}
                >
                  {pageNumber}
                </PaginationLink>
              )}
            </PaginationItem>
          ))}

          {/* Next Button */}
          <PaginationItem>
            <PaginationNext
              onClick={() => handlePageClick(currentPage + 1)}
              className={cn(
                currentPage === totalPages && 'pointer-events-none opacity-50 cursor-not-allowed'
              )}
              aria-disabled={currentPage === totalPages}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}
