'use client';

import React, { useState, use } from 'react';
import { Link } from '@/i18n/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { ArrowLeft, Bell, TrendingUp, AlertTriangle, Clock, BarChart3, Settings } from 'lucide-react';
import { toast } from 'sonner';
import { useGetBranchWithShopQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { AppLoading } from '@/components/ui/app-loading';
import { useNotifications } from '@/hooks/useNotifications';
import { NotificationItem } from './_components/NotificationItem';
import { NotificationFilters } from './_components/NotificationFilters';
import { NotificationActions } from './_components/NotificationActions';
import { CenterPagination } from './_components/CenterPagination';
import { BranchNotificationDashboard } from './_components/BranchNotificationDashboard';
import { RealTimeIndicator } from './_components/RealTimeIndicator';
import { BranchNotificationSettings } from './_components/BranchNotificationSettings';

interface NotificationPreferences {
  enabled: boolean;
  sound: boolean;
  desktop: boolean;
  email: boolean;
  sms: boolean;
  types: Record<string, boolean>;
  priorities: Record<string, boolean>;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

interface NotificationsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function NotificationsPage({ params }: NotificationsPageProps) {
  const { slugShop, slugBranch } = use(params);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedNotificationIds, setSelectedNotificationIds] = useState<string[]>([]);
  const [currentView, setCurrentView] = useState<'list' | 'dashboard' | 'settings'>('list');
  const [showSelection, setShowSelection] = useState(false);

  // Get shop and branch data using real API
  const {
    data: branchWithShop,
    isLoading: isLoadingBranch,
    isError: isBranchError,
  } = useGetBranchWithShopQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  });

  // Get shop and branch data
  const branch = branchWithShop;
  const shop = branchWithShop?.shop;

  // Use the notifications hook with backend API
  const {
    notifications,
    totalCount,
    currentPage,
    totalPages,
    filters,
    stats,
    pagination,
    isLoading,
    handleSearch,
    handleTypeFilter,
    handlePriorityFilter,
    handleReadStatusFilter,
    handleDateRangeFilter,
    handlePageChange,
    handleMarkAsRead,
    handleMarkAsUnread,
    handleDeleteNotification,
    handleMarkAllAsRead,
    handleClearAllNotifications,
    handleBulkMarkAsRead,
    formatTimestamp,
    getPriorityColor,
    getTypeIcon,
    refetch,
    resetFilters,
  } = useNotifications({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    initialFilters: {
      page: 1,
      limit: 20,
      sort_by: 'timestamp',
      sort_order: 'desc'
    }
  });

  // Action handlers with toast notifications
  const onMarkAsRead = async (id: string) => {
    try {
      await handleMarkAsRead(id);
      toast.success('Notification marked as read');
    } catch {
      toast.error('Failed to mark notification as read');
    }
  };

  const onMarkAsUnread = async (id: string) => {
    try {
      await handleMarkAsUnread(id);
      toast.success('Notification marked as unread');
    } catch {
      toast.error('Failed to mark notification as unread');
    }
  };

  const onDeleteNotification = async (id: string) => {
    try {
      await handleDeleteNotification(id);
      toast.success('Notification deleted');
    } catch {
      toast.error('Failed to delete notification');
    }
  };

  const onMarkAllAsRead = async () => {
    try {
      await handleMarkAllAsRead();
      toast.success('All notifications marked as read');
    } catch {
      toast.error('Failed to mark all notifications as read');
    }
  };

  const onClearAllNotifications = async () => {
    try {
      await handleClearAllNotifications();
      toast.success('All notifications cleared');
    } catch {
      toast.error('Failed to clear all notifications');
    }
  };

  const onBulkMarkAsRead = async (ids: string[]) => {
    try {
      await handleBulkMarkAsRead(ids);
      toast.success(`${ids.length} notifications marked as read`);
      setSelectedNotificationIds([]);
    } catch {
      toast.error('Failed to mark notifications as read');
    }
  };

  const handleRefresh = () => {
    refetch();
    toast.success('Notifications refreshed');
  };

  const handleSaveNotificationSettings = async (preferences: NotificationPreferences) => {
    // This would integrate with your notification settings API
    console.log('Saving notification preferences:', preferences);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    toast.success('Notification settings saved successfully');
  };

  // Selection handlers
  const handleSelectNotification = (id: string, selected: boolean) => {
    setSelectedNotificationIds(prev =>
      selected
        ? [...prev, id]
        : prev.filter(notificationId => notificationId !== id)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedNotificationIds(selected ? notifications.map(n => n.id) : []);
  };

  const toggleSelectionMode = () => {
    setShowSelection(!showSelection);
    if (showSelection) {
      setSelectedNotificationIds([]);
    }
  };

  if (isLoadingBranch || isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (isBranchError || !branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-muted-foreground text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
        <RealTimeIndicator />
      </div>

      {/* Page Title */}
      <div>
        <h1 className="text-foreground text-[32px] font-bold leading-tight">Notifications</h1>
        <p className="text-muted-foreground text-sm">
          View and manage notifications for {shop?.name} - {branch?.name}
        </p>
      </div>

      {/* Tabs Navigation */}
      <Tabs value={currentView} onValueChange={(value) => setCurrentView(value as 'list' | 'dashboard' | 'settings')} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Settings
          </TabsTrigger>
        </TabsList>

        {/* Notifications List Tab */}
        <TabsContent value="list" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <Bell className="h-5 w-5 text-muted-foreground mr-2" />
                  <div>
                    <p className="text-sm text-muted-foreground">Total</p>
                    <p className="text-2xl font-bold text-foreground">{stats.totalNotifications}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <TrendingUp className="h-5 w-5 text-orange-600 dark:text-orange-400 mr-2" />
                  <div>
                    <p className="text-sm text-muted-foreground">Unread</p>
                    <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">{stats.unreadNotifications}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                  <div>
                    <p className="text-sm text-muted-foreground">Read</p>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">{stats.readNotifications}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mr-2" />
                  <div>
                    <p className="text-sm text-muted-foreground">Urgent</p>
                    <p className="text-2xl font-bold text-red-600 dark:text-red-400">{stats.urgentNotifications}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

      {/* Filters */}
      <NotificationFilters
        filters={filters}
        searchTerm={searchTerm}
        onSearchChange={setSearchTerm}
        onSearch={handleSearch}
        onTypeFilter={handleTypeFilter}
        onPriorityFilter={handlePriorityFilter}
        onReadStatusFilter={handleReadStatusFilter}
        onDateRangeFilter={handleDateRangeFilter}
        onResetFilters={resetFilters}
        stats={stats}
      />

      {/* Actions */}
      <NotificationActions
        selectedCount={selectedNotificationIds.length}
        totalCount={totalCount}
        unreadCount={stats.unreadNotifications}
        onMarkAllAsRead={onMarkAllAsRead}
        onClearAll={onClearAllNotifications}
        onBulkMarkAsRead={onBulkMarkAsRead}
        onRefresh={handleRefresh}
        isLoading={isLoading}
        selectedNotificationIds={selectedNotificationIds}
        showSelection={showSelection}
        onToggleSelection={toggleSelectionMode}
        onSelectAll={handleSelectAll}
        allSelected={notifications.length > 0 && selectedNotificationIds.length === notifications.length}
      />

      {/* Notifications List */}
      <Card>
        <CardContent className="p-0">
          {notifications.length === 0 ? (
            <div className="text-center py-12">
              <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">No notifications</h3>
              <p className="text-muted-foreground">You&apos;re all caught up! No notifications to display.</p>
            </div>
          ) : (
            <div className="divide-y divide-border">
              {notifications.map((notification) => (
                <div key={notification.id} className="p-4">
                  <NotificationItem
                    notification={notification}
                    shopSlug={slugShop}
                    branchSlug={slugBranch}
                    onMarkAsRead={onMarkAsRead}
                    onMarkAsUnread={onMarkAsUnread}
                    onDelete={onDeleteNotification}
                    formatTimestamp={formatTimestamp}
                    getPriorityColor={getPriorityColor}
                    getTypeIcon={getTypeIcon}
                    isSelected={selectedNotificationIds.includes(notification.id)}
                    onSelect={handleSelectNotification}
                    showSelection={showSelection}
                  />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

          {/* Pagination */}
          {pagination && totalPages > 1 && (
            <CenterPagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalItems={totalCount}
              itemsPerPage={filters.limit}
              onPageChange={handlePageChange}
              showInfo={true}
              maxVisible={5}
            />
          )}
        </TabsContent>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          <BranchNotificationDashboard
            shopName={shop?.name || ''}
            branchName={branch?.name || ''}
            stats={stats}
            onRefresh={handleRefresh}
            isLoading={isLoading}
          />
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <BranchNotificationSettings
            branchName={branch?.name || ''}
            onSave={handleSaveNotificationSettings}
            isLoading={false}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}