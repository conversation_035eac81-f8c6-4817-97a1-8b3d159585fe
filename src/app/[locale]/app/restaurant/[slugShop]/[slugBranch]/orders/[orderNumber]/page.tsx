'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { format } from 'date-fns';
import { useGetOrderByNumberQuery, useUpdateOrderStatusByNumberMutation, Order } from '@/lib/redux/api/endpoints/restaurant/ordersApi';
import { AppLoading } from '@/components/ui/app-loading';

// Define order status types
type OrderStatus = 'pending' | 'confirmed' | 'preparing' | 'ready' | 'served' | 'completed' | 'cancelled';

// Define status color mapping
const statusColorMap: Record<OrderStatus, string> = {
  pending: '#f59e0b', // Amber
  confirmed: '#06b6d4', // Cyan
  preparing: '#3b82f6', // Blue
  ready: '#8b5cf6', // Purple
  served: '#10b981', // Emerald
  completed: '#07880e', // Green
  cancelled: '#ef4444', // Red
};

export default function OrderDetailPage() {
  const params = useParams();
  const orderNumber = params.orderNumber as string;
  const slugShop = params.slugShop as string;
  const slugBranch = params.slugBranch as string;

  // Real API call using slugs and order number
  const { data: order, isLoading, isError } = useGetOrderByNumberQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    orderNumber
  });

  // Mutations for updating order status
  const [updateOrderStatus, { isLoading: isUpdating }] = useUpdateOrderStatusByNumberMutation();

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMMM dd, yyyy, h:mm a');
  };

  // Handle marking order as completed
  const handleMarkAsCompleted = async () => {
    try {
      await updateOrderStatus({
        shopSlug: slugShop,
        branchSlug: slugBranch,
        orderNumber,
        status: 'completed'
      }).unwrap();
    } catch (error) {
      console.error('Failed to update order status:', error);
    }
  };

  // Handle cancelling order
  const handleCancelOrder = async () => {
    try {
      await updateOrderStatus({
        shopSlug: slugShop,
        branchSlug: slugBranch,
        orderNumber,
        status: 'cancelled'
      }).unwrap();
    } catch (error) {
      console.error('Failed to cancel order:', error);
    }
  };

  if (isLoading) {
    return <AppLoading />;
  }

  if (isError || !order) {
    return <div className="p-4">Error loading order details. Please try again.</div>;
  }

  return (
    <>
      <div className="flex flex-wrap gap-2 p-4">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/orders`} className="text-[#887663] text-base font-medium leading-normal">
          Orders
        </Link>
        <span className="text-[#887663] text-base font-medium leading-normal">/</span>
        <span className="text-[#181511] text-base font-medium leading-normal">Order {order.order_number}</span>
      </div>

      <div className="flex flex-wrap justify-between gap-3 p-4">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-[#181511] tracking-light text-[32px] font-bold leading-tight">Order {order.order_number}</p>
          <p className="text-[#887663] text-sm font-normal leading-normal">
            Placed on {formatDate(order.created_at)}
          </p>
        </div>
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Order Summary</h3>
      <div className="px-4 py-3 @container">
        <div className="flex overflow-hidden rounded-xl border border-[#e5e1dc] bg-white">
          <table className="flex-1">
            <thead>
              <tr className="bg-white">
                <th className="table-order-detail-column-120 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Item</th>
                <th className="table-order-detail-column-240 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                  Quantity
                </th>
                <th className="table-order-detail-column-360 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">
                  Special Requests
                </th>
                <th className="table-order-detail-column-480 px-4 py-3 text-left text-[#181511] w-[400px] text-sm font-medium leading-normal">Price</th>
              </tr>
            </thead>
            <tbody>
              {order.items.map((item) => (
                <tr key={item.id} className="border-t border-t-[#e5e1dc]">
                  <td className="table-order-detail-column-120 h-[72px] px-4 py-2 w-[400px] text-[#181511] text-sm font-normal leading-normal">
                    {item.name || item.menu_item?.name || 'Unknown Item'}
                  </td>
                  <td className="table-order-detail-column-240 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    {item.quantity}
                  </td>
                  <td className="table-order-detail-column-360 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    {item.specialInstructions || item.notes || '-'}
                  </td>
                  <td className="table-order-detail-column-480 h-[72px] px-4 py-2 w-[400px] text-[#887663] text-sm font-normal leading-normal">
                    ${((item.price || item.unit_price || item.menu_item?.price || 0) * item.quantity).toFixed(2)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <style>
          {`
            @container(max-width:120px){.table-order-detail-column-120{display: none;}}
            @container(max-width:240px){.table-order-detail-column-240{display: none;}}
            @container(max-width:360px){.table-order-detail-column-360{display: none;}}
            @container(max-width:480px){.table-order-detail-column-480{display: none;}}
          `}
        </style>
      </div>

      <div className="p-4">
        <div className="flex justify-between gap-x-6 py-2">
          <p className="text-[#887663] text-sm font-normal leading-normal">Subtotal</p>
          <p className="text-[#181511] text-sm font-normal leading-normal text-right">${order.subtotal.toFixed(2)}</p>
        </div>
        <div className="flex justify-between gap-x-6 py-2">
          <p className="text-[#887663] text-sm font-normal leading-normal">Tax</p>
          <p className="text-[#181511] text-sm font-normal leading-normal text-right">${order.tax_amount.toFixed(2)}</p>
        </div>
        <div className="flex justify-between gap-x-6 py-2">
          <p className="text-[#887663] text-sm font-normal leading-normal">Total</p>
          <p className="text-[#181511] text-sm font-normal leading-normal text-right">${order.total_amount.toFixed(2)}</p>
        </div>
      </div>

      <h3 className="text-[#181511] text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Customer Details</h3>
      <div className="p-4 grid grid-cols-[20%_1fr] gap-x-6">
        <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e5e1dc] py-5">
          <p className="text-[#887663] text-sm font-normal leading-normal">Name</p>
          <p className="text-[#181511] text-sm font-normal leading-normal">{order.customer_name}</p>
        </div>
        <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e5e1dc] py-5">
          <p className="text-[#887663] text-sm font-normal leading-normal">Phone</p>
          <p className="text-[#181511] text-sm font-normal leading-normal">{order.customer_phone}</p>
        </div>
        {order.table_id && (
          <div className="col-span-2 grid grid-cols-subgrid border-t border-t-[#e5e1dc] py-5">
            <p className="text-[#887663] text-sm font-normal leading-normal">Table</p>
            <p className="text-[#181511] text-sm font-normal leading-normal">Table {order.table_id}</p>
          </div>
        )}
      </div>

      <h3 className="text-foreground text-lg font-bold leading-tight tracking-[-0.015em] px-4 pb-2 pt-4">Order Status</h3>
      <div className="flex items-center gap-4 bg-card px-4 min-h-14 justify-between">
        <p className="text-foreground text-base font-normal leading-normal flex-1 truncate">Status</p>
        <div className="shrink-0">
          <div className="flex size-7 items-center justify-center">
            <div className="size-3 rounded-full" style={{ backgroundColor: statusColorMap[order.status] }}></div>
          </div>
        </div>
      </div>
      <div className="flex items-center gap-4 bg-white px-4 min-h-14 justify-between">
        <p className="text-[#181511] text-base font-normal leading-normal flex-1 truncate">Estimated Time</p>
        <div className="shrink-0">
          <p className="text-[#181511] text-base font-normal leading-normal">{order.estimated_time || 'N/A'} minutes</p>
        </div>
      </div>

      <div className="flex justify-stretch">
        <div className="flex flex-1 gap-3 flex-wrap px-4 py-3 justify-end">
          {/* Only show Cancel button if order is not completed or cancelled */}
          {order.status !== 'completed' && order.status !== 'cancelled' && (
            <button
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#f4f2f0] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
              onClick={handleCancelOrder}
              disabled={isUpdating}
            >
              <span className="truncate">
                {isUpdating ? 'Cancelling...' : 'Cancel Order'}
              </span>
            </button>
          )}

          {/* Only show Mark as Completed button if order is not completed or cancelled */}
          {order.status !== 'completed' && order.status !== 'cancelled' && (
            <button
              className="flex min-w-[84px] max-w-[480px] cursor-pointer items-center justify-center overflow-hidden rounded-full h-10 px-4 bg-[#e58219] text-[#181511] text-sm font-bold leading-normal tracking-[0.015em]"
              onClick={handleMarkAsCompleted}
              disabled={isUpdating}
            >
              <span className="truncate">
                {isUpdating ? 'Updating...' : 'Mark as Completed'}
              </span>
            </button>
          )}

          {/* Show status message for completed or cancelled orders */}
          {(order.status === 'completed' || order.status === 'cancelled') && (
            <div className="flex items-center gap-2 px-4 py-2 bg-gray-100 rounded-full">
              <div className="size-3 rounded-full" style={{ backgroundColor: statusColorMap[order.status] }}></div>
              <span className="text-[#181511] text-sm font-medium capitalize">
                Order {order.status}
              </span>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
