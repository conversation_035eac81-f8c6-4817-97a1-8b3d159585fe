'use client';

import React from 'react';
import { format, parseISO } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarDays, Clock, Users } from 'lucide-react';

interface Reservation {
  id: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  status: string;
  tableId?: string;
  tableName?: string;
  specialRequests?: string;
}

interface ReservationCalendarProps {
  selectedDate: Date | undefined;
  onDateSelect: (date: Date | undefined) => void;
  reservations: Reservation[];
}

export function ReservationCalendar({
  selectedDate,
  onDateSelect,
  reservations
}: ReservationCalendarProps) {
  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 text-xs">Confirmed</Badge>;
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 text-xs">Pending</Badge>;
      case 'cancelled':
        return <Badge variant="secondary" className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300 text-xs">Cancelled</Badge>;
      case 'seated':
        return <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 text-xs">Seated</Badge>;
      case 'completed':
        return <Badge variant="secondary" className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300 text-xs">Completed</Badge>;
      case 'no-show':
        return <Badge variant="secondary" className="bg-muted text-muted-foreground border-border text-xs">No Show</Badge>;
      default:
        return <Badge variant="secondary" className="bg-muted text-muted-foreground border-border text-xs">{status}</Badge>;
    }
  };

  // Filter reservations for the selected date
  const selectedDateReservations = selectedDate
    ? reservations.filter(reservation =>
        reservation.date === format(selectedDate, 'yyyy-MM-dd')
      )
    : [];

  // Sort reservations by time
  const sortedReservations = selectedDateReservations.sort((a, b) =>
    a.time.localeCompare(b.time)
  );

  return (
    <Card className="bg-muted">
      <CardHeader className="pb-4">
        <CardTitle className="text-lg font-medium text-foreground flex items-center gap-2">
          <CalendarDays className="h-5 w-5" />
          Reservation Calendar
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Calendar Component */}
        <div className="bg-card rounded-lg p-3 border border-border">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={onDateSelect}
            className="w-full"
            classNames={{
              months: "flex w-full flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",
              month: "space-y-4 w-full flex flex-col",
              caption: "flex justify-center pt-1 relative items-center",
              caption_label: "text-sm font-medium text-foreground",
              nav: "space-x-1 flex items-center",
              nav_button: "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100 text-foreground",
              nav_button_previous: "absolute left-1",
              nav_button_next: "absolute right-1",
              table: "w-full border-collapse space-y-1",
              head_row: "flex",
              head_cell: "text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",
              row: "flex w-full mt-2",
              cell: "text-center text-sm p-0 relative [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",
              day: "h-9 w-9 p-0 font-normal aria-selected:opacity-100 text-foreground hover:bg-muted rounded-md",
              day_selected: "bg-accent text-accent-foreground hover:bg-accent/80 hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
              day_today: "bg-primary text-primary-foreground",
              day_outside: "text-muted-foreground opacity-50",
              day_disabled: "text-muted-foreground opacity-50",
              day_range_middle: "aria-selected:bg-accent aria-selected:text-accent-foreground",
              day_hidden: "invisible",
            }}
          />
        </div>

        {/* Selected Date Reservations */}
        <div>
          <h3 className="text-md font-medium mb-3 text-foreground flex items-center gap-2">
            <Clock className="h-4 w-4" />
            {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'Select a date'}
          </h3>

          <div className="space-y-2 max-h-64 overflow-y-auto">
            {sortedReservations.length > 0 ? (
              sortedReservations.map((reservation) => (
                <div
                  key={reservation.id}
                  className="bg-card p-3 rounded-md border border-border hover:bg-muted/50 transition-colors"
                >
                  <div className="flex justify-between items-start mb-2">
                    <div className="flex items-center gap-2">
                      <Clock className="h-3 w-3 text-muted-foreground" />
                      <span className="font-medium text-foreground">{reservation.time}</span>
                    </div>
                    {getStatusBadge(reservation.status)}
                  </div>

                  <div className="space-y-1">
                    <div className="font-medium text-foreground">{reservation.customerName}</div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {reservation.partySize} {reservation.partySize === 1 ? 'person' : 'people'}
                      </div>
                      {reservation.tableName && (
                        <div>Table: {reservation.tableName}</div>
                      )}
                    </div>
                    {reservation.specialRequests && (
                      <div className="text-xs text-muted-foreground mt-1 italic">
                        Note: {reservation.specialRequests}
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-muted-foreground text-center py-6 bg-card rounded-md border border-border">
                <CalendarDays className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p>No reservations for this date</p>
                <p className="text-xs mt-1">Select a different date to view reservations</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
