'use client';

import React from 'react';
import { format, parseISO } from 'date-fns';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { ReservationDeleteDialog } from '@/components/ui/delete-confirmation-dialog';
import { useState } from 'react';

interface Reservation {
  id: string;
  slug: string;
  customerName: string;
  customerEmail?: string;
  customerPhone?: string;
  date: string;
  time: string;
  partySize: number;
  status: string;
  tableId?: string;
  tableName?: string;
  specialRequests?: string;
}

interface ReservationTableProps {
  reservations: Reservation[];
  slugShop: string;
  slugBranch: string;
  onCancelReservation: (slug: string) => Promise<void>;
  isLoading?: boolean;
}

export function ReservationTable({
  reservations,
  slugShop,
  slugBranch,
  onCancelReservation,
  isLoading = false
}: ReservationTableProps) {
  const [cancellingSlug, setCancellingSlug] = useState<string | null>(null);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [selectedReservationSlug, setSelectedReservationSlug] = useState<string | null>(null);

  const getStatusBadge = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return <Badge className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300">Confirmed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300">Cancelled</Badge>;
      case 'seated':
        return <Badge className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300">Seated</Badge>;
      case 'completed':
        return <Badge className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300">Completed</Badge>;
      case 'no-show':
        return <Badge className="bg-muted text-muted-foreground border-border">No Show</Badge>;
      default:
        return <Badge className="bg-muted text-muted-foreground border-border">{status}</Badge>;
    }
  };

  const handleCancelClick = (reservationSlug: string) => {
    setSelectedReservationSlug(reservationSlug);
    setShowCancelModal(true);
  };

  const handleConfirmCancel = async () => {
    if (selectedReservationSlug) {
      setCancellingSlug(selectedReservationSlug);
      try {
        await onCancelReservation(selectedReservationSlug);
      } finally {
        setCancellingSlug(null);
        setShowCancelModal(false);
        setSelectedReservationSlug(null);
      }
    }
  };

  if (reservations.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground text-lg">No reservations found</p>
          <p className="text-muted-foreground text-sm mt-2">
            Reservations will appear here once they are created.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted hover:bg-muted">
                <TableHead className="text-foreground font-medium">Customer</TableHead>
                <TableHead className="text-foreground font-medium">Date & Time</TableHead>
                <TableHead className="text-foreground font-medium">Party Size</TableHead>
                <TableHead className="text-foreground font-medium">Table</TableHead>
                <TableHead className="text-foreground font-medium">Status</TableHead>
                <TableHead className="text-foreground font-medium">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reservations.map((reservation) => (
                <TableRow key={reservation.slug} className="hover:bg-muted/50">
                  <TableCell>
                    <div>
                      <div className="font-medium text-foreground">{reservation.customerName}</div>
                      <div className="text-sm text-muted-foreground">
                        {reservation.customerEmail || reservation.customerPhone}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="text-foreground">
                        {reservation.date ? format(parseISO(reservation.date), 'MMM d, yyyy') : 'No date'}
                      </div>
                      <div className="text-sm text-muted-foreground">{reservation.time || 'No time'}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-foreground">
                    {reservation.partySize} {reservation.partySize === 1 ? 'person' : 'people'}
                  </TableCell>
                  <TableCell className="text-foreground">
                    {reservation.tableName || reservation.tableId || 'Not assigned'}
                  </TableCell>
                  <TableCell>{getStatusBadge(reservation.status)}</TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Link href={
                        reservation.status === 'completed' || reservation.status === 'cancelled'
                          ? `/app/restaurant/${slugShop}/${slugBranch}/reservations/${reservation.slug}`
                          : `/app/restaurant/${slugShop}/${slugBranch}/reservations/${reservation.slug}/edit`
                      }>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-8 px-3"
                        >
                          {reservation.status === 'completed' || reservation.status === 'cancelled' ? 'View' : 'Edit'}
                        </Button>
                      </Link>
                      {reservation.status !== 'cancelled' &&
                       reservation.status !== 'completed' &&
                       reservation.status !== 'no-show' && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs h-8 px-3 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/20"
                          onClick={() => handleCancelClick(reservation.slug)}
                          disabled={cancellingSlug === reservation.slug || isLoading}
                        >
                          {cancellingSlug === reservation.slug ? 'Cancelling...' : 'Cancel'}
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <ReservationDeleteDialog
        open={showCancelModal}
        onOpenChange={setShowCancelModal}
        onConfirm={handleConfirmCancel}
        isLoading={cancellingSlug !== null}
        reservationId={selectedReservationSlug || undefined}
      />
    </>
  );
}
