'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { useRouter } from '@/i18n/navigation';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { AppLoading } from '@/components/ui/app-loading';
import { useGetShopBySlugQuery, type Shop, type ShopBranch } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useReservations } from '@/hooks/useReservations';
import { useGetTablesQuery } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { toast } from 'sonner';
import { ReservationForm } from '../_components/ReservationForm';

interface NewReservationPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function NewReservationPage({ params }: NewReservationPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const router = useRouter();

  // Get shop data from backend by slug (includes branches)
  const { data: shop, isLoading: isLoadingShop } = useGetShopBySlugQuery(slugShop);

  // Find the branch from the shop data
  const branch = shop?.branches?.find((b: ShopBranch) => b.slug === slugBranch);

  const shopId = shop?.id;
  const branchId = branch?.id;

  // Get tables for the branch
  const { data: tablesData, isLoading: isLoadingTables } = useGetTablesQuery(
    { shopId: shopId || '', branchId: branchId || '' },
    { skip: !shopId || !branchId }
  );

  // Use reservations hook for creating reservations
  const { createReservation } = useReservations({
    shopSlug: slugShop,
    branchSlug: slugBranch,
  });

  const isLoading = isLoadingShop || isLoadingTables;
  const tables = tablesData || [];

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!shop || !branch) {
    return (
      <div className="text-center py-12">
        <h1 className="text-[#181511] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
        <p className="text-[#887663] text-sm">The branch you are looking for does not exist.</p>
      </div>
    );
  }

  // Handle reservation creation success
  const handleCreateSuccess = async (reservationData: {
    customerName: string;
    contactInformation: string;
    date: string;
    time: string;
    tableId?: string;
    partySize: number;
  }) => {
    try {
      // Transform the data to match the frontend API expectations
      const apiData = {
        customerName: reservationData.customerName,
        customerPhone: reservationData.contactInformation, // Use contact info as phone
        customerEmail: '', // Optional field
        date: reservationData.date,
        time: reservationData.time,
        partySize: reservationData.partySize,
        tableId: reservationData.tableId === 'none' ? undefined : reservationData.tableId,
        specialRequests: '', // Not included in simplified form
        duration: 120, // Default 2 hours
        source: 'website', // Set source
      };

      await createReservation(apiData);
      toast.success('Reservation created successfully!');
      router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations`);
    } catch {
      toast.error('Failed to create reservation');
    }
  };

  const handleCancel = () => {
    router.push(`/app/restaurant/${slugShop}/${slugBranch}/reservations`);
  };

  return (
    <>
      {/* Back Button */}
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Reservations
          </Button>
        </Link>
      </div>

      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-[#181510]">Create New Reservation</h1>
          <p className="text-[#8a745c]">Add a new reservation for {shop.name} - {branch.name}</p>
        </div>

        <ReservationForm
          tables={tables.map(table => ({ id: table.id, number: parseInt(table.number) }))}
          onSuccess={handleCreateSuccess}
          onCancel={handleCancel}
        />
      </div>
    </>
  );
}
