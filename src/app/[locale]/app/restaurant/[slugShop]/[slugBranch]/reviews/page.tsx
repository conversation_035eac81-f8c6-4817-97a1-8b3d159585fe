'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Star, ArrowLeft } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useReviews } from '@/hooks/useReviews';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { ReviewAvatar } from '@/components/ui/safe-avatar';
import React from 'react';


interface ReviewsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function ReviewsPage({ params }: ReviewsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // State for UI
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('all');
  const [isRespondDialogOpen, setIsRespondDialogOpen] = useState(false);
  const [selectedReviewId, setSelectedReviewId] = useState<string | null>(null);
  const [responseText, setResponseText] = useState('');

  // Get shop and branch data directly by slug using real API
  const { data: shop, isLoading: isLoadingShop } = useGetShopBySlugQuery(slugShop);
  const { data: branch, isLoading: isLoadingBranch } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  });

  const shopSlug = slugShop;
  const branchSlug = slugBranch;
  const shopId = shop?.id;
  const branchId = branch?.id;

  // Get reviews data from backend
  const {
    reviews,
    reviewStats,
    isLoading: isLoadingReviews,
    refetch: refetchReviews,
    respondToReview,
    updateStatus,
    updateFilters,
  } = useReviews({
    shopSlug: shopSlug || '',
    branchSlug: branchSlug || '',
    shopId: shopId || '',
    branchId: branchId || '',
    initialFilters: {
      status: activeTab === 'all' ? undefined : activeTab,
      rating: ratingFilter ? parseInt(ratingFilter) : undefined,
      search: searchTerm || undefined,
    }
  });

  // Update filters when UI state changes
  React.useEffect(() => {
    updateFilters({
      status: activeTab === 'all' ? undefined : activeTab,
      rating: ratingFilter ? parseInt(ratingFilter) : undefined,
      search: searchTerm || undefined,
    });
  }, [activeTab, ratingFilter, searchTerm, updateFilters]);

  const isLoading = isLoadingShop || isLoadingBranch || isLoadingReviews;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!shop || !branch) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const filteredReviews = reviews;

  // Handle responding to a review
  const handleOpenRespondDialog = (reviewId: string) => {
    const review = reviews.find(r => r.id === reviewId);
    setSelectedReviewId(reviewId);
    setResponseText(review?.response?.message || '');
    setIsRespondDialogOpen(true);
  };

  // Handle submitting a response
  const handleSubmitResponse = async () => {
    if (!selectedReviewId) return;

    try {
      await respondToReview(selectedReviewId, responseText);
      setIsRespondDialogOpen(false);
      toast.success('Response submitted successfully');
      refetchReviews();
    } catch (error) {
      toast.error('Failed to submit response');
    }
  };

  // Handle updating review status
  const handleUpdateStatus = async (reviewId: string, status: string) => {
    try {
      await updateStatus(reviewId, status);
      toast.success(`Review ${status} successfully`);
      refetchReviews();
    } catch (error) {
      toast.error(`Failed to update review status`);
    }
  };

  // Render stars for rating
  const renderStars = (rating: number) => {
    return Array(5).fill(0).map((_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-yellow-400' : 'text-muted-foreground'}`}
      />
    ));
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      {/* Header */}
      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <p className="text-foreground tracking-light text-[32px] font-bold leading-tight">Reviews</p>
          <p className="text-muted-foreground text-sm font-normal leading-normal">
            Manage and respond to customer reviews for {shop.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-end gap-3">
          <div className="flex flex-col gap-1.5">
            <label htmlFor="rating-filter" className="text-xs font-medium text-muted-foreground">
              Filter by Rating
            </label>
            <Select value={ratingFilter || 'all'} onValueChange={setRatingFilter}>
              <SelectTrigger id="rating-filter" className="w-[120px]">
                <SelectValue placeholder="All Ratings" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Ratings</SelectItem>
                <SelectItem value="5">5 Stars</SelectItem>
                <SelectItem value="4">4 Stars</SelectItem>
                <SelectItem value="3">3 Stars</SelectItem>
                <SelectItem value="2">2 Stars</SelectItem>
                <SelectItem value="1">1 Star</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-1.5">
            <label htmlFor="search-reviews" className="text-xs font-medium text-muted-foreground">
              Search Reviews
            </label>
            <Input
              id="search-reviews"
              placeholder="Search by name or content"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-[250px]"
            />
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="p-1">
          <TabsTrigger value="all">All Reviews</TabsTrigger>
          <TabsTrigger value="approved">Approved</TabsTrigger>
          <TabsTrigger value="pending">Pending</TabsTrigger>
          <TabsTrigger value="flagged">Flagged</TabsTrigger>
          <TabsTrigger value="rejected">Rejected</TabsTrigger>
        </TabsList>

        {/* Reviews List */}
        {filteredReviews.length === 0 ? (
          <div className="text-center py-10 text-muted-foreground">
            No reviews found. Adjust your filters to see more reviews.
          </div>
        ) : (
          <div className="space-y-4">
            {filteredReviews.map((review) => (
              <div key={review.id} className="bg-card rounded-lg border border-border p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center">
                    <ReviewAvatar
                      src={review.customer_avatar}
                      alt={review.customer_name}
                      name={review.customer_name}
                      className="mr-3"
                    />
                    <div>
                      <h3 className="font-medium text-foreground">{review.customer_name}</h3>
                      <div className="flex items-center mt-1">
                        <div className="flex mr-2">
                          {renderStars(review.rating)}
                        </div>
                        <span className="text-xs text-muted-foreground">
                          {format(new Date(review.created_at), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      review.status === 'approved'
                        ? 'bg-green-50 text-green-700 dark:bg-green-950 dark:text-green-300'
                        : review.status === 'pending'
                          ? 'bg-yellow-50 text-yellow-700 dark:bg-yellow-950 dark:text-yellow-300'
                          : review.status === 'flagged'
                            ? 'bg-red-50 text-red-700 dark:bg-red-950 dark:text-red-300'
                            : review.status === 'rejected'
                              ? 'bg-muted text-muted-foreground'
                              : 'bg-muted text-muted-foreground'
                    }`}>
                      {review.status.charAt(0).toUpperCase() + review.status.slice(1)}
                    </span>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-foreground">{review.comment}</p>
                </div>

                {review.title && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-foreground mb-2">Review Title:</h4>
                    <p className="text-muted-foreground text-sm">{review.title}</p>
                  </div>
                )}

                {review.response && (
                  <div className="bg-muted rounded-md p-4 mb-4">
                    <h4 className="text-sm font-medium text-foreground mb-2">Your Response:</h4>
                    <p className="text-muted-foreground text-sm">{review.response.message}</p>
                    <p className="text-muted-foreground text-xs mt-2">
                      Responded on {format(new Date(review.response.responded_at), 'MMM d, yyyy')}
                    </p>
                  </div>
                )}

                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleOpenRespondDialog(review.id)}
                  >
                    {review.response ? 'Edit Response' : 'Respond'}
                  </Button>

                  {review.status !== 'approved' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-green-600 hover:bg-green-50 hover:text-green-700 dark:text-green-400 dark:hover:bg-green-950 dark:hover:text-green-300"
                      onClick={() => handleUpdateStatus(review.id, 'approved')}
                    >
                      Approve
                    </Button>
                  )}

                  {review.status !== 'flagged' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-red-600 hover:bg-red-50 hover:text-red-700 dark:text-red-400 dark:hover:bg-red-950 dark:hover:text-red-300"
                      onClick={() => handleUpdateStatus(review.id, 'flagged')}
                    >
                      Flag
                    </Button>
                  )}

                  {review.status !== 'rejected' && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="text-muted-foreground hover:bg-muted hover:text-foreground"
                      onClick={() => handleUpdateStatus(review.id, 'rejected')}
                    >
                      Reject
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Tabs>

      {/* Response Dialog */}
      <Dialog open={isRespondDialogOpen} onOpenChange={setIsRespondDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Respond to Review</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Textarea
              placeholder="Type your response here..."
              value={responseText}
              onChange={(e) => setResponseText(e.target.value)}
              rows={6}
              className="resize-none"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsRespondDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmitResponse}
            >
              Submit Response
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
