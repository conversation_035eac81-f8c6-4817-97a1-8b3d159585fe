'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { ArrowLeft, Palette, Moon, Sun, Monitor, Check, Save } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import AppearancePreview from '@/components/settings/AppearancePreview';
import { useAppearance } from '@/lib/context/AppearanceContext';
import {
  useGetShopBySlugQuery,
  useGetBranchBySlugQuery,
  useGetBranchSettingsQuery,
  useUpdateBranchSettingsMutation
} from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop, Branch } from '@/lib/redux/api/endpoints/restaurant/shopApi';

interface AppearanceSettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

// Appearance settings interface
interface AppearanceSettings {
  theme: string;
  accentColor: string;
  fontSize: string;
  customFont: string;
  reducedMotion: boolean;
  reducedTransparency: boolean;
  highContrast: boolean;
  compactMode: boolean;
}

// Color theme options - All themes use black and white backgrounds
const colorThemes = [
  { id: 'solar-dusk', name: 'Solar Dusk (Current)', primary: '#d97706', secondary: '#fbbf24', bg: '#fef3c7', text: '#451a03' },
  { id: 'earth', name: 'Earth Tones', primary: '#8a745c', secondary: '#e5ccb2', bg: '#ffffff', text: '#000000' },
  { id: 'ocean', name: 'Ocean Blue', primary: '#3b82f6', secondary: '#93c5fd', bg: '#ffffff', text: '#000000' },
  { id: 'forest', name: 'Forest Green', primary: '#059669', secondary: '#a7f3d0', bg: '#ffffff', text: '#000000' },
  { id: 'sunset', name: 'Sunset Orange', primary: '#ea580c', secondary: '#fdba74', bg: '#ffffff', text: '#000000' },
  { id: 'berry', name: 'Berry Purple', primary: '#8b5cf6', secondary: '#c4b5fd', bg: '#ffffff', text: '#000000' },
];

// Font size options
const fontSizeOptions = [
  { id: 'small', name: 'Small', scale: 0.875 },
  { id: 'medium', name: 'Medium (Default)', scale: 1 },
  { id: 'large', name: 'Large', scale: 1.125 },
  { id: 'x-large', name: 'Extra Large', scale: 1.25 },
];

// Font family options
const fontFamilyOptions = [
  { id: 'oxanium', name: 'Oxanium (Solar Dusk)', fontFamily: 'Oxanium, sans-serif' },
  { id: 'be-vietnam', name: 'Be Vietnam Pro', fontFamily: 'Be Vietnam Pro, sans-serif' },
  { id: 'inter', name: 'Inter', fontFamily: 'Inter, sans-serif' },
  { id: 'roboto', name: 'Roboto', fontFamily: 'Roboto, sans-serif' },
  { id: 'poppins', name: 'Poppins', fontFamily: 'Poppins, sans-serif' },
  { id: 'montserrat', name: 'Montserrat', fontFamily: 'Montserrat, sans-serif' },
];

export default function AppearanceSettingsPage({ params }: AppearanceSettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get branch settings
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
  } = useGetBranchSettingsQuery(
    { shopId: shopData?.id || '', branchId: branchData?.id || '' },
    { skip: !shopData?.id || !branchData?.id }
  );

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  const [activeTab, setActiveTab] = useState('theme');

  // Local appearance settings state
  const [appearanceSettings, setAppearanceSettings] = useState<AppearanceSettings>({
    theme: 'light',
    accentColor: 'solar-dusk',
    fontSize: 'medium',
    customFont: 'oxanium',
    reducedMotion: false,
    reducedTransparency: false,
    highContrast: false,
    compactMode: false,
  });

  // Use the global appearance context for live preview
  const { settings, updateSettings } = useAppearance();

  const isLoading = isShopLoading || isBranchLoading || isLoadingSettings;
  const hasError = shopError || branchError;

  // Load appearance settings from branch settings
  useEffect(() => {
    if (branchSettings) {
      const newSettings = {
        theme: branchSettings.appearanceTheme || 'light',
        accentColor: branchSettings.appearanceAccentColor || 'solar-dusk',
        fontSize: branchSettings.appearanceFontSize || 'medium',
        customFont: branchSettings.appearanceCustomFont || 'oxanium',
        reducedMotion: branchSettings.appearanceReducedMotion || false,
        reducedTransparency: branchSettings.appearanceReducedTransparency || false,
        highContrast: branchSettings.appearanceHighContrast || false,
        compactMode: branchSettings.appearanceCompactMode || false,
      };

      setAppearanceSettings(newSettings);
    }
  }, [branchSettings]);

  // Separate effect to update global context only when local settings change
  useEffect(() => {
    updateSettings(appearanceSettings);
  }, [appearanceSettings, updateSettings]);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const handleSave = async () => {
    if (!shopData?.id || !branchData?.id) {
      toast.error('Missing shop or branch information');
      return;
    }

    try {
      // Update branch settings using real API
      await updateBranchSettings({
        shopId: shopData.id,
        branchId: branchData.id,
        settings: {
          // Appearance settings
          appearanceTheme: appearanceSettings.theme,
          appearanceAccentColor: appearanceSettings.accentColor,
          appearanceFontSize: appearanceSettings.fontSize,
          appearanceCustomFont: appearanceSettings.customFont,
          appearanceReducedMotion: appearanceSettings.reducedMotion,
          appearanceReducedTransparency: appearanceSettings.reducedTransparency,
          appearanceHighContrast: appearanceSettings.highContrast,
          appearanceCompactMode: appearanceSettings.compactMode,
        },
      }).unwrap();

      toast.success('Appearance settings saved successfully! Changes applied to the website.');
    } catch (error) {
      console.error('Error saving appearance settings:', error);
      toast.error('Failed to save appearance settings. Please try again.');
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    // Update local state - this will trigger the useEffect to update global context
    setAppearanceSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground text-[32px] font-bold leading-tight">Appearance Settings</h1>
          <p className="text-muted-foreground text-sm">Customize the look and feel of your restaurant dashboard for {shopData.name} - {branchData.name}</p>
        </div>
        <Button
          onClick={handleSave}
          disabled={isUpdating}
          className="bg-primary hover:bg-primary/90 text-primary-foreground font-bold px-6 h-12"
        >
          <Save className="w-4 h-4 mr-2" />
          {isUpdating ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>

    <div className="max-w-7xl">

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="bg-muted mb-6">
              <TabsTrigger value="theme" className="data-[state=active]:bg-background">
                <Palette className="h-4 w-4 mr-2" />
                Theme & Colors
              </TabsTrigger>
              <TabsTrigger value="typography" className="data-[state=active]:bg-background">
                <span className="font-serif mr-2">A</span>
                Typography
              </TabsTrigger>
              <TabsTrigger value="accessibility" className="data-[state=active]:bg-background">
                <span className="mr-2">👁️</span>
                Accessibility
              </TabsTrigger>
            </TabsList>

            <TabsContent value="theme" className="space-y-6">
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Theme Mode</CardTitle>
                  <CardDescription>Choose between light, dark, or system theme</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={appearanceSettings.theme}
                    onValueChange={(value) => handleInputChange('theme', value)}
                    className="flex flex-col space-y-1 sm:flex-row sm:space-x-4 sm:space-y-0"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="light" id="theme-light" />
                      <Label htmlFor="theme-light" className="flex items-center">
                        <Sun className="h-4 w-4 mr-2" />
                        Light
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="dark" id="theme-dark" />
                      <Label htmlFor="theme-dark" className="flex items-center">
                        <Moon className="h-4 w-4 mr-2" />
                        Dark
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="system" id="theme-system" />
                      <Label htmlFor="theme-system" className="flex items-center">
                        <Monitor className="h-4 w-4 mr-2" />
                        System
                      </Label>
                    </div>
                  </RadioGroup>
                </CardContent>
              </Card>

              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Color Theme</CardTitle>
                  <CardDescription>Choose a color theme for your dashboard</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {colorThemes.map((theme) => (
                      <div
                        key={theme.id}
                        className={cn(
                          "flex flex-col p-4 rounded-lg border-2 cursor-pointer transition-all",
                          appearanceSettings.accentColor === theme.id
                            ? "border-primary"
                            : "border-border hover:border-muted-foreground"
                        )}
                        onClick={() => handleInputChange('accentColor', theme.id)}
                      >
                        <div className="flex justify-between items-center mb-3">
                          <span className="font-medium text-foreground">{theme.name}</span>
                          {appearanceSettings.accentColor === theme.id && (
                            <Check className="h-4 w-4 text-primary" />
                          )}
                        </div>
                        <div className="flex gap-2">
                          <div
                            className="w-8 h-8 rounded-full"
                            style={{ backgroundColor: theme.primary }}
                          ></div>
                          <div
                            className="w-8 h-8 rounded-full"
                            style={{ backgroundColor: theme.secondary }}
                          ></div>
                          <div
                            className="w-8 h-8 rounded-full border border-border"
                            style={{ backgroundColor: theme.bg }}
                          ></div>
                          <div
                            className="w-8 h-8 rounded-full"
                            style={{ backgroundColor: theme.text }}
                          ></div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="typography" className="space-y-6">
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Font Size</CardTitle>
                  <CardDescription>Adjust the size of text throughout the dashboard</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={appearanceSettings.fontSize}
                    onValueChange={(value) => handleInputChange('fontSize', value)}
                    className="space-y-4"
                  >
                    {fontSizeOptions.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.id} id={`font-size-${option.id}`} />
                        <Label
                          htmlFor={`font-size-${option.id}`}
                          style={{ fontSize: `${option.scale}rem` }}
                        >
                          {option.name}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </CardContent>
              </Card>

              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Font Family</CardTitle>
                  <CardDescription>Choose a font family for the dashboard</CardDescription>
                </CardHeader>
                <CardContent>
                  <RadioGroup
                    value={appearanceSettings.customFont}
                    onValueChange={(value) => handleInputChange('customFont', value)}
                    className="space-y-4"
                  >
                    {fontFamilyOptions.map((option) => (
                      <div key={option.id} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.id} id={`font-family-${option.id}`} />
                        <Label
                          htmlFor={`font-family-${option.id}`}
                          style={{ fontFamily: option.fontFamily }}
                          className="text-lg"
                        >
                          {option.name}
                        </Label>
                      </div>
                    ))}
                  </RadioGroup>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="accessibility" className="space-y-6">
              <Card className="bg-card border-border">
                <CardHeader>
                  <CardTitle>Accessibility Options</CardTitle>
                  <CardDescription>Adjust settings to improve accessibility</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="reduced-motion" className="text-base">Reduced Motion</Label>
                      <p className="text-sm text-muted-foreground">Minimize animations and transitions</p>
                    </div>
                    <Switch
                      id="reduced-motion"
                      checked={appearanceSettings.reducedMotion}
                      onCheckedChange={(checked) => handleInputChange('reducedMotion', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="reduced-transparency" className="text-base">Reduced Transparency</Label>
                      <p className="text-sm text-muted-foreground">Reduce transparency and blur effects</p>
                    </div>
                    <Switch
                      id="reduced-transparency"
                      checked={appearanceSettings.reducedTransparency}
                      onCheckedChange={(checked) => handleInputChange('reducedTransparency', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="high-contrast" className="text-base">High Contrast</Label>
                      <p className="text-sm text-muted-foreground">Increase contrast for better readability</p>
                    </div>
                    <Switch
                      id="high-contrast"
                      checked={appearanceSettings.highContrast}
                      onCheckedChange={(checked) => handleInputChange('highContrast', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="compact-mode" className="text-base">Compact Mode</Label>
                      <p className="text-sm text-muted-foreground">Reduce spacing between elements</p>
                    </div>
                    <Switch
                      id="compact-mode"
                      checked={appearanceSettings.compactMode}
                      onCheckedChange={(checked) => handleInputChange('compactMode', checked)}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        <div className="lg:sticky lg:top-20">
          <AppearancePreview
            theme={settings.theme}
            accentColor={settings.accentColor}
            fontSize={settings.fontSize}
            customFont={settings.customFont}
            reducedMotion={settings.reducedMotion}
            highContrast={settings.highContrast}
            compactMode={settings.compactMode}
          />
        </div>
      </div>
      </div>
    </div>
  );
}
