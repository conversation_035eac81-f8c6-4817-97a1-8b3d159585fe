'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { useNavigation } from '@/lib/context/NavigationContext';
import { NavigationType } from '@/lib/types/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Check } from 'lucide-react';
import { toast } from 'sonner';

export default function NavigationSettingsPage() {
  const { settings, updateSettings, resetSettings } = useNavigation();

  const handleTypeChange = (value: string) => {
    updateSettings({ type: value as NavigationType });
    toast.success('Navigation type updated');
  };

  const handleToggleChange = (key: keyof typeof settings, value: boolean) => {
    updateSettings({ [key]: value });
    toast.success('Navigation settings updated');
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center gap-4 mb-6">
        <Link href="/app/restaurant/settings">
          <Button variant="outline" className="border-[#e5e1dc]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
        <h1 className="text-[#181510] text-2xl font-bold leading-tight">Navigation Settings</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle>Navigation Type</CardTitle>
            <CardDescription>Choose how you want to navigate through the application</CardDescription>
          </CardHeader>
          <CardContent>
            <RadioGroup
              value={settings.type}
              onValueChange={handleTypeChange}
              className="space-y-4"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={NavigationType.HEADER} id="header" />
                <Label htmlFor="header" className="flex items-center">
                  <span className="ml-2">Header Navigation</span>
                  {settings.type === NavigationType.HEADER && (
                    <Check className="h-4 w-4 ml-2 text-green-500" />
                  )}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={NavigationType.SIDEBAR_LEFT} id="sidebar-left" />
                <Label htmlFor="sidebar-left" className="flex items-center">
                  <span className="ml-2">Left Sidebar Navigation</span>
                  {settings.type === NavigationType.SIDEBAR_LEFT && (
                    <Check className="h-4 w-4 ml-2 text-green-500" />
                  )}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={NavigationType.SIDEBAR_RIGHT} id="sidebar-right" />
                <Label htmlFor="sidebar-right" className="flex items-center">
                  <span className="ml-2">Right Sidebar Navigation</span>
                  {settings.type === NavigationType.SIDEBAR_RIGHT && (
                    <Check className="h-4 w-4 ml-2 text-green-500" />
                  )}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={NavigationType.DRAWER} id="drawer" />
                <Label htmlFor="drawer" className="flex items-center">
                  <span className="ml-2">Drawer Navigation</span>
                  {settings.type === NavigationType.DRAWER && (
                    <Check className="h-4 w-4 ml-2 text-green-500" />
                  )}
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value={NavigationType.BOTTOM} id="bottom" />
                <Label htmlFor="bottom" className="flex items-center">
                  <span className="ml-2">Bottom Navigation</span>
                  {settings.type === NavigationType.BOTTOM && (
                    <Check className="h-4 w-4 ml-2 text-green-500" />
                  )}
                </Label>
              </div>
            </RadioGroup>
          </CardContent>
        </Card>

        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle>Display Options</CardTitle>
            <CardDescription>Customize how navigation elements are displayed</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="show-icons" className="text-base">Show Icons</Label>
                <p className="text-sm text-[#8a745c]">Display icons next to navigation items</p>
              </div>
              <Switch
                id="show-icons"
                checked={settings.showIcons}
                onCheckedChange={(checked) => handleToggleChange('showIcons', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="show-labels" className="text-base">Show Labels</Label>
                <p className="text-sm text-[#8a745c]">Display text labels for navigation items</p>
              </div>
              <Switch
                id="show-labels"
                checked={settings.showLabels}
                onCheckedChange={(checked) => handleToggleChange('showLabels', checked)}
              />
            </div>

            {(settings.type === NavigationType.SIDEBAR_LEFT || settings.type === NavigationType.SIDEBAR_RIGHT) && (
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="is-collapsed" className="text-base">Collapsed Sidebar</Label>
                  <p className="text-sm text-[#8a745c]">Show sidebar in collapsed state by default</p>
                </div>
                <Switch
                  id="is-collapsed"
                  checked={settings.isCollapsed}
                  onCheckedChange={(checked) => handleToggleChange('isCollapsed', checked)}
                />
              </div>
            )}

            {(settings.type === NavigationType.HEADER || settings.type === NavigationType.BOTTOM) && (
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-hide" className="text-base">Auto-hide Navigation</Label>
                  <p className="text-sm text-[#8a745c]">Hide navigation when scrolling down</p>
                </div>
                <Switch
                  id="auto-hide"
                  checked={settings.autoHide}
                  onCheckedChange={(checked) => handleToggleChange('autoHide', checked)}
                />
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              variant="outline"
              className="border-[#e5e1dc]"
              onClick={resetSettings}
            >
              Reset to Default
            </Button>
            <Button
              className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
              onClick={() => toast.success('Settings saved successfully')}
            >
              Save Settings
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
