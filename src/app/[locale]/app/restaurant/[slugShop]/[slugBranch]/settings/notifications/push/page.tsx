'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel 
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowLeft, 
  MessageSquare, 
  Calendar, 
  ShoppingCart, 
  Star, 
  Users, 
  AlertCircle,
  Smartphone,
  Volume2,
  Clock
} from 'lucide-react';
import { toast } from 'sonner';

// Define the push notification settings schema
const pushNotificationSettingsSchema = z.object({
  // Push notifications
  pushEnabled: z.boolean().default(true),
  deviceToken: z.string().optional(),
  pushOrders: z.boolean().default(true),
  pushReservations: z.boolean().default(true),
  pushReviews: z.boolean().default(false),
  pushStaff: z.boolean().default(false),
  pushSystem: z.boolean().default(true),
  quietHoursEnabled: z.boolean().default(false),
  quietHoursStart: z.string().default('22:00'),
  quietHoursEnd: z.string().default('08:00'),
  soundEnabled: z.boolean().default(true),
  vibrationEnabled: z.boolean().default(true),
  priority: z.enum(['normal', 'high']).default('normal'),
});

type PushNotificationSettings = z.infer<typeof pushNotificationSettingsSchema>;

// Mock data for initial settings
const mockPushSettings: PushNotificationSettings = {
  pushEnabled: true,
  deviceToken: 'fcm-token-example-123456789',
  pushOrders: true,
  pushReservations: true,
  pushReviews: false,
  pushStaff: false,
  pushSystem: true,
  quietHoursEnabled: false,
  quietHoursStart: '22:00',
  quietHoursEnd: '08:00',
  soundEnabled: true,
  vibrationEnabled: true,
  priority: 'normal',
};

export default function PushNotificationsSettingsPage() {
  const [isSaving, setIsSaving] = useState(false);
  
  // Initialize form with mock data
  const form = useForm<PushNotificationSettings>({
    resolver: zodResolver(pushNotificationSettingsSchema),
    defaultValues: mockPushSettings,
  });
  
  // Watch for changes to enabled toggles
  const pushEnabled = form.watch('pushEnabled');
  const quietHoursEnabled = form.watch('quietHoursEnabled');
  
  // Handle form submission
  const onSubmit = async (data: PushNotificationSettings) => {
    setIsSaving(true);
    
    try {
      // In a real implementation, this would save to an API
      console.log('Saving push notification settings:', data);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 800));
      
      toast.success('Push notification settings saved', {
        description: 'Your push notification preferences have been updated.',
      });
    } catch (error) {
      toast.error('Failed to save settings', {
        description: 'There was an error saving your push notification preferences.',
      });
      console.error('Error saving push notification settings:', error);
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <div className="p-6 font-be-vietnam">
      <div className="mb-6">
        <Link 
          href="/app/restaurant/settings/notifications" 
          className="flex items-center text-[#8a745c] hover:text-[#181510] transition-colors"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Notification Settings
        </Link>
      </div>
      
      <div className="flex flex-col gap-2 mb-6">
        <h1 className="text-[#181510] text-2xl font-bold">Push Notification Settings</h1>
        <p className="text-[#8a745c]">Configure how you receive push notifications on your mobile devices</p>
      </div>
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5 text-[#8a745c]" />
                  <CardTitle>Push Notifications</CardTitle>
                </div>
                <FormField
                  control={form.control}
                  name="pushEnabled"
                  render={({ field }) => (
                    <FormItem className="flex items-center space-x-2 space-y-0">
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <CardDescription>
                Configure notifications sent to your mobile devices
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center p-4 rounded-lg bg-[#f1edea]">
                <Smartphone className="h-5 w-5 text-[#8a745c] mr-3" />
                <div>
                  <p className="text-sm font-medium text-[#181510]">Mobile App Required</p>
                  <p className="text-xs text-[#8a745c]">
                    To receive push notifications, you need to install our mobile app and log in with your account.
                  </p>
                </div>
              </div>
              
              <Separator className="my-6" />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-[#181510]">Notification Types</h3>
                
                <FormField
                  control={form.control}
                  name="pushOrders"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <ShoppingCart className="h-4 w-4 text-blue-500" />
                          <FormLabel className="font-medium text-[#181510]">Order Notifications</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive push notifications about new orders and order status changes
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!pushEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="pushReservations"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-green-500" />
                          <FormLabel className="font-medium text-[#181510]">Reservation Notifications</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive push notifications about new, updated, or canceled reservations
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!pushEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="pushReviews"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <FormLabel className="font-medium text-[#181510]">Review Notifications</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive push notifications when customers leave reviews
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!pushEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="pushStaff"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-purple-500" />
                          <FormLabel className="font-medium text-[#181510]">Staff Notifications</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive push notifications about staff schedule changes and requests
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!pushEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="pushSystem"
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                      <div className="space-y-0.5">
                        <div className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500" />
                          <FormLabel className="font-medium text-[#181510]">System Notifications</FormLabel>
                        </div>
                        <FormDescription className="text-[#8a745c]">
                          Receive important system alerts and updates on your mobile device
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={!pushEnabled}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              
              <Separator className="my-6" />
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-medium text-[#181510]">Quiet Hours</h3>
                    <p className="text-sm text-[#8a745c]">Set a time period when you don't want to receive notifications</p>
                  </div>
                  <FormField
                    control={form.control}
                    name="quietHoursEnabled"
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField
                    control={form.control}
                    name="quietHoursStart"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Time</FormLabel>
                        <div className="flex items-center">
                          <FormControl>
                            <Input
                              type="time"
                              {...field}
                              disabled={!pushEnabled || !quietHoursEnabled}
                              className="bg-white border-[#e5e1dc]"
                            />
                          </FormControl>
                          <Clock className="h-4 w-4 ml-2 text-[#8a745c]" />
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="quietHoursEnd"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Time</FormLabel>
                        <div className="flex items-center">
                          <FormControl>
                            <Input
                              type="time"
                              {...field}
                              disabled={!pushEnabled || !quietHoursEnabled}
                              className="bg-white border-[#e5e1dc]"
                            />
                          </FormControl>
                          <Clock className="h-4 w-4 ml-2 text-[#8a745c]" />
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
              
              <Separator className="my-6" />
              
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-[#181510]">Notification Behavior</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="soundEnabled"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Volume2 className="h-4 w-4 text-[#8a745c]" />
                            <FormLabel className="font-medium text-[#181510]">Sound</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Play sound when notifications arrive
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vibrationEnabled"
                    render={({ field }) => (
                      <FormItem className="flex items-center justify-between rounded-lg border border-[#e5e1dc] p-4">
                        <div className="space-y-0.5">
                          <div className="flex items-center gap-2">
                            <Smartphone className="h-4 w-4 text-[#8a745c]" />
                            <FormLabel className="font-medium text-[#181510]">Vibration</FormLabel>
                          </div>
                          <FormDescription className="text-[#8a745c]">
                            Vibrate when notifications arrive
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            disabled={!pushEnabled}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="priority"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notification Priority</FormLabel>
                      <FormDescription>
                        High priority notifications may appear as pop-ups even when your device is locked
                      </FormDescription>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                        disabled={!pushEnabled}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-white border-[#e5e1dc]">
                            <SelectValue placeholder="Select priority" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="normal">Normal</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset(mockPushSettings)}
                className="border-[#e5e1dc] text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]"
              >
                Reset to Default
              </Button>
              <Button 
                type="submit" 
                disabled={isSaving}
                className="bg-[#8a745c] hover:bg-[#6d5a49] text-white"
              >
                {isSaving ? 'Saving...' : 'Save Changes'}
              </Button>
            </CardFooter>
          </Card>
        </form>
      </Form>
    </div>
  );
}
