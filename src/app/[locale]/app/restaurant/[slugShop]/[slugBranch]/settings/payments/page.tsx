'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

import {
  ArrowLeft,
  CreditCard,
  Smartphone,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  QrCode,
  Shield,
  Zap
} from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery, useGetBranchSettingsQuery, useUpdateBranchSettingsMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import type { Shop, Branch } from '@/lib/types/shop';

interface PaymentSettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

interface PaymentSettings {
  promptPay: {
    enabled: boolean;
    promptPayId: string;
    accountHolderName: string;
    phoneNumber: string;
    verified: boolean;
  };
  stripe: {
    connected: boolean;
    accountId: string;
    accountStatus: 'pending' | 'active' | 'restricted' | 'inactive';
    liveMode: boolean;
    webhookConfigured: boolean;
    accountEmail: string;
    country: string;
  };
  general: {
    defaultCurrency: string;
    processingFee: number;
    autoCapture: boolean;
  };
}

export default function PaymentSettingsPage({ params }: PaymentSettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get branch settings
  const {
    data: branchSettings,
    isLoading: isLoadingSettings,
  } = useGetBranchSettingsQuery(
    { shopId: shopData?.id || '', branchId: branchData?.id || '' },
    { skip: !shopData?.id || !branchData?.id }
  );

  // Update branch settings mutation
  const [updateBranchSettings, { isLoading: isUpdating }] = useUpdateBranchSettingsMutation();

  const [paymentSettings, setPaymentSettings] = useState<PaymentSettings>({
    promptPay: {
      enabled: false,
      promptPayId: '',
      accountHolderName: '',
      phoneNumber: '',
      verified: false,
    },
    stripe: {
      connected: false,
      accountId: '',
      accountStatus: 'inactive',
      liveMode: false,
      webhookConfigured: false,
      accountEmail: '',
      country: 'TH',
    },
    general: {
      defaultCurrency: 'THB',
      processingFee: 2.9,
      autoCapture: true,
    },
  });

  const isLoading = isShopLoading || isBranchLoading || isLoadingSettings;
  const hasError = shopError || branchError;

  // Load payment settings from branch settings and shop/branch data
  useEffect(() => {
    if (shopData && branchData) {
      setPaymentSettings(prev => ({
        ...prev,
        promptPay: {
          ...prev.promptPay,
          accountHolderName: shopData.name,
          phoneNumber: branchData.phone || '',
        },
      }));
    }

    if (branchSettings) {
      setPaymentSettings(prev => ({
        ...prev,
        // Map payment methods from settings
        general: {
          ...prev.general,
          defaultCurrency: branchSettings.currency || 'THB',
        },
        // Map PromptPay settings if available
        promptPay: {
          ...prev.promptPay,
          enabled: branchSettings.paymentMethods?.includes('promptpay') || false,
        },
        // Map Stripe settings if available
        stripe: {
          ...prev.stripe,
          connected: branchSettings.paymentMethods?.includes('stripe') || false,
        },
      }));
    }
  }, [shopData, branchData, branchSettings]);

  const handlePromptPayChange = (field: keyof PaymentSettings['promptPay'], value: string | boolean) => {
    setPaymentSettings(prev => ({
      ...prev,
      promptPay: {
        ...prev.promptPay,
        [field]: value,
      },
    }));
  };

  const handleStripeChange = (field: keyof PaymentSettings['stripe'], value: string | boolean) => {
    setPaymentSettings(prev => ({
      ...prev,
      stripe: {
        ...prev.stripe,
        [field]: value,
      },
    }));
  };

  const handleGeneralChange = (field: keyof PaymentSettings['general'], value: string | boolean | number) => {
    setPaymentSettings(prev => ({
      ...prev,
      general: {
        ...prev.general,
        [field]: value,
      },
    }));
  };

  const handleConnectStripe = async () => {
    try {
      // Simulate Stripe Connect flow
      await new Promise(resolve => setTimeout(resolve, 2000));
      setPaymentSettings(prev => ({
        ...prev,
        stripe: {
          ...prev.stripe,
          connected: true,
          accountId: 'acct_1234567890',
          accountStatus: 'active',
          accountEmail: '<EMAIL>',
          webhookConfigured: true,
        },
      }));
      toast.success('Stripe account connected successfully!');
    } catch (error) {
      toast.error('Failed to connect Stripe account. Please try again.');
    }
  };

  const handleDisconnectStripe = async () => {
    try {
      // Simulate disconnect
      await new Promise(resolve => setTimeout(resolve, 1000));
      setPaymentSettings(prev => ({
        ...prev,
        stripe: {
          ...prev.stripe,
          connected: false,
          accountId: '',
          accountStatus: 'inactive',
          accountEmail: '',
          webhookConfigured: false,
        },
      }));
      toast.success('Stripe account disconnected successfully!');
    } catch (error) {
      toast.error('Failed to disconnect Stripe account. Please try again.');
    }
  };

  const handleVerifyPromptPay = async () => {
    try {
      // Simulate verification
      await new Promise(resolve => setTimeout(resolve, 1500));
      setPaymentSettings(prev => ({
        ...prev,
        promptPay: {
          ...prev.promptPay,
          verified: true,
        },
      }));
      toast.success('PromptPay account verified successfully!');
    } catch (error) {
      toast.error('Failed to verify PromptPay account. Please try again.');
    }
  };

  const handleSaveSettings = async () => {
    if (!shopData?.id || !branchData?.id) {
      toast.error('Missing shop or branch information');
      return;
    }

    try {
      // Prepare payment methods array
      const paymentMethods = [];
      if (paymentSettings.promptPay.enabled) {
        paymentMethods.push('promptpay');
      }
      if (paymentSettings.stripe.connected) {
        paymentMethods.push('stripe');
      }

      // Update branch settings using real API
      await updateBranchSettings({
        shopId: shopData.id,
        branchId: branchData.id,
        settings: {
          currency: paymentSettings.general.defaultCurrency,
          paymentMethods,
        },
      }).unwrap();

      toast.success('Payment settings saved successfully!');
    } catch (error) {
      console.error('Error saving payment settings:', error);
      toast.error('Failed to save payment settings. Please try again.');
    }
  };

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-[#8a745c] text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Payment Integration</h1>
          <p className="text-[#8a745c] text-sm">Configure payment methods for {shopData.name} - {branchData.name}</p>
        </div>
      </div>

      <div className="max-w-4xl space-y-6">
        {/* PromptPay Configuration */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-[#f1edea] p-2 rounded-lg">
                <Smartphone className="h-5 w-5 text-[#8a745c]" />
              </div>
              <div>
                <CardTitle className="text-[#181510] text-lg">PromptPay Integration</CardTitle>
                <CardDescription className="text-[#8a745c]">
                  Accept payments via PromptPay QR codes for Thai customers
                </CardDescription>
              </div>
              <div className="ml-auto">
                <Switch
                  checked={paymentSettings.promptPay.enabled}
                  onCheckedChange={(checked) => handlePromptPayChange('enabled', checked)}
                  className="data-[state=checked]:bg-[#e58219]"
                />
              </div>
            </div>
          </CardHeader>

          {paymentSettings.promptPay.enabled && (
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="promptPayId" className="text-[#181510] text-base font-medium leading-normal">
                    PromptPay ID
                  </Label>
                  <Input
                    id="promptPayId"
                    value={paymentSettings.promptPay.promptPayId}
                    onChange={(e) => handlePromptPayChange('promptPayId', e.target.value)}
                    className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-12 text-base"
                    placeholder="Enter your PromptPay ID"
                  />
                </div>

                <div>
                  <Label htmlFor="accountHolderName" className="text-[#181510] text-base font-medium leading-normal">
                    Account Holder Name
                  </Label>
                  <Input
                    id="accountHolderName"
                    value={paymentSettings.promptPay.accountHolderName}
                    onChange={(e) => handlePromptPayChange('accountHolderName', e.target.value)}
                    className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-12 text-base"
                    placeholder="Enter account holder name"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="phoneNumber" className="text-[#181510] text-base font-medium leading-normal">
                  Phone Number
                </Label>
                <Input
                  id="phoneNumber"
                  value={paymentSettings.promptPay.phoneNumber}
                  onChange={(e) => handlePromptPayChange('phoneNumber', e.target.value)}
                  className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-12 text-base"
                  placeholder="Enter phone number"
                />
              </div>

              <div className="flex items-center justify-between p-4 bg-[#f8f6f3] rounded-lg">
                <div className="flex items-center gap-3">
                  <QrCode className="h-5 w-5 text-[#8a745c]" />
                  <div>
                    <p className="text-[#181510] font-medium">Verification Status</p>
                    <p className="text-[#8a745c] text-sm">
                      {paymentSettings.promptPay.verified ? 'Account verified and ready to use' : 'Account needs verification'}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {paymentSettings.promptPay.verified ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : (
                    <>
                      <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Pending
                      </Badge>
                      <Button
                        onClick={handleVerifyPromptPay}
                        disabled={isUpdating || !paymentSettings.promptPay.promptPayId}
                        size="sm"
                        className="bg-[#e58219] hover:bg-[#d67917] text-white"
                      >
                        Verify
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          )}
        </Card>

        {/* Stripe Connect Configuration */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="bg-[#f1edea] p-2 rounded-lg">
                <CreditCard className="h-5 w-5 text-[#8a745c]" />
              </div>
              <div>
                <CardTitle className="text-[#181510] text-lg">Stripe Connect</CardTitle>
                <CardDescription className="text-[#8a745c]">
                  Accept international credit card payments via Stripe
                </CardDescription>
              </div>
              <div className="ml-auto">
                {paymentSettings.stripe.connected ? (
                  <Badge className="bg-green-100 text-green-800 border-green-200">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Connected
                  </Badge>
                ) : (
                  <Badge className="bg-gray-100 text-gray-800 border-gray-200">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Not Connected
                  </Badge>
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-4">
            {!paymentSettings.stripe.connected ? (
              <div className="text-center py-8">
                <div className="bg-[#f8f6f3] rounded-lg p-6 mb-4">
                  <Shield className="h-12 w-12 text-[#8a745c] mx-auto mb-3" />
                  <h3 className="text-[#181510] font-semibold mb-2">Connect your Stripe account</h3>
                  <p className="text-[#8a745c] text-sm mb-4">
                    Securely connect your Stripe account to start accepting credit card payments from customers worldwide.
                  </p>
                  <Button
                    onClick={handleConnectStripe}
                    disabled={isUpdating}
                    className="bg-[#635bff] hover:bg-[#5a52e8] text-white"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    {isUpdating ? 'Connecting...' : 'Connect with Stripe'}
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 bg-[#f8f6f3] rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Shield className="h-4 w-4 text-[#8a745c]" />
                      <span className="text-[#181510] font-medium">Account Status</span>
                    </div>
                    <Badge
                      className={
                        paymentSettings.stripe.accountStatus === 'active'
                          ? "bg-green-100 text-green-800 border-green-200"
                          : paymentSettings.stripe.accountStatus === 'pending'
                          ? "bg-yellow-100 text-yellow-800 border-yellow-200"
                          : "bg-red-100 text-red-800 border-red-200"
                      }
                    >
                      {paymentSettings.stripe.accountStatus.charAt(0).toUpperCase() + paymentSettings.stripe.accountStatus.slice(1)}
                    </Badge>
                  </div>

                  <div className="p-4 bg-[#f8f6f3] rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <CreditCard className="h-4 w-4 text-[#8a745c]" />
                      <span className="text-[#181510] font-medium">Account ID</span>
                    </div>
                    <p className="text-[#8a745c] text-sm font-mono">{paymentSettings.stripe.accountId}</p>
                  </div>
                </div>

                <div className="p-4 bg-[#f8f6f3] rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Zap className="h-4 w-4 text-[#8a745c]" />
                      <span className="text-[#181510] font-medium">Live Mode</span>
                    </div>
                    <Switch
                      checked={paymentSettings.stripe.liveMode}
                      onCheckedChange={(checked) => handleStripeChange('liveMode', checked)}
                      className="data-[state=checked]:bg-[#e58219]"
                    />
                  </div>
                  <p className="text-[#8a745c] text-sm">
                    {paymentSettings.stripe.liveMode
                      ? 'Processing real payments'
                      : 'Test mode - no real charges will be made'
                    }
                  </p>
                </div>

                <div className="flex items-center justify-between p-4 bg-[#f8f6f3] rounded-lg">
                  <div>
                    <p className="text-[#181510] font-medium">Webhook Configuration</p>
                    <p className="text-[#8a745c] text-sm">
                      {paymentSettings.stripe.webhookConfigured
                        ? 'Webhooks are properly configured'
                        : 'Webhooks need to be configured'
                      }
                    </p>
                  </div>
                  {paymentSettings.stripe.webhookConfigured ? (
                    <Badge className="bg-green-100 text-green-800 border-green-200">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Configured
                    </Badge>
                  ) : (
                    <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      Pending
                    </Badge>
                  )}
                </div>

                <div className="flex justify-end">
                  <Button
                    onClick={handleDisconnectStripe}
                    disabled={isUpdating}
                    variant="outline"
                    className="border-red-200 text-red-600 hover:bg-red-50"
                  >
                    Disconnect Stripe
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* General Payment Settings */}
        <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] text-lg">General Settings</CardTitle>
            <CardDescription className="text-[#8a745c]">
              Configure general payment processing preferences
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="defaultCurrency" className="text-foreground text-base font-medium leading-normal">
                  Default Currency
                </Label>
                <Select
                  value={paymentSettings.general.defaultCurrency}
                  onValueChange={(value) => handleGeneralChange('defaultCurrency', value)}
                >
                  <SelectTrigger className="mt-2 border-border bg-background h-12">
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="THB">THB - Thai Baht</SelectItem>
                    <SelectItem value="USD">USD - US Dollar</SelectItem>
                    <SelectItem value="EUR">EUR - Euro</SelectItem>
                    <SelectItem value="GBP">GBP - British Pound</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="processingFee" className="text-[#181510] text-base font-medium leading-normal">
                  Processing Fee (%)
                </Label>
                <Input
                  id="processingFee"
                  type="number"
                  step="0.1"
                  value={paymentSettings.general.processingFee}
                  onChange={(e) => handleGeneralChange('processingFee', parseFloat(e.target.value))}
                  className="mt-2 border-[#e5e1dc] bg-white focus:border-[#e5e1dc] h-12 text-base"
                  placeholder="2.9"
                />
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-[#f8f6f3] rounded-lg">
              <div>
                <p className="text-[#181510] font-medium">Auto-capture Payments</p>
                <p className="text-[#8a745c] text-sm">
                  Automatically capture payments when orders are confirmed
                </p>
              </div>
              <Switch
                checked={paymentSettings.general.autoCapture}
                onCheckedChange={(checked) => handleGeneralChange('autoCapture', checked)}
                className="data-[state=checked]:bg-[#e58219]"
              />
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end pt-4">
          <Button
            onClick={handleSaveSettings}
            disabled={isUpdating}
            className="bg-[#e58219] hover:bg-[#d67917] text-white font-bold px-6 h-12"
          >
            {isUpdating ? 'Saving...' : 'Save Payment Settings'}
          </Button>
        </div>
      </div>
    </div>
  );
}
