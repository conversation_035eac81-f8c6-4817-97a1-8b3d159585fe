'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { 
  ArrowLeft,
  Printer,
  Wifi,
  Monitor,
  Smartphone,
  Tablet,
  CreditCard,
  QrCode,
  Settings,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plus,
  Edit,
  Trash2,
  Power,
  Save,
  RefreshCw,
  Bluetooth,
  Usb,
  Network
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

// Types
interface POSDevice {
  id: string;
  name: string;
  type: 'terminal' | 'printer' | 'scanner' | 'display' | 'cash_drawer' | 'scale';
  brand: string;
  model: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  connectionType: 'wifi' | 'ethernet' | 'bluetooth' | 'usb' | 'serial';
  ipAddress?: string;
  macAddress?: string;
  lastSeen: Date;
  location: string;
  settings?: Record<string, any>;
}

interface NetworkSettings {
  wifiSSID: string;
  wifiPassword: string;
  staticIP: boolean;
  ipAddress: string;
  subnet: string;
  gateway: string;
  dns1: string;
  dns2: string;
}

interface PrinterSettings {
  receiptPrinter: string;
  kitchenPrinter: string;
  barPrinter: string;
  receiptWidth: '58mm' | '80mm';
  printLogo: boolean;
  printFooter: boolean;
  autoCut: boolean;
  buzzer: boolean;
}

export default function POSSettingsPage() {
  const t = useTranslations('settings');
  const params = useParams();
  const { slugShop, slugBranch } = params;

  // State
  const [loading, setLoading] = useState(false);
  const [devices, setDevices] = useState<POSDevice[]>([]);
  const [isAddDeviceDialogOpen, setIsAddDeviceDialogOpen] = useState(false);
  const [networkSettings, setNetworkSettings] = useState<NetworkSettings>({
    wifiSSID: 'Restaurant_WiFi',
    wifiPassword: '',
    staticIP: false,
    ipAddress: '*************',
    subnet: '*************',
    gateway: '***********',
    dns1: '*******',
    dns2: '*******',
  });
  const [printerSettings, setPrinterSettings] = useState<PrinterSettings>({
    receiptPrinter: '',
    kitchenPrinter: '',
    barPrinter: '',
    receiptWidth: '80mm',
    printLogo: true,
    printFooter: true,
    autoCut: true,
    buzzer: false,
  });

  // Mock data
  const mockDevices: POSDevice[] = [
    {
      id: '1',
      name: 'Main POS Terminal',
      type: 'terminal',
      brand: 'Square',
      model: 'Terminal',
      status: 'online',
      connectionType: 'wifi',
      ipAddress: '***********01',
      macAddress: '00:1B:44:11:3A:B7',
      lastSeen: new Date(Date.now() - 5 * 60 * 1000),
      location: 'Front Counter',
    },
    {
      id: '2',
      name: 'Receipt Printer',
      type: 'printer',
      brand: 'Epson',
      model: 'TM-T88VI',
      status: 'online',
      connectionType: 'ethernet',
      ipAddress: '*************',
      macAddress: '00:22:58:3C:2A:F1',
      lastSeen: new Date(Date.now() - 2 * 60 * 1000),
      location: 'Front Counter',
    },
    {
      id: '3',
      name: 'Kitchen Printer',
      type: 'printer',
      brand: 'Star',
      model: 'TSP143III',
      status: 'online',
      connectionType: 'wifi',
      ipAddress: '*************',
      macAddress: '00:11:62:2F:1A:C3',
      lastSeen: new Date(Date.now() - 1 * 60 * 1000),
      location: 'Kitchen',
    },
    {
      id: '4',
      name: 'Handheld Scanner',
      type: 'scanner',
      brand: 'Zebra',
      model: 'DS2208',
      status: 'offline',
      connectionType: 'usb',
      lastSeen: new Date(Date.now() - 30 * 60 * 1000),
      location: 'Storage',
    },
    {
      id: '5',
      name: 'Customer Display',
      type: 'display',
      brand: 'Elo',
      model: 'EloPOS',
      status: 'error',
      connectionType: 'usb',
      lastSeen: new Date(Date.now() - 60 * 60 * 1000),
      location: 'Front Counter',
    },
  ];

  // Load data
  useEffect(() => {
    const timer = setTimeout(() => {
      setDevices(mockDevices);
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Get status badge
  const getStatusBadge = (status: POSDevice['status']) => {
    const statusConfig = {
      online: { color: 'bg-green-100 text-green-800', icon: CheckCircle, label: 'Online' },
      offline: { color: 'bg-gray-100 text-gray-800', icon: XCircle, label: 'Offline' },
      error: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'Error' },
      maintenance: { color: 'bg-yellow-100 text-yellow-800', icon: Settings, label: 'Maintenance' },
    };

    const config = statusConfig[status];
    const Icon = config.icon;

    return (
      <Badge className={cn('flex items-center gap-1', config.color)}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    );
  };

  // Get device icon
  const getDeviceIcon = (type: POSDevice['type']) => {
    const iconMap = {
      terminal: Monitor,
      printer: Printer,
      scanner: QrCode,
      display: Tablet,
      cash_drawer: CreditCard,
      scale: Settings,
    };

    const Icon = iconMap[type] || Settings;
    return <Icon className="w-5 h-5" />;
  };

  // Get connection icon
  const getConnectionIcon = (type: POSDevice['connectionType']) => {
    const iconMap = {
      wifi: Wifi,
      ethernet: Network,
      bluetooth: Bluetooth,
      usb: Usb,
      serial: Settings,
    };

    const Icon = iconMap[type] || Settings;
    return <Icon className="w-4 h-4" />;
  };

  // Format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    setLoading(true);
    try {
      // TODO: Implement API call to save POS settings
      console.log('Saving POS settings:', { networkSettings, printerSettings });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      alert('POS settings saved successfully!');
    } catch (error) {
      console.error('Error saving settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Test device connection
  const testDeviceConnection = async (deviceId: string) => {
    try {
      // TODO: Implement device connection test
      console.log('Testing device connection:', deviceId);
      
      // Simulate test
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      alert('Device connection test successful!');
    } catch (error) {
      console.error('Error testing device:', error);
      alert('Device connection test failed.');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
            <Button variant="outline" size="sm">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Settings
            </Button>
          </Link>
          <div className="flex items-center gap-3">
            <Printer className="w-8 h-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">POS & Hardware</h1>
              <p className="text-gray-600">Manage point-of-sale systems and hardware devices</p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Scan Devices
          </Button>
          <Button onClick={handleSaveSettings} disabled={loading}>
            <Save className="w-4 h-4 mr-2" />
            {loading ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {/* Device Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Devices</CardTitle>
            <Monitor className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{devices.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Online</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {devices.filter(d => d.status === 'online').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Offline</CardTitle>
            <XCircle className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {devices.filter(d => d.status === 'offline').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Errors</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {devices.filter(d => d.status === 'error').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Connected Devices */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Connected Devices</CardTitle>
              <p className="text-sm text-gray-600">Manage your POS hardware devices</p>
            </div>
            <Dialog open={isAddDeviceDialogOpen} onOpenChange={setIsAddDeviceDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  Add Device
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add New Device</DialogTitle>
                  <DialogDescription>
                    Connect a new hardware device to your POS system
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="deviceName">Device Name</Label>
                    <Input
                      id="deviceName"
                      placeholder="Enter device name"
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="deviceType">Device Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="terminal">POS Terminal</SelectItem>
                          <SelectItem value="printer">Printer</SelectItem>
                          <SelectItem value="scanner">Scanner</SelectItem>
                          <SelectItem value="display">Customer Display</SelectItem>
                          <SelectItem value="cash_drawer">Cash Drawer</SelectItem>
                          <SelectItem value="scale">Scale</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="connectionType">Connection</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select connection" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="wifi">WiFi</SelectItem>
                          <SelectItem value="ethernet">Ethernet</SelectItem>
                          <SelectItem value="bluetooth">Bluetooth</SelectItem>
                          <SelectItem value="usb">USB</SelectItem>
                          <SelectItem value="serial">Serial</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="ipAddress">IP Address (if applicable)</Label>
                    <Input
                      id="ipAddress"
                      placeholder="*************"
                    />
                  </div>
                </div>

                <DialogFooter>
                  <Button variant="outline" onClick={() => setIsAddDeviceDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button>
                    Add Device
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Device</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Connection</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Seen</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {devices.map((device) => (
                <TableRow key={device.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getDeviceIcon(device.type)}
                      </div>
                      <div>
                        <div className="font-medium">{device.name}</div>
                        <div className="text-sm text-gray-500">
                          {device.brand} {device.model}
                        </div>
                        <div className="text-xs text-gray-400">{device.location}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {device.type.replace('_', ' ')}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      {getConnectionIcon(device.connectionType)}
                      <span className="text-sm capitalize">{device.connectionType}</span>
                      {device.ipAddress && (
                        <div className="text-xs text-gray-500 font-mono">
                          {device.ipAddress}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{getStatusBadge(device.status)}</TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatTimeAgo(device.lastSeen)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => testDeviceConnection(device.id)}
                      >
                        Test
                      </Button>
                      <Button variant="outline" size="sm">
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Network Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wifi className="w-5 h-5" />
            Network Settings
          </CardTitle>
          <p className="text-sm text-gray-600">Configure network connectivity for your devices</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="wifiSSID">WiFi Network Name (SSID)</Label>
              <Input
                id="wifiSSID"
                value={networkSettings.wifiSSID}
                onChange={(e) => setNetworkSettings({ ...networkSettings, wifiSSID: e.target.value })}
                placeholder="Restaurant_WiFi"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="wifiPassword">WiFi Password</Label>
              <Input
                id="wifiPassword"
                type="password"
                value={networkSettings.wifiPassword}
                onChange={(e) => setNetworkSettings({ ...networkSettings, wifiPassword: e.target.value })}
                placeholder="Enter WiFi password"
              />
            </div>
          </div>

          <Separator />

          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Use Static IP Address</h4>
              <p className="text-sm text-gray-600">
                Configure static IP addresses for devices
              </p>
            </div>
            <Switch
              checked={networkSettings.staticIP}
              onCheckedChange={(checked) => setNetworkSettings({ ...networkSettings, staticIP: checked })}
            />
          </div>

          {networkSettings.staticIP && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="ipAddress">IP Address</Label>
                <Input
                  id="ipAddress"
                  value={networkSettings.ipAddress}
                  onChange={(e) => setNetworkSettings({ ...networkSettings, ipAddress: e.target.value })}
                  placeholder="*************"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="subnet">Subnet Mask</Label>
                <Input
                  id="subnet"
                  value={networkSettings.subnet}
                  onChange={(e) => setNetworkSettings({ ...networkSettings, subnet: e.target.value })}
                  placeholder="*************"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gateway">Gateway</Label>
                <Input
                  id="gateway"
                  value={networkSettings.gateway}
                  onChange={(e) => setNetworkSettings({ ...networkSettings, gateway: e.target.value })}
                  placeholder="***********"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dns1">Primary DNS</Label>
                <Input
                  id="dns1"
                  value={networkSettings.dns1}
                  onChange={(e) => setNetworkSettings({ ...networkSettings, dns1: e.target.value })}
                  placeholder="*******"
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Printer Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Printer className="w-5 h-5" />
            Printer Configuration
          </CardTitle>
          <p className="text-sm text-gray-600">Configure receipt and kitchen printers</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="receiptPrinter">Receipt Printer</Label>
              <Select value={printerSettings.receiptPrinter} onValueChange={(value) => setPrinterSettings({ ...printerSettings, receiptPrinter: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select printer" />
                </SelectTrigger>
                <SelectContent>
                  {devices.filter(d => d.type === 'printer').map((printer) => (
                    <SelectItem key={printer.id} value={printer.id}>
                      {printer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="kitchenPrinter">Kitchen Printer</Label>
              <Select value={printerSettings.kitchenPrinter} onValueChange={(value) => setPrinterSettings({ ...printerSettings, kitchenPrinter: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select printer" />
                </SelectTrigger>
                <SelectContent>
                  {devices.filter(d => d.type === 'printer').map((printer) => (
                    <SelectItem key={printer.id} value={printer.id}>
                      {printer.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="receiptWidth">Receipt Width</Label>
              <Select value={printerSettings.receiptWidth} onValueChange={(value: '58mm' | '80mm') => setPrinterSettings({ ...printerSettings, receiptWidth: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select width" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="58mm">58mm</SelectItem>
                  <SelectItem value="80mm">80mm</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Print Logo</h4>
                  <p className="text-sm text-gray-600">Include restaurant logo on receipts</p>
                </div>
                <Switch
                  checked={printerSettings.printLogo}
                  onCheckedChange={(checked) => setPrinterSettings({ ...printerSettings, printLogo: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Print Footer</h4>
                  <p className="text-sm text-gray-600">Include footer message on receipts</p>
                </div>
                <Switch
                  checked={printerSettings.printFooter}
                  onCheckedChange={(checked) => setPrinterSettings({ ...printerSettings, printFooter: checked })}
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Auto Cut</h4>
                  <p className="text-sm text-gray-600">Automatically cut receipts after printing</p>
                </div>
                <Switch
                  checked={printerSettings.autoCut}
                  onCheckedChange={(checked) => setPrinterSettings({ ...printerSettings, autoCut: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Buzzer Alert</h4>
                  <p className="text-sm text-gray-600">Sound alert when printing</p>
                </div>
                <Switch
                  checked={printerSettings.buzzer}
                  onCheckedChange={(checked) => setPrinterSettings({ ...printerSettings, buzzer: checked })}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
