'use client';

import React, { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Camera, Mail, Phone, User, MapPin, Shield, Key } from 'lucide-react';
import { AppLoading } from '@/components/ui/app-loading';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Switch } from '@/components/ui/switch';
import { useGetShopBySlugQuery, useGetBranchBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetStaffMemberBySlugQuery, useUpdateStaffMemberMutation } from '@/lib/redux/api/endpoints/restaurant/staffApi';
import { useAuth } from '@/hooks/useAuth';
import type { Shop, Branch } from '@/lib/types/shop';

interface ProfileSettingsPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}



export default function ProfileSettingsPage({ params }: ProfileSettingsPageProps) {
  const { slugShop, slugBranch } = React.use(params);

  // Get current user from auth
  const { user: currentUser, isLoading: isAuthLoading } = useAuth();

  // Get shop and branch data from backend using slug-based queries
  const { data: shopData, isLoading: isShopLoading, error: shopError } = useGetShopBySlugQuery(slugShop) as {
    data: Shop | undefined;
    isLoading: boolean;
    error: unknown;
  };
  const { data: branchData, isLoading: isBranchLoading, error: branchError } = useGetBranchBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  }) as {
    data: Branch | undefined;
    isLoading: boolean;
    error: unknown;
  };

  // Get current user profile data from staff API (assuming the user is a staff member)
  const { data: staffData, isLoading: isStaffLoading, error: staffError } = useGetStaffMemberBySlugQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    slug: currentUser?.email?.split('@')[0] || 'current-user' // Use email prefix as slug fallback
  }, {
    skip: !currentUser?.email || !shopData?.id || !branchData?.id
  });

  // Update staff member mutation
  const [updateStaffMember, { isLoading: isUpdating }] = useUpdateStaffMemberMutation();

  const [activeTab, setActiveTab] = useState('personal');
  const [userData, setUserData] = useState({
    id: '',
    name: '',
    email: '',
    phone: '',
    role: '',
    avatar: '',
    bio: '',
    address: '',
    language: 'en',
    timezone: 'Asia/Bangkok',
    twoFactorEnabled: false,
    emailNotifications: true,
    pushNotifications: true,
    lastLogin: '',
    createdAt: '',
  });

  const isLoading = isAuthLoading || isShopLoading || isBranchLoading || isStaffLoading;
  const hasError = shopError || branchError || staffError;

  // Load user data from auth and staff API
  useEffect(() => {
    if (currentUser) {
      setUserData(prev => ({
        ...prev,
        id: currentUser.id,
        name: currentUser.name || '',
        email: currentUser.email || '',
        role: currentUser.role || 'User',
      }));
    }
  }, [currentUser]);

  // Load staff data when available
  useEffect(() => {
    if (staffData) {
      setUserData(prev => ({
        ...prev,
        id: staffData.id,
        name: `${staffData.firstName} ${staffData.lastName}`,
        email: staffData.email,
        phone: staffData.phone || '',
        role: staffData.roleName || staffData.position || 'Staff',
        avatar: staffData.avatar || '',
        address: staffData.address ? `${staffData.address.street}, ${staffData.address.city}, ${staffData.address.state}` : '',
        createdAt: staffData.createdAt,
      }));
    }
  }, [staffData]);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError || !shopData || !branchData) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
          <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  const handleSave = async () => {
    if (!staffData?.id) {
      toast.error('Unable to save: Staff profile not found');
      return;
    }

    try {
      // Prepare update data
      const updateData = {
        id: staffData.id,
        firstName: userData.name.split(' ')[0] || '',
        lastName: userData.name.split(' ').slice(1).join(' ') || '',
        email: userData.email,
        phone: userData.phone,
        // Note: Other fields like role, position, etc. might need admin privileges to update
      };

      await updateStaffMember({
        shopSlug: slugShop,
        branchSlug: slugBranch,
        staffData: updateData,
      }).unwrap();

      toast.success('Profile settings saved successfully');
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error('Failed to save profile settings. Please try again.');
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setUserData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAvatarChange = () => {
    // Create a file input element
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        // For now, just show a preview using FileReader
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          setUserData(prev => ({
            ...prev,
            avatar: result
          }));
          toast.success('Avatar updated! Click Save Changes to persist.');
        };
        reader.readAsDataURL(file);

        // TODO: Implement actual file upload to backend
        // This would typically involve uploading to a cloud storage service
        // and then updating the staff member's avatar_url field
      }
    };
    input.click();
  };

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}>
          <Button variant="outline">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Settings
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div className="flex min-w-72 flex-col gap-3">
          <h1 className="text-foreground text-[32px] font-bold leading-tight">Profile Settings</h1>
          <p className="text-muted-foreground text-sm">Manage your personal information and account settings</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <Card>
            <CardContent className="p-6">
              <div className="flex flex-col items-center">
                <div className="relative mb-4">
                  <Avatar className="w-32 h-32 border-4 border-border">
                    <AvatarImage src={userData.avatar} alt={userData.name} />
                    <AvatarFallback className="bg-secondary text-secondary-foreground text-2xl">
                      {userData.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                  <button
                    className="absolute bottom-0 right-0 bg-secondary text-secondary-foreground rounded-full p-2 shadow-md hover:bg-secondary/80 transition-colors"
                    onClick={handleAvatarChange}
                  >
                    <Camera className="h-4 w-4" />
                  </button>
                </div>
                <h2 className="text-foreground text-xl font-bold mb-1">{userData.name}</h2>
                <p className="text-muted-foreground text-sm mb-4">{userData.role}</p>
                <div className="w-full space-y-2 text-sm">
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Mail className="h-4 w-4" />
                    <span>{userData.email}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <Phone className="h-4 w-4" />
                    <span>{userData.phone}</span>
                  </div>
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>{userData.address}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="lg:col-span-3">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-6">
              <TabsTrigger value="personal">
                <User className="h-4 w-4 mr-2" />
                Personal Info
              </TabsTrigger>
              <TabsTrigger value="security">
                <Shield className="h-4 w-4 mr-2" />
                Security
              </TabsTrigger>
              <TabsTrigger value="preferences">
                <Key className="h-4 w-4 mr-2" />
                Preferences
              </TabsTrigger>
            </TabsList>

            <TabsContent value="personal" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Personal Information</CardTitle>
                  <CardDescription>Update your personal details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={userData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={userData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={userData.phone}
                        onChange={(e) => handleInputChange('phone', e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Input
                        id="role"
                        value={userData.role}
                        onChange={(e) => handleInputChange('role', e.target.value)}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Input
                      id="address"
                      value={userData.address}
                      onChange={(e) => handleInputChange('address', e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      value={userData.bio}
                      onChange={(e) => handleInputChange('bio', e.target.value)}
                      className="min-h-[100px]"
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    onClick={handleSave}
                    disabled={isUpdating}
                  >
                    {isUpdating ? 'Saving...' : 'Save Changes'}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Security Settings</CardTitle>
                  <CardDescription>Manage your account security</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-foreground font-medium">Change Password</h3>
                        <p className="text-muted-foreground text-sm">Update your account password</p>
                      </div>
                      <Button variant="outline">
                        Change Password
                      </Button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-foreground font-medium">Two-Factor Authentication</h3>
                        <p className="text-muted-foreground text-sm">Add an extra layer of security to your account</p>
                      </div>
                      <Switch
                        checked={userData.twoFactorEnabled}
                        onCheckedChange={(checked) => handleInputChange('twoFactorEnabled', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-foreground font-medium">Login History</h3>
                        <p className="text-muted-foreground text-sm">View your recent login activity</p>
                      </div>
                      <Button variant="outline">
                        View History
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="preferences" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>User Preferences</CardTitle>
                  <CardDescription>Customize your account experience</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="language">Language</Label>
                      <select
                        id="language"
                        value={userData.language}
                        onChange={(e) => handleInputChange('language', e.target.value)}
                        className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm text-foreground"
                      >
                        <option value="en">English</option>
                        <option value="th">Thai</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                      </select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="timezone">Timezone</Label>
                      <select
                        id="timezone"
                        value={userData.timezone}
                        onChange={(e) => handleInputChange('timezone', e.target.value)}
                        className="w-full rounded-md border border-border bg-background px-3 py-2 text-sm text-foreground"
                      >
                        <option value="America/Los_Angeles">Pacific Time (US & Canada)</option>
                        <option value="America/New_York">Eastern Time (US & Canada)</option>
                        <option value="Asia/Bangkok">Bangkok</option>
                        <option value="Europe/London">London</option>
                        <option value="Asia/Tokyo">Tokyo</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-4 pt-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-foreground font-medium">Email Notifications</h3>
                        <p className="text-muted-foreground text-sm">Receive email updates about your account</p>
                      </div>
                      <Switch
                        checked={userData.emailNotifications}
                        onCheckedChange={(checked) => handleInputChange('emailNotifications', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-foreground font-medium">Push Notifications</h3>
                        <p className="text-muted-foreground text-sm">Receive push notifications on your devices</p>
                      </div>
                      <Switch
                        checked={userData.pushNotifications}
                        onCheckedChange={(checked) => handleInputChange('pushNotifications', checked)}
                      />
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    onClick={handleSave}
                    disabled={isUpdating}
                  >
                    {isUpdating ? 'Saving...' : 'Save Preferences'}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}
