'use client';

import React from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useGetReservationsQuery, Reservation } from '@/lib/redux/api/endpoints/restaurant/reservationsApi';
import { AppLoading } from '@/components/ui/app-loading';

interface ReservationsTabProps {
  slugShop: string;
  slugBranch: string;
}

export function ReservationsTab({ slugShop, slugBranch }: ReservationsTabProps) {
  // Get upcoming reservations data from backend using real API
  // Filter by startDate to get reservations from today onwards (upcoming)
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

  const {
    data: reservationsResponse,
    isLoading: isLoadingReservations,
    error: reservationsError
  } = useGetReservationsQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch,
    filters: {
      startDate: today, // Get reservations from today onwards
      page: 1,
      limit: 20
    }
  });

  if (isLoadingReservations) {
    return (
      <div className="flex justify-center py-12">
        <AppLoading type="restaurant" size="md" />
      </div>
    );
  }

  if (reservationsError) {
    return (
      <div className="text-center py-12">
        <p className="text-destructive mb-2">Error loading reservations</p>
        <p className="text-muted-foreground text-sm">Please try again later</p>
      </div>
    );
  }

  const reservations = reservationsResponse?.data || [];

  const formatTime = (timeString: string) => {
    try {
      // If it's already in HH:MM format, just return it
      if (/^\d{2}:\d{2}$/.test(timeString)) {
        return timeString;
      }

      // Try to parse as a date/time string
      const date = new Date(timeString);
      if (!isNaN(date.getTime())) {
        return date.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        });
      }

      // Fallback: return the original string
      return timeString;
    } catch {
      return timeString;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      });
    } catch {
      return dateString;
    }
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'pending':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'completed':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      case 'no-show':
        return 'bg-muted text-muted-foreground border-border';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-foreground">Upcoming Reservations</h3>
        <div className="flex gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/tables/layout-editor`}>
            <Button variant="outline">
              Edit Layout
            </Button>
          </Link>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/new`}>
            <Button>
              Add Reservation
            </Button>
          </Link>
        </div>
      </div>

      <div className="bg-card border border-border rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-muted">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Time</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Party Size</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Table</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-card divide-y divide-border">
            {reservations && reservations.length > 0 ? (
              reservations.map((reservation: Reservation) => (
                <tr key={reservation.id} className="hover:bg-muted/50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {formatDate(reservation.date)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {formatTime(reservation.time)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-foreground">
                    {reservation.customerName}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {reservation.partySize}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-foreground">
                    {reservation.tableName || `Table ${reservation.tableId}`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusVariant(reservation.status)}`}>
                      {reservation.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                    <Link
                      href={`/app/restaurant/${slugShop}/${slugBranch}/reservations/${reservation.slug}`}
                      className="text-muted-foreground hover:text-foreground font-medium"
                    >
                      View
                    </Link>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-muted-foreground">
                  No reservations found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination info */}
      {reservationsResponse?.pagination && (
        <div className="mt-4 flex justify-between items-center text-sm text-muted-foreground">
          <span>
            Showing {reservations.length} of {reservationsResponse.pagination.totalItems} reservations
          </span>
          {reservationsResponse.pagination.totalPages > 1 && (
            <span>
              Page {reservationsResponse.pagination.currentPage} of {reservationsResponse.pagination.totalPages}
            </span>
          )}
        </div>
      )}
    </div>
  );
}
