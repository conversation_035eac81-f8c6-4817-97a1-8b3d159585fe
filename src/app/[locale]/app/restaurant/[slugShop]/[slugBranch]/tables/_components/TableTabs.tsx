'use client';

import React from 'react';

interface TableTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function TableTabs({ activeTab, onTabChange }: TableTabsProps) {
  const tabs = [
    { id: 'floor-plan', label: 'Floor Plan' },
    { id: 'reservations', label: 'Reservations' },
    { id: 'waitlist', label: 'Waitlist' }
  ];

  return (
    <div className="border-b border-border mb-6">
      <nav className="flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === tab.id
                ? 'border-foreground text-foreground'
                : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
            }`}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </button>
        ))}
      </nav>
    </div>
  );
}
