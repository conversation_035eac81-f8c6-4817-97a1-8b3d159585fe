'use client';

import React from 'react';
import { LayoutGrid } from 'lucide-react';
import { Table as ApiTable } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { CanvasGrid } from './CanvasGrid';
import { TableItem } from './TableItem';

interface TableCanvasProps {
  tables: ApiTable[];
  selectedTable: ApiTable | null;
  localTablePositions: Record<string, { x: number; y: number }>;
  canvasRef: React.RefObject<HTMLDivElement | null>;
  onCanvasClick: () => void;
  onTableClick: (table: ApiTable, e: React.MouseEvent) => void;
  onTableDragEnd: (tableId: string, info: { offset: { x: number; y: number }; point: { x: number; y: number } }) => void;
  showGrid?: boolean;
  gridSize?: number;
  zoom?: number;
}

export const TableCanvas: React.FC<TableCanvasProps> = ({
  tables,
  selectedTable,
  localTablePositions,
  canvasRef,
  onCanvasClick,
  onTableClick,
  onTableDragEnd,
  showGrid = true,
  gridSize = 20,
  zoom = 1,
}) => {
  return (
    <div className="bg-[#fbfaf9] border-2 border-dashed border-[#e2dcd4] rounded-lg p-4 min-h-[400px] sm:min-h-[600px] relative overflow-hidden">
      <div
        ref={canvasRef}
        className="w-full h-full relative cursor-crosshair"
        onClick={onCanvasClick}
        style={{
          minHeight: '500px',
          transform: `scale(${zoom})`,
          transformOrigin: 'top left',
          transition: 'transform 0.2s ease-in-out'
        }}
      >
        {/* Grid background */}
        {showGrid && <CanvasGrid gridSize={gridSize} />}

        {/* Tables */}
        {tables.map((table) => (
          <TableItem
            key={table.id}
            table={table}
            isSelected={selectedTable?.id === table.id}
            localPosition={localTablePositions[table.id]}
            canvasRef={canvasRef}
            onTableClick={onTableClick}
            onDragEnd={onTableDragEnd}
          />
        ))}

        {/* Empty state */}
        {tables.length === 0 && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-[#8a745c]">
              <LayoutGrid className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No tables in this area</p>
              <p className="text-sm">Click &quot;Add Table&quot; to start designing your layout</p>
            </div>
          </div>
        )}

        {/* Canvas info overlay */}
        <div className="absolute top-2 left-2 bg-white/80 backdrop-blur-sm rounded px-2 py-1 text-xs text-[#8a745c]">
          {tables.length} table{tables.length !== 1 ? 's' : ''}
        </div>

        {/* Zoom indicator */}
        {zoom !== 1 && (
          <div className="absolute top-2 right-2 bg-white/80 backdrop-blur-sm rounded px-2 py-1 text-xs text-[#8a745c]">
            {Math.round(zoom * 100)}%
          </div>
        )}
      </div>
    </div>
  );
};
