'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Plus,
  Edit,
  Trash2,
  Grid3X3,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Settings
} from 'lucide-react';
import { Table as ApiTable } from '@/lib/redux/api/endpoints/restaurant/tablesApi';

interface TableControlsProps {
  selectedTable: ApiTable | null;
  hasUnsavedChanges: boolean;
  showGrid: boolean;
  zoom?: number;
  onAddTable: () => void;
  onEditTable: () => void;
  onDeleteTable: () => void;
  onToggleGrid: () => void;
  onResetLayout: () => void;
  onZoomIn: () => void;
  onZoomOut: () => void;
}

export const TableControls: React.FC<TableControlsProps> = ({
  selectedTable,
  hasUnsavedChanges,
  showGrid,
  zoom = 1,
  onAddTable,
  onEditTable,
  onDeleteTable,
  onToggleGrid,
  onResetLayout,
  onZoomIn,
  onZoomOut,
}) => {
  return (
    <div className="space-y-4">
      {/* Add Table Button */}
      <Button
        onClick={onAddTable}
        className="w-full bg-[#e5ccb2] text-[#181510] hover:bg-[#d6bd9e]"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add Table
      </Button>

      {/* Canvas Controls */}
      <div className="space-y-2">
        <h3 className="text-sm font-medium text-[#181510]">Canvas Controls</h3>
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onToggleGrid}
            className={`border-[#e2dcd4] text-[#181510] ${showGrid ? 'bg-[#e5ccb2]' : ''}`}
          >
            <Grid3X3 className="h-3 w-3 mr-1" />
            Grid
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onResetLayout}
            className="border-[#e2dcd4] text-[#181510]"
          >
            <RotateCcw className="h-3 w-3 mr-1" />
            Reset
          </Button>
        </div>
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={onZoomIn}
            disabled={zoom >= 2}
            className="border-[#e2dcd4] text-[#181510] disabled:opacity-50"
          >
            <ZoomIn className="h-3 w-3 mr-1" />
            Zoom+
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={onZoomOut}
            disabled={zoom <= 0.5}
            className="border-[#e2dcd4] text-[#181510] disabled:opacity-50"
          >
            <ZoomOut className="h-3 w-3 mr-1" />
            Zoom-
          </Button>
        </div>
        {zoom !== 1 && (
          <div className="text-center text-xs text-[#8a745c]">
            Zoom: {Math.round(zoom * 100)}%
          </div>
        )}
      </div>

      {/* Selected Table Info */}
      {selectedTable && (
        <div className="bg-[#fbfaf9] border border-[#e2dcd4] rounded-lg p-4">
          <h3 className="text-sm font-medium text-[#181510] mb-3">
            Table {selectedTable.number}
          </h3>
          <div className="space-y-2 text-sm text-[#8a745c]">
            <div>Capacity: {selectedTable.capacity} people</div>
            <div>Shape: {selectedTable.shape}</div>
            <div>Status: {selectedTable.status}</div>
            <div>Area: {selectedTable.area?.name || 'No Area'}</div>
          </div>
          <div className="flex gap-2 mt-4">
            <Button
              size="sm"
              variant="outline"
              onClick={onEditTable}
              className="flex-1 border-[#e2dcd4] text-[#181510]"
            >
              <Edit className="h-3 w-3 mr-1" />
              Edit
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={onDeleteTable}
              className="border-red-300 text-red-600 hover:bg-red-50"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      )}

      {/* Layout Status */}
      <div className="bg-[#fbfaf9] border border-[#e2dcd4] rounded-lg p-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-[#8a745c]">Layout Status</span>
          <span className={`font-medium ${
            hasUnsavedChanges ? 'text-orange-600' : 'text-green-600'
          }`}>
            {hasUnsavedChanges ? 'Modified' : 'Saved'}
          </span>
        </div>
      </div>
    </div>
  );
};
