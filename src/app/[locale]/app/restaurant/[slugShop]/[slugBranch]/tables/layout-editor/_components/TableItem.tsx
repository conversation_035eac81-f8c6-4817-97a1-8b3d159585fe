'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Table as ApiTable } from '@/lib/redux/api/endpoints/restaurant/tablesApi';

interface TableItemProps {
  table: ApiTable;
  isSelected: boolean;
  localPosition?: { x: number; y: number };
  canvasRef: React.RefObject<HTMLDivElement | null>;
  onTableClick: (table: ApiTable, e: React.MouseEvent) => void;
  onDragEnd: (tableId: string, info: { offset: { x: number; y: number }; point: { x: number; y: number } }) => void;
}

export const TableItem: React.FC<TableItemProps> = ({
  table,
  isSelected,
  localPosition,
  canvasRef,
  onTableClick,
  onDragEnd,
}) => {
  // Use local position if available, otherwise use server position
  const x = localPosition?.x ?? table.position?.x ?? 100;
  const y = localPosition?.y ?? table.position?.y ?? 100;
  const width = table.position?.width || 80;
  const height = table.position?.height || 80;

  // console.log(`Table ${table.number} - Local: ${localPosition ? `${localPosition.x}, ${localPosition.y}` : 'none'}, Server: ${table.position?.x}, ${table.position?.y}, Final: ${x}, ${y}`);

  // Calculate drag constraints to keep table within canvas
  const getDragConstraints = () => {
    if (!canvasRef.current) return { left: 0, right: 0, top: 0, bottom: 0 };

    const canvas = canvasRef.current;
    const canvasRect = canvas.getBoundingClientRect();

    return {
      left: 0,
      right: Math.max(0, canvasRect.width - width),
      top: 0,
      bottom: Math.max(0, canvasRect.height - height),
    };
  };

  return (
    <motion.div
      key={table.id}
      drag
      dragMomentum={false}
      dragElastic={0}
      dragConstraints={getDragConstraints()}
      style={{
        x,
        y,
        width,
        height,
      }}
      onDragEnd={(_, info) => {
        console.log('Drag info:', info);
        // Calculate the new position based on the current position + offset
        const newX = x + info.offset.x;
        const newY = y + info.offset.y;

        console.log('Current position:', x, y);
        console.log('Offset:', info.offset.x, info.offset.y);
        console.log('New position:', newX, newY);

        // Ensure the position stays within bounds
        const constraints = getDragConstraints();
        const boundedX = Math.max(constraints.left, Math.min(newX, constraints.right));
        const boundedY = Math.max(constraints.top, Math.min(newY, constraints.bottom));

        console.log('Bounded position:', boundedX, boundedY);

        onDragEnd(table.id, {
          offset: info.offset,
          point: { x: boundedX, y: boundedY }
        });
      }}
      className={`absolute cursor-move select-none ${
        isSelected
          ? 'ring-2 ring-[#8a745c] ring-offset-2 z-20'
          : 'hover:ring-1 hover:ring-[#8a745c] hover:ring-offset-1 z-10'
      }`}
      onClick={(e) => {
        e.stopPropagation();
        onTableClick(table, e);
      }}
      whileHover={{ scale: 1.02 }}
      whileDrag={{
        scale: 1.05,
        zIndex: 1000,
        boxShadow: '0 10px 25px rgba(0,0,0,0.15)'
      }}
    >
      {/* Table shape */}
      <div
        className={`w-full h-full bg-[#e5ccb2] border-2 border-[#8a745c] flex items-center justify-center text-[#181510] font-medium text-sm shadow-sm ${
          table.shape === 'round'
            ? 'rounded-full'
            : table.shape === 'rectangle'
            ? 'rounded-lg'
            : 'rounded-md'
        }`}
      >
        <div className="text-center pointer-events-none">
          <div className="font-bold text-sm">{table.number}</div>
          <div className="text-xs opacity-75">{table.capacity}p</div>
        </div>
      </div>

      {/* Table status indicator */}
      <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border border-white ${
        table.status === 'available' ? 'bg-green-500' :
        table.status === 'occupied' ? 'bg-red-500' :
        table.status === 'reserved' ? 'bg-yellow-500' :
        table.status === 'cleaning' ? 'bg-blue-500' :
        'bg-gray-500'
      }`} />
    </motion.div>
  );
};
