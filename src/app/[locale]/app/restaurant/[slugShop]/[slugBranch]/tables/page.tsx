'use client';

import React, { useState } from 'react';
import { AppLoading } from '@/components/ui/app-loading';

import { useGetBranchWithShopQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { useGetTablesQuery, useGetTableAreasQuery } from '@/lib/redux/api/endpoints/restaurant/tablesApi';

// Import components
import { TableHeader } from './_components/TableHeader';
import { TableTabs } from './_components/TableTabs';
import { FloorPlanTab } from './_components/FloorPlanTab';
import { ReservationsTab } from './_components/ReservationsTab';
import { WaitlistTab } from './_components/WaitlistTab';

interface TablesPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function TablesPage({ params }: TablesPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [activeTab, setActiveTab] = useState('floor-plan');

  // Get branch data directly by slug (includes shop data)
  const { data: branchData, isLoading: isLoadingShop, error: shopError } = useGetBranchWithShopQuery({
    shopSlug: slugShop,
    branchSlug: slugBranch
  });

  // Extract shop and branch from the response
  const shop = branchData?.shop;
  const branch = branchData;

  // Get tables data from backend
  const {
    data: tablesData,
    isLoading: isLoadingTables,
    error: tablesError
  } = useGetTablesQuery(
    {
      shopId: shop?.id || '',
      branchId: branch?.id || ''
    },
    {
      skip: !shop?.id || !branch?.id
    }
  );

  // Get table areas from backend
  const {
    data: areasData,
    isLoading: isLoadingAreas,
    error: areasError
  } = useGetTableAreasQuery(
    {
      shopId: shop?.id || '',
      branchId: branch?.id || ''
    },
    {
      skip: !shop?.id || !branch?.id
    }
  );

  const isLoading = isLoadingShop || isLoadingTables || isLoadingAreas;
  const hasError = shopError || tablesError || areasError;

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (hasError) {
    return (
      <div className="text-center py-12">
        <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Error Loading Data</h1>
        <p className="text-muted-foreground text-sm">There was an error loading the table data. Please try again.</p>
      </div>
    );
  }

  if (!shop || !branch) {
    return (
      <div className="text-center py-12">
        <h1 className="text-foreground text-[32px] font-bold leading-tight mb-2">Restaurant Not Found</h1>
        <p className="text-muted-foreground text-sm">The restaurant or branch you are looking for does not exist.</p>
      </div>
    );
  }

  // Use real data from backend or fallback to empty arrays
  // The Redux API transformResponse extracts data from paginated response
  const tables = tablesData || [];
  const areas = areasData || [];

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <TableHeader />

      {/* Main Content */}
      <div className="p-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-foreground mb-2">Table Layout</h2>
          <p className="text-muted-foreground">Manage your restaurant&apos;s table layout and reservations.</p>
        </div>

        {/* Tabs */}
        <TableTabs activeTab={activeTab} onTabChange={setActiveTab} />

        {activeTab === 'floor-plan' && (
          <FloorPlanTab
            tables={tables}
            areas={areas}
            slugShop={slugShop}
            slugBranch={slugBranch}
          />
        )}

        {activeTab === 'reservations' && (
          <ReservationsTab slugShop={slugShop} slugBranch={slugBranch} />
        )}

        {activeTab === 'waitlist' && (
          <WaitlistTab />
        )}
      </div>
    </div>
  );
}
