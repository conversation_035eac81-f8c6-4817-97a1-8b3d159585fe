'use client';

import React from 'react';
import { use } from 'react';
import { Link } from '@/i18n/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  ArrowLeft,
  Settings,
  MapPin,
  Phone,
  Mail,
  Users,
  Calendar,
  TrendingUp,
  Store,
  Plus,
  ArrowRight
} from 'lucide-react';
import { MESSAGES } from '@/lib/constants/messages';
import { useGetShopBySlugQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { AppLoading } from '@/components/ui/app-loading';
import { ImageWithFallback } from '@/components/ui/image-with-fallback';

interface RestaurantPageProps {
  params: Promise<{
    slugShop: string;
  }>;
}

// Helper function to format address
const formatAddress = (address: string | { street?: string; city?: string; state?: string; zip_code?: string; country?: string } | undefined): string => {
  if (typeof address === 'string') {
    return address;
  }
  if (address && typeof address === 'object') {
    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.state) parts.push(address.state);
    if (address.zip_code) parts.push(address.zip_code);
    if (address.country) parts.push(address.country);
    return parts.join(', ');
  }
  return '';
};

export default function RestaurantPage({ params }: RestaurantPageProps) {
  const { slugShop } = use(params);

  // Fetch restaurant data by slug
  const { data: restaurantData, isLoading, isError } = useGetShopBySlugQuery(slugShop);

  if (isLoading) {
    return <AppLoading />;
  }

  if (isError || !restaurantData) {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-7xl mx-auto">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-foreground mb-4">Restaurant Not Found</h1>
            <p className="text-muted-foreground mb-6">
              The restaurant you are looking for does not exist or has been removed.
            </p>
            <Link href="/app/restaurant">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Restaurants
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/app/restaurant">
              <Button
                variant="ghost"
                size="sm"
                className="text-muted-foreground hover:text-foreground"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {MESSAGES.ACTION.BACK}
              </Button>
            </Link>

            <div className="h-6 w-px bg-border" />

            <nav className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link href="/app/restaurant" className="hover:text-foreground">
                Restaurants
              </Link>
              <span>/</span>
              <span className="text-foreground font-medium">{restaurantData.name}</span>
            </nav>
          </div>

          {/* Restaurant Header Card */}
          <Card className="bg-card border-border">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-lg overflow-hidden bg-muted">
                    <ImageWithFallback
                      src={restaurantData.logo || ''}
                      alt={restaurantData.name}
                      className="w-full h-full object-cover"
                      fallbackIcon="🏪"
                      fallbackText="Restaurant"
                      containerClassName="w-full h-full"
                    />
                  </div>
                  <div>
                    <div className="flex items-center gap-3 mb-2">
                      <h1 className="text-2xl font-bold text-foreground">{restaurantData.name}</h1>
                      <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300">
                        {MESSAGES.STATUS.ACTIVE}
                      </Badge>
                    </div>
                    <p className="text-muted-foreground text-base max-w-2xl">
                      {restaurantData.description}
                    </p>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Link href={`/app/restaurant/${slugShop}/settings`}>
                    <Button variant="outline">
                      <Settings className="h-4 w-4 mr-2" />
                      {MESSAGES.NAVIGATION.SETTINGS}
                    </Button>
                  </Link>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <Card>
            <CardContent className="p-6 text-center">
              <Store className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">{restaurantData.branches?.length || 0}</div>
              <div className="text-sm text-muted-foreground">Branches</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Users className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">24</div>
              <div className="text-sm text-muted-foreground">Staff Members</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <TrendingUp className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">$12.5K</div>
              <div className="text-sm text-muted-foreground">Monthly Revenue</div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6 text-center">
              <Calendar className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold text-foreground">156</div>
              <div className="text-sm text-muted-foreground">Reservations</div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Restaurant Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-foreground flex items-center gap-2">
                  <Phone className="h-5 w-5 text-primary" />
                  Contact Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {restaurantData.address && (
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
                    <span className="text-foreground">
                      {formatAddress(restaurantData.address)}
                    </span>
                  </div>
                )}

                {restaurantData.phone && (
                  <div className="flex items-center gap-3">
                    <Phone className="h-4 w-4 text-primary flex-shrink-0" />
                    <span className="text-foreground">{restaurantData.phone}</span>
                  </div>
                )}

                {restaurantData.email && (
                  <div className="flex items-center gap-3">
                    <Mail className="h-4 w-4 text-primary flex-shrink-0" />
                    <span className="text-foreground">{restaurantData.email}</span>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Branches */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-foreground flex items-center gap-2">
                    <Store className="h-5 w-5 text-primary" />
                    Branches ({restaurantData.branches?.length || 0})
                  </CardTitle>
                  <Link href={`/app/restaurant/${slugShop}/new-branch`}>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Branch
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {restaurantData.branches?.map((branch) => (
                    <Link
                      key={branch.id}
                      href={`/app/restaurant/${slugShop}/${branch.slug}`}
                      className="flex items-center justify-between p-4 bg-muted rounded-lg hover:bg-accent transition-colors group"
                    >
                      <div>
                        <h4 className="font-medium text-foreground group-hover:text-primary">
                          {branch.name}
                        </h4>
                        <p className="text-sm text-muted-foreground">{formatAddress(branch.address)}</p>
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-foreground">Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-foreground">New reservation for 4 people</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-foreground">Menu item updated</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <span className="text-foreground">Staff member added</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
