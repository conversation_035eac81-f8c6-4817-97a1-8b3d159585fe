import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import RestaurantShopsPage from '../page'

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  })),
}))

// Mock useRestaurants hook
const mockUseRestaurants = {
  restaurants: [
    {
      id: 'restaurant-1',
      slug: 'test-restaurant-1',
      name: 'Test Restaurant 1',
      description: 'A great test restaurant',
      status: 'active',
      city: 'Test City',
      address: '123 Test St',
      phone: '+**********',
      email: '<EMAIL>',
      branches: [
        {
          id: 'branch-1',
          slug: 'main-branch',
          name: 'Main Branch',
          address: '123 Test St',
        },
      ],
    },
    {
      id: 'restaurant-2',
      slug: 'test-restaurant-2',
      name: 'Test Restaurant 2',
      description: 'Another great test restaurant',
      status: 'inactive',
      city: 'Test City 2',
      address: '456 Test Ave',
      phone: '+1234567891',
      email: '<EMAIL>',
      branches: [],
    },
  ],
  isLoading: false,
  totalCount: 2,
  statusCounts: { active: 1, inactive: 1, suspended: 0 },
  cityCounts: { 'Test City': 1, 'Test City 2': 1 },
  filters: { page: 1, limit: 12 },
  updateFilters: jest.fn(),
  deleteRestaurant: jest.fn(),
}

jest.mock('@/hooks/useRestaurant', () => ({
  useRestaurants: jest.fn(() => mockUseRestaurants),
}))

// Mock constants
jest.mock('@/lib/constants/messages', () => ({
  MESSAGES: {
    WARNING: {
      DELETE_CONFIRMATION: 'Are you sure you want to delete this restaurant?',
    },
  },
}))

// Mock components
jest.mock('@/components/ui/app-loading', () => ({
  AppLoading: () => <div data-testid="app-loading">Loading...</div>,
}))

jest.mock('@/components/restaurant/RestaurantList', () => ({
  RestaurantList: ({ 
    restaurants, 
    onRestaurantView, 
    onRestaurantEdit, 
    onRestaurantDelete, 
    onRestaurantCreate,
    ...props 
  }: any) => (
    <div data-testid="restaurant-list">
      <button onClick={onRestaurantCreate} data-testid="create-restaurant-btn">
        Create Restaurant
      </button>
      {restaurants.map((restaurant: any) => (
        <div key={restaurant.id} data-testid={`restaurant-${restaurant.id}`}>
          <h3>{restaurant.name}</h3>
          <p>{restaurant.description}</p>
          <button 
            onClick={() => onRestaurantView(restaurant)} 
            data-testid={`view-${restaurant.id}`}
          >
            View
          </button>
          <button 
            onClick={() => onRestaurantEdit(restaurant)} 
            data-testid={`edit-${restaurant.id}`}
          >
            Edit
          </button>
          <button 
            onClick={() => onRestaurantDelete(restaurant)} 
            data-testid={`delete-${restaurant.id}`}
          >
            Delete
          </button>
        </div>
      ))}
    </div>
  ),
}))

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('RestaurantShopsPage', () => {
  const mockRouter = {
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }

  beforeEach(() => {
    jest.clearAllMocks()
    const { useRouter } = require('next/navigation')
    useRouter.mockReturnValue(mockRouter)
  })

  describe('Loading State', () => {
    it('shows loading spinner when data is loading', () => {
      const { useRestaurants } = require('@/hooks/useRestaurant')
      useRestaurants.mockReturnValue({
        ...mockUseRestaurants,
        isLoading: true,
      })

      renderWithProvider(<RestaurantShopsPage />)

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
    })
  })

  describe('Restaurant List Display', () => {
    it('renders restaurant list when data is loaded', async () => {
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('restaurant-list')).toBeInTheDocument()
        expect(screen.getByText('Test Restaurant 1')).toBeInTheDocument()
        expect(screen.getByText('Test Restaurant 2')).toBeInTheDocument()
      })
    })

    it('displays restaurant information correctly', async () => {
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByText('A great test restaurant')).toBeInTheDocument()
        expect(screen.getByText('Another great test restaurant')).toBeInTheDocument()
      })
    })

    it('shows create restaurant button', async () => {
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('create-restaurant-btn')).toBeInTheDocument()
      })
    })
  })

  describe('Restaurant Actions', () => {
    it('navigates to create restaurant page when create button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        const createBtn = screen.getByTestId('create-restaurant-btn')
        expect(createBtn).toBeInTheDocument()
      })

      await user.click(screen.getByTestId('create-restaurant-btn'))

      expect(mockRouter.push).toHaveBeenCalledWith('/app/restaurant/new')
    })

    it('navigates to restaurant view when view button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('view-restaurant-1')).toBeInTheDocument()
      })

      await user.click(screen.getByTestId('view-restaurant-1'))

      expect(mockRouter.push).toHaveBeenCalledWith('/app/restaurant/test-restaurant-1')
    })

    it('navigates to restaurant edit when edit button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('edit-restaurant-1')).toBeInTheDocument()
      })

      await user.click(screen.getByTestId('edit-restaurant-1'))

      expect(mockRouter.push).toHaveBeenCalledWith('/app/restaurant/test-restaurant-1/settings')
    })

    it('shows confirmation dialog and deletes restaurant when delete is confirmed', async () => {
      const user = userEvent.setup()
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(true)
      
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('delete-restaurant-1')).toBeInTheDocument()
      })

      await user.click(screen.getByTestId('delete-restaurant-1'))

      expect(confirmSpy).toHaveBeenCalledWith('Are you sure you want to delete this restaurant?')
      expect(mockUseRestaurants.deleteRestaurant).toHaveBeenCalledWith('restaurant-1')

      confirmSpy.mockRestore()
    })

    it('does not delete restaurant when delete is cancelled', async () => {
      const user = userEvent.setup()
      const confirmSpy = jest.spyOn(window, 'confirm').mockReturnValue(false)
      
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('delete-restaurant-1')).toBeInTheDocument()
      })

      await user.click(screen.getByTestId('delete-restaurant-1'))

      expect(confirmSpy).toHaveBeenCalledWith('Are you sure you want to delete this restaurant?')
      expect(mockUseRestaurants.deleteRestaurant).not.toHaveBeenCalled()

      confirmSpy.mockRestore()
    })
  })

  describe('Data Handling', () => {
    it('passes correct props to RestaurantList component', async () => {
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        const restaurantList = screen.getByTestId('restaurant-list')
        expect(restaurantList).toBeInTheDocument()
      })

      // Verify that the component receives the expected data
      expect(screen.getByText('Test Restaurant 1')).toBeInTheDocument()
      expect(screen.getByText('Test Restaurant 2')).toBeInTheDocument()
    })

    it('handles empty restaurant list', async () => {
      const { useRestaurants } = require('@/hooks/useRestaurant')
      useRestaurants.mockReturnValue({
        ...mockUseRestaurants,
        restaurants: [],
        totalCount: 0,
      })

      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('restaurant-list')).toBeInTheDocument()
      })

      // Should still show the create button even with no restaurants
      expect(screen.getByTestId('create-restaurant-btn')).toBeInTheDocument()
    })
  })

  describe('Filter Updates', () => {
    it('calls updateFilters when filters change', async () => {
      renderWithProvider(<RestaurantShopsPage />)

      await waitFor(() => {
        expect(screen.getByTestId('restaurant-list')).toBeInTheDocument()
      })

      // The RestaurantList component should have access to updateFilters
      expect(mockUseRestaurants.updateFilters).toBeDefined()
    })
  })
})
