'use client';

import { useRouter } from '@/i18n/navigation';
import { RestaurantForm, type RestaurantFormData } from '@/components/restaurant/RestaurantForm';
import { useRestaurants } from '@/hooks/useRestaurant';
import { MESSAGES } from '@/lib/constants/messages';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link } from '@/i18n/navigation';

export default function NewRestaurantPage() {
  const router = useRouter();
  const { createRestaurant, isCreating } = useRestaurants({});

  const handleSubmit = async (data: RestaurantFormData) => {
    try {
      await createRestaurant(data);
      router.push('/app/restaurant');
    } catch (error) {
      // Error is handled by the hook
      console.error('Failed to create restaurant:', error);
    }
  };

  const handleCancel = () => {
    router.push('/app/restaurant');
  };

  return (
    <div className="min-h-screen bg-[#fbfaf9] p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/app/restaurant">
              <Button
                variant="ghost"
                size="sm"
                className="text-[#8a745c] hover:text-[#181510] hover:bg-[#f1edea]"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                {MESSAGES.ACTION.BACK}
              </Button>
            </Link>

            <div className="h-6 w-px bg-[#e5e1dc]" />

            <nav className="flex items-center space-x-2 text-sm text-[#8a745c]">
              <Link href="/app/restaurant" className="hover:text-[#181510]">
                Restaurants
              </Link>
              <span>/</span>
              <span className="text-[#181510] font-medium">New Restaurant</span>
            </nav>
          </div>

          <Card className="bg-[#fbfaf9] border-[#e5e1dc]">
            <CardHeader>
              <CardTitle className="text-[#181510] flex items-center gap-3">
                <div className="p-2 bg-[#8a745c] rounded-lg">
                  <Plus className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold">Create New Restaurant</h1>
                  <p className="text-[#8a745c] text-base font-normal mt-1">
                    Set up your restaurant profile and start managing your business
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
          </Card>
        </div>

        {/* Form */}
        <RestaurantForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={isCreating}
          isEditing={false}
        />

        {/* Help Section */}
        <Card className="bg-[#f9f7f4] border-[#e5e1dc] mt-6">
          <CardContent className="p-6">
            <h3 className="text-[#181510] font-semibold mb-3">Getting Started Tips</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-[#8a745c]">
              <div>
                <h4 className="font-medium text-[#181510] mb-2">Restaurant Information</h4>
                <ul className="space-y-1">
                  <li>• Choose a clear, memorable restaurant name</li>
                  <li>• Add a compelling description to attract customers</li>
                  <li>• Ensure contact information is accurate</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-[#181510] mb-2">Business Settings</h4>
                <ul className="space-y-1">
                  <li>• Select the correct timezone for your location</li>
                  <li>• Choose your local currency for pricing</li>
                  <li>• Upload a high-quality logo for branding</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
