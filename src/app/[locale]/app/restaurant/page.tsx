'use client';

import { useState } from 'react';
import { useRouter } from '@/i18n/navigation';
import { RestaurantList } from '@/components/restaurant/RestaurantList';
import { useRestaurants } from '@/hooks/useRestaurant';
import { AppLoading } from '@/components/ui/app-loading';
import { RestaurantDeleteDialog } from '@/components/ui/delete-confirmation-dialog';
import { PageHeader, PageHeaderSkeleton, PageHeaderError } from '@/components/ui/page-header';
import { Shop } from '@/lib/types/shop';
import { Store, Plus } from 'lucide-react';

export default function RestaurantShopsPage() {
  const router = useRouter();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [restaurantToDelete, setRestaurantToDelete] = useState<Shop | null>(null);

  const {
    restaurants,
    isLoading,
    totalCount,
    statusCounts,
    cityCounts,
    filters,
    updateFilters,
    deleteRestaurant,
    isDeleting,
    isError,
    error,
    refetch
  } = useRestaurants({
    page: 1,
    limit: 12
  });

  const handleCreateRestaurant = () => {
    router.push('/app/restaurant/new');
  };

  const handleViewRestaurant = (restaurant: Shop) => {
    router.push(`/app/restaurant/${restaurant.slug}`);
  };

  const handleEditRestaurant = (restaurant: Shop) => {
    router.push(`/app/restaurant/${restaurant.slug}/settings`);
  };

  const handleDeleteRestaurant = (restaurant: Shop) => {
    setRestaurantToDelete(restaurant);
    setDeleteDialogOpen(true);
  };

  const confirmDeleteRestaurant = async () => {
    if (restaurantToDelete) {
      await deleteRestaurant(restaurantToDelete.id);
      setDeleteDialogOpen(false);
      setRestaurantToDelete(null);
    }
  };

  // Breadcrumb items for navigation
  const breadcrumbItems = [
    { label: 'Dashboard', href: '/app' },
    { label: 'Restaurants' }
  ];

  // Action buttons for the header
  const actions = [
    {
      label: 'Add Restaurant',
      onClick: handleCreateRestaurant,
      icon: Plus,
      variant: 'default' as const
    }
  ];

  // Stats for the header
  const stats = [
    {
      label: 'Total Restaurants',
      value: totalCount,
      icon: Store,
      variant: 'outline' as const
    },
    ...(statusCounts.active ? [{
      label: 'Active',
      value: statusCounts.active,
      variant: 'default' as const,
      className: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300'
    }] : []),
    ...(statusCounts.inactive ? [{
      label: 'Inactive',
      value: statusCounts.inactive,
      variant: 'outline' as const,
      className: 'text-muted-foreground border-border'
    }] : [])
  ];

  // Error state
  if (isError) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-6">
          <PageHeaderError
            title="Restaurant Management"
            description="Manage your restaurants, view analytics, and track performance"
            icon={Store}
            breadcrumbItems={breadcrumbItems}
            actions={actions}
            error={error}
            onRetry={refetch}
          />
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-6">
          <PageHeaderSkeleton />
          <AppLoading type="restaurant" />
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-6">
          {/* Page Header with Breadcrumb */}
          <PageHeader
            title="Restaurant Management"
            description="Manage your restaurants, view analytics, and track performance"
            icon={Store}
            breadcrumbItems={breadcrumbItems}
            actions={actions}
            stats={totalCount > 0 ? stats : []}
          />

          {/* Restaurant List */}
          <RestaurantList
            restaurants={restaurants}
            isLoading={isLoading}
            totalCount={totalCount}
            statusCounts={statusCounts}
            cityCounts={cityCounts}
            filters={filters}
            onFiltersChange={updateFilters}
            onRestaurantView={handleViewRestaurant}
            onRestaurantEdit={handleEditRestaurant}
            onRestaurantDelete={handleDeleteRestaurant}
            onRestaurantCreate={handleCreateRestaurant}
            showFilters={true}
            showCreateButton={false} // We moved the create button to the header
          />
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <RestaurantDeleteDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={confirmDeleteRestaurant}
        restaurantName={restaurantToDelete?.name}
        isLoading={isDeleting}
      />
    </>
  );
}
