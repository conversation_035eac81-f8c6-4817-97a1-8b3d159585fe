'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, ShoppingBag } from 'lucide-react';
import { Link } from '@/i18n/navigation';
import { useCreateMerchantMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { toast } from 'sonner';

export default function NewRetailStorePage() {
  const router = useRouter();
  const { data: session } = useSession();
  const [createMerchant, { isLoading }] = useCreateMerchantMutation();

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    state: '',
    country: '',
    postalCode: '',
    timezone: 'Asia/Bangkok',
    currency: 'THB'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error('Store name is required');
      return;
    }

    try {
      const result = await createMerchant({
        ...formData,
        settings: {
          businessType: 'retail'
        }
      }).unwrap();

      toast.success('Retail store created successfully!');
      router.push(`/app/retail/${result.slug || result.id}`);
    } catch (error) {
      console.error('Error creating retail store:', error);
      toast.error('Failed to create retail store. Please try again.');
    }
  };

  return (
    <div className="font-be-vietnam min-h-screen bg-[#fbfaf9]">
      <div className="container mx-auto p-4 max-w-2xl">
        {/* Header */}
        <div className="flex items-center gap-4 mb-6">
          <Link href="/app">
            <Button variant="outline" size="sm" className="bg-white border-[#e5e1dc]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <div>
            <h1 className="text-[#181510] text-[32px] font-bold leading-tight">Create Retail Store</h1>
            <p className="text-[#8a745c] text-sm">Set up your retail store to start managing inventory and sales</p>
          </div>
        </div>

        {/* Form */}
        <Card className="bg-white border-[#e5e1dc]">
          <CardHeader>
            <CardTitle className="text-[#181510] flex items-center">
              <ShoppingBag className="h-5 w-5 mr-2 text-[#8a745c]" />
              Store Information
            </CardTitle>
            <CardDescription className="text-[#8a745c]">
              Enter your retail store details to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Basic Information */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name" className="text-[#181510]">Store Name *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Enter your store name"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description" className="text-[#181510]">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Describe your retail store"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    rows={3}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-[#181510] font-medium">Contact Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email" className="text-[#181510]">Email</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-[#181510]">Phone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+66 2 123 4567"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-4">
                <h3 className="text-[#181510] font-medium">Address</h3>
                <div>
                  <Label htmlFor="address" className="text-[#181510]">Street Address</Label>
                  <Input
                    id="address"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    placeholder="123 Main Street"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="city" className="text-[#181510]">City</Label>
                    <Input
                      id="city"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      placeholder="Bangkok"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="state" className="text-[#181510]">State/Province</Label>
                    <Input
                      id="state"
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      placeholder="Bangkok"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                  <div>
                    <Label htmlFor="postalCode" className="text-[#181510]">Postal Code</Label>
                    <Input
                      id="postalCode"
                      name="postalCode"
                      value={formData.postalCode}
                      onChange={handleInputChange}
                      placeholder="10100"
                      className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="country" className="text-[#181510]">Country</Label>
                  <Input
                    id="country"
                    name="country"
                    value={formData.country}
                    onChange={handleInputChange}
                    placeholder="Thailand"
                    className="bg-[#f1edea] border-none text-[#181510] placeholder:text-[#8a745c]"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end gap-4 pt-6">
                <Link href="/app">
                  <Button variant="outline" className="bg-[#f1edea] text-[#181510] border-none">
                    Cancel
                  </Button>
                </Link>
                <Button
                  type="submit"
                  disabled={isLoading}
                  className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
                >
                  {isLoading ? 'Creating...' : 'Create Store'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
