'use client'

import { useSession, signOut } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  User,
  Mail,
  Shield,
  Calendar,
  LogOut,
  CheckCircle,
  XCircle,
  Settings
} from 'lucide-react'
import OAuthButtons from '@/components/auth/OAuthButtons'
import { useRouter } from 'next/navigation'

export default function OAuthTestPage() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut({ redirect: false })
    router.push('/login')
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-[#fbfaf9] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#8a745c] mx-auto"></div>
          <p className="mt-2 text-[#8a745c]">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#fbfaf9] py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-[#181510]">OAuth Testing Page</h1>
          <p className="mt-2 text-[#8a745c]">Test OAuth authentication and view session data</p>
        </div>

        {session ? (
          <div className="space-y-6">
            {/* User Session Info */}
            <Card className="bg-[#f1edea] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Authentication Successful
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={session.user?.image || ''} alt={session.user?.name || ''} />
                    <AvatarFallback className="bg-[#8a745c] text-white">
                      {session.user?.name?.charAt(0) || session.user?.email?.charAt(0) || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="space-y-1">
                    <h3 className="text-lg font-semibold text-[#181510]">
                      {session.user?.name || 'No name provided'}
                    </h3>
                    <p className="text-[#8a745c] flex items-center gap-1">
                      <Mail className="h-4 w-4" />
                      {session.user?.email}
                    </p>
                    <Badge variant="secondary" className="bg-[#e5ccb2] text-[#181510]">
                      <Shield className="h-3 w-3 mr-1" />
                      {session.user?.role || 'user'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Session Details */}
            <Card className="bg-[#f1edea] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <Settings className="h-5 w-5 text-[#8a745c]" />
                  Session Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-[#8a745c]">User ID</label>
                      <p className="text-[#181510] font-mono text-sm bg-[#fbfaf9] p-2 rounded border">
                        {session.user?.id}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-[#8a745c]">Email</label>
                      <p className="text-[#181510] bg-[#fbfaf9] p-2 rounded border">
                        {session.user?.email}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-[#8a745c]">Name</label>
                      <p className="text-[#181510] bg-[#fbfaf9] p-2 rounded border">
                        {session.user?.name || 'Not provided'}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-[#8a745c]">Role</label>
                      <p className="text-[#181510] bg-[#fbfaf9] p-2 rounded border">
                        {session.user?.role || 'user'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Actions */}
            <Card className="bg-[#f1edea] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="text-[#181510]">Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-3">
                  <Button
                    onClick={() => router.push('/app')}
                    className="bg-[#8a745c] hover:bg-[#6d5a48] text-white"
                  >
                    Go to App
                  </Button>
                  <Button
                    onClick={handleSignOut}
                    variant="outline"
                    className="border-red-300 text-red-700 hover:bg-red-50"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Not Authenticated */}
            <Card className="bg-[#f1edea] border-[#e5e1dc]">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-[#181510]">
                  <XCircle className="h-5 w-5 text-red-600" />
                  Not Authenticated
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-[#8a745c] mb-4">
                  You are not currently signed in. Please authenticate using one of the methods below.
                </p>

                {/* OAuth Sign-in Options */}
                <OAuthButtons callbackUrl="/oauth-test" />

                <div className="mt-4 text-center">
                  <Button
                    onClick={() => router.push('/login')}
                    variant="outline"
                    className="border-[#e5e1dc] text-[#181510] hover:bg-[#f1edea]"
                  >
                    Go to Login Page
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
