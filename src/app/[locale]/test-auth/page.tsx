'use client'

import { useAuth } from '@/hooks/useAuth'
import { useSession } from 'next-auth/react'
import AuthStatus from '@/components/auth/AuthStatus'

export default function TestAuthPage() {
  const { data: session, status } = useSession()
  const { user, isAuthenticated, isLoading } = useAuth()

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">NextAuth Test Page</h1>
          <p className="mt-2 text-gray-600">Testing NextAuth integration</p>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* NextAuth Session Info */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">NextAuth Session</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Status:</strong> {status}</p>
              <p><strong>Session:</strong> {session ? 'Active' : 'None'}</p>
              {session && (
                <>
                  <p><strong>User ID:</strong> {session.user?.id}</p>
                  <p><strong>Email:</strong> {session.user?.email}</p>
                  <p><strong>Role:</strong> {session.user?.role}</p>
                </>
              )}
            </div>
          </div>

          {/* Redux Auth State */}
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">Redux Auth State</h2>
            <div className="space-y-2 text-sm">
              <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
              <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
              {user && (
                <>
                  <p><strong>User ID:</strong> {user.id}</p>
                  <p><strong>Email:</strong> {user.email}</p>
                  <p><strong>Role:</strong> {user.role}</p>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Auth Status Component */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Auth Status Component</h2>
          <AuthStatus />
        </div>

        {/* Quick Links */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Quick Links</h2>
          <div className="space-x-4">
            <a href="/login" className="text-blue-600 hover:underline">Login Page</a>
            <a href="/register" className="text-blue-600 hover:underline">Register Page</a>
            <a href="/admin/dashboard" className="text-blue-600 hover:underline">Admin Dashboard</a>
          </div>
        </div>
      </div>
    </div>
  )
}
