/**
 * API route for user authentication login
 * Forwards requests to the Golang backend
 */

import { NextRequest, NextResponse } from 'next/server';
import { fetchClient, handleApiResponse } from '@/lib/fetch/fetchClient';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const response = await fetchClient('/auth/login', {
      method: 'POST',
      body: JSON.stringify(body),
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await handleApiResponse(response);
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error in POST /api/auth/login:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Authentication failed' },
      { status: error instanceof Error && 'status' in error ? (error as any).status : 500 }
    );
  }
}
