import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export async function GET(request: NextRequest) {
  try {
    // Get NextAuth JWT token from the request
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token) {
      return NextResponse.json(
        { error: 'No authentication token found' },
        { status: 401 }
      )
    }

    // Return token information (for debugging)
    return NextResponse.json({
      message: 'Token extracted successfully',
      user: {
        id: token.sub,
        email: token.email,
        name: token.name,
        role: token.role,
      },
      tokenExists: !!token,
      headers: {
        'X-User-ID': token.sub || '',
        'X-User-Email': token.email || '',
        'X-User-Role': (token.role as string) || 'user',
      }
    })

  } catch (error) {
    console.error('Token test error:', error)
    return NextResponse.json(
      { error: 'Failed to extract token' },
      { status: 500 }
    )
  }
}
