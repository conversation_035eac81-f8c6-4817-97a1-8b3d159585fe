import React from 'react'
import { render, screen, waitFor, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'

// Simple working page component for testing
const WorkingPageComponent = ({ 
  merchantSlug, 
  branchSlug, 
  isLoading = false, 
  hasError = false,
  data = null 
}: {
  merchantSlug: string
  branchSlug: string
  isLoading?: boolean
  hasError?: boolean
  data?: any
}) => {
  const [searchTerm, setSearchTerm] = React.useState('')
  const [activeTab, setActiveTab] = React.useState<'list' | 'grid'>('list')

  if (isLoading) {
    return <div data-testid="app-loading">Loading...</div>
  }

  if (hasError) {
    return (
      <div data-testid="error-state">
        <h1>Error Loading Data</h1>
        <p>There was an error loading the data. Please try again.</p>
      </div>
    )
  }

  if (!data) {
    return (
      <div data-testid="not-found">
        <h1>Restaurant Not Found</h1>
        <p>The restaurant or branch you are looking for does not exist.</p>
      </div>
    )
  }

  const filteredItems = data.items?.filter((item: any) =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  ) || []

  return (
    <div data-testid="working-page" className="p-6">
      {/* Header */}
      <div className="mb-6">
        <a href={`/app/restaurant/${merchantSlug}`} data-testid="back-link">
          <button>Back to Restaurant</button>
        </a>
      </div>

      {/* Title */}
      <div className="mb-6">
        <h1 data-testid="page-title">{data.title}</h1>
        <p data-testid="page-description">{data.description}</p>
      </div>

      {/* Actions */}
      <div className="mb-6 flex gap-3">
        <button data-testid="refresh-btn" onClick={() => console.log('refresh')}>
          Refresh
        </button>
        <a href={`/app/restaurant/${merchantSlug}/${branchSlug}/new`} data-testid="add-link">
          <button>Add New</button>
        </a>
      </div>

      {/* Search */}
      <div className="mb-6">
        <input
          data-testid="search-input"
          type="text"
          placeholder="Search items..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full px-3 py-2 border rounded-md"
        />
      </div>

      {/* View Toggle */}
      <div className="mb-6">
        <div className="flex border-b">
          <button
            data-testid="list-view-btn"
            onClick={() => setActiveTab('list')}
            className={`px-4 py-2 ${activeTab === 'list' ? 'border-b-2 border-blue-500' : ''}`}
          >
            List View
          </button>
          <button
            data-testid="grid-view-btn"
            onClick={() => setActiveTab('grid')}
            className={`px-4 py-2 ${activeTab === 'grid' ? 'border-b-2 border-blue-500' : ''}`}
          >
            Grid View
          </button>
        </div>
      </div>

      {/* Content */}
      <div data-testid="content-area">
        {filteredItems.length === 0 ? (
          <div data-testid="empty-state" className="text-center py-8">
            <p>No items found</p>
          </div>
        ) : (
          <div data-testid={`${activeTab}-view`} className={activeTab === 'grid' ? 'grid grid-cols-3 gap-4' : 'space-y-4'}>
            {filteredItems.map((item: any) => (
              <div key={item.id} data-testid={`item-${item.id}`} className="border rounded-lg p-4">
                <h3 data-testid={`item-name-${item.id}`}>{item.name}</h3>
                <p data-testid={`item-description-${item.id}`}>{item.description}</p>
                <div className="mt-2 space-x-2">
                  <a href={`/app/restaurant/${merchantSlug}/${branchSlug}/items/${item.id}`}>
                    <button data-testid={`view-btn-${item.id}`} className="text-sm">View</button>
                  </a>
                  <a href={`/app/restaurant/${merchantSlug}/${branchSlug}/items/${item.id}/edit`}>
                    <button data-testid={`edit-btn-${item.id}`} className="text-sm">Edit</button>
                  </a>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

// Mock data
const mockData = {
  title: 'Test Management',
  description: 'Manage your test items',
  items: [
    {
      id: 'item-1',
      name: 'Test Item 1',
      description: 'Description for test item 1',
    },
    {
      id: 'item-2',
      name: 'Test Item 2',
      description: 'Description for test item 2',
    },
    {
      id: 'item-3',
      name: 'Another Item',
      description: 'Description for another item',
    },
  ],
}

const createMockStore = () => {
  return configureStore({
    reducer: {
      api: () => ({}),
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false,
      }),
  })
}

const renderWithProvider = (component: React.ReactElement) => {
  const store = createMockStore()
  return render(<Provider store={store}>{component}</Provider>)
}

describe('WorkingPageComponent', () => {
  const defaultProps = {
    merchantSlug: 'test-restaurant',
    branchSlug: 'main-branch',
  }

  describe('Loading States', () => {
    it('shows loading spinner when isLoading is true', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} isLoading={true} />
      )

      expect(screen.getByTestId('app-loading')).toBeInTheDocument()
      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })
  })

  describe('Error States', () => {
    it('shows error message when hasError is true', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} hasError={true} />
      )

      expect(screen.getByTestId('error-state')).toBeInTheDocument()
      expect(screen.getByText('Error Loading Data')).toBeInTheDocument()
      expect(screen.getByText('There was an error loading the data. Please try again.')).toBeInTheDocument()
    })

    it('shows not found message when data is null', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={null} />
      )

      expect(screen.getByTestId('not-found')).toBeInTheDocument()
      expect(screen.getByText('Restaurant Not Found')).toBeInTheDocument()
      expect(screen.getByText('The restaurant or branch you are looking for does not exist.')).toBeInTheDocument()
    })
  })

  describe('Successful Data Loading', () => {
    it('renders page title and description', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      expect(screen.getByTestId('page-title')).toHaveTextContent('Test Management')
      expect(screen.getByTestId('page-description')).toHaveTextContent('Manage your test items')
    })

    it('renders back link with correct href', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      const backLink = screen.getByTestId('back-link')
      expect(backLink).toHaveAttribute('href', '/app/restaurant/test-restaurant')
    })

    it('renders action buttons', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      expect(screen.getByTestId('refresh-btn')).toBeInTheDocument()
      expect(screen.getByTestId('add-link')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/new'
      )
    })

    it('renders search input', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      const searchInput = screen.getByTestId('search-input')
      expect(searchInput).toBeInTheDocument()
      expect(searchInput).toHaveAttribute('placeholder', 'Search items...')
    })

    it('renders view toggle buttons', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      expect(screen.getByTestId('list-view-btn')).toBeInTheDocument()
      expect(screen.getByTestId('grid-view-btn')).toBeInTheDocument()
    })

    it('displays all items by default', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      expect(screen.getByTestId('item-item-1')).toBeInTheDocument()
      expect(screen.getByTestId('item-item-2')).toBeInTheDocument()
      expect(screen.getByTestId('item-item-3')).toBeInTheDocument()
      expect(screen.getByText('Test Item 1')).toBeInTheDocument()
      expect(screen.getByText('Test Item 2')).toBeInTheDocument()
      expect(screen.getByText('Another Item')).toBeInTheDocument()
    })
  })

  describe('Search Functionality', () => {
    it('filters items by name', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      // Initially all items should be visible
      expect(screen.getByText('Test Item 1')).toBeInTheDocument()
      expect(screen.getByText('Test Item 2')).toBeInTheDocument()
      expect(screen.getByText('Another Item')).toBeInTheDocument()

      // Search for "test"
      const searchInput = screen.getByTestId('search-input')
      await user.type(searchInput, 'test')

      // Only items with "test" should be visible
      expect(screen.getByText('Test Item 1')).toBeInTheDocument()
      expect(screen.getByText('Test Item 2')).toBeInTheDocument()
      expect(screen.queryByText('Another Item')).not.toBeInTheDocument()
    })

    it('shows empty state when no items match search', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      const searchInput = screen.getByTestId('search-input')
      await user.type(searchInput, 'nonexistent')

      expect(screen.getByTestId('empty-state')).toBeInTheDocument()
      expect(screen.getByText('No items found')).toBeInTheDocument()
    })

    it('clears search results when search is cleared', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      const searchInput = screen.getByTestId('search-input')
      await user.type(searchInput, 'test')

      // Should show filtered results
      expect(screen.queryByText('Another Item')).not.toBeInTheDocument()

      await user.clear(searchInput)

      // Should show all items again
      expect(screen.getByText('Test Item 1')).toBeInTheDocument()
      expect(screen.getByText('Test Item 2')).toBeInTheDocument()
      expect(screen.getByText('Another Item')).toBeInTheDocument()
    })
  })

  describe('View Mode Toggle', () => {
    it('starts in list view by default', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      const listBtn = screen.getByTestId('list-view-btn')
      const gridBtn = screen.getByTestId('grid-view-btn')

      expect(listBtn).toHaveClass('border-b-2', 'border-blue-500')
      expect(gridBtn).not.toHaveClass('border-b-2', 'border-blue-500')
      expect(screen.getByTestId('list-view')).toBeInTheDocument()
    })

    it('switches to grid view when grid button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      const gridBtn = screen.getByTestId('grid-view-btn')
      await user.click(gridBtn)

      expect(gridBtn).toHaveClass('border-b-2', 'border-blue-500')
      expect(screen.getByTestId('list-view-btn')).not.toHaveClass('border-b-2', 'border-blue-500')
      expect(screen.getByTestId('grid-view')).toBeInTheDocument()
    })

    it('switches back to list view when list button is clicked', async () => {
      const user = userEvent.setup()
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      // Switch to grid first
      await user.click(screen.getByTestId('grid-view-btn'))
      expect(screen.getByTestId('grid-view')).toBeInTheDocument()

      // Switch back to list
      await user.click(screen.getByTestId('list-view-btn'))
      expect(screen.getByTestId('list-view')).toBeInTheDocument()
    })
  })

  describe('Item Actions', () => {
    it('renders view and edit links for each item', () => {
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={mockData} />
      )

      // Check first item
      const viewBtn1 = screen.getByTestId('view-btn-item-1')
      const editBtn1 = screen.getByTestId('edit-btn-item-1')

      expect(viewBtn1.closest('a')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/items/item-1'
      )
      expect(editBtn1.closest('a')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/items/item-1/edit'
      )

      // Check second item
      const viewBtn2 = screen.getByTestId('view-btn-item-2')
      const editBtn2 = screen.getByTestId('edit-btn-item-2')

      expect(viewBtn2.closest('a')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/items/item-2'
      )
      expect(editBtn2.closest('a')).toHaveAttribute(
        'href',
        '/app/restaurant/test-restaurant/main-branch/items/item-2/edit'
      )
    })
  })

  describe('Empty States', () => {
    it('shows empty state when no items exist', () => {
      const emptyData = { ...mockData, items: [] }
      renderWithProvider(
        <WorkingPageComponent {...defaultProps} data={emptyData} />
      )

      expect(screen.getByTestId('empty-state')).toBeInTheDocument()
      expect(screen.getByText('No items found')).toBeInTheDocument()
    })
  })
})
