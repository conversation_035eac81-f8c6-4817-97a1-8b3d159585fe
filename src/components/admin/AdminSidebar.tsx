'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useAppSelector } from '@/lib/redux/hooks';

// Icons (you can replace with your preferred icon library)
const DashboardIcon = () => <span>📊</span>;
const MerchantsIcon = () => <span>🏪</span>;
const UsersIcon = () => <span>👥</span>;
const OrdersIcon = () => <span>📦</span>;
const SettingsIcon = () => <span>⚙️</span>;
const RestaurantIcon = () => <span>🍽️</span>;
const RetailIcon = () => <span>👕</span>;
const ServiceIcon = () => <span>🔧</span>;

export default function AdminSidebar() {
  const pathname = usePathname();
  const [activeMerchantType, setActiveMerchantType] = useState<string | null>(null);
  const { user } = useAppSelector((state) => state.auth);

  // Function to determine if a link is active
  const isActive = (path: string) => pathname === path;

  // Function to toggle merchant type submenu
  const toggleMerchantType = (type: string) => {
    setActiveMerchantType(activeMerchantType === type ? null : type);
  };

  return (
    <aside className="w-64 bg-sidebar text-sidebar-foreground">
      <div className="p-4 border-b border-sidebar-border">
        <h1 className="text-xl font-bold">ADC Admin</h1>
      </div>

      <nav className="p-4">
        <ul className="space-y-2">
          {/* Dashboard */}
          <li>
            <Link
              href="/admin/dashboard"
              className={`flex items-center p-2 rounded-md ${isActive('/admin/dashboard') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <DashboardIcon />
              <span className="ml-3">Dashboard</span>
            </Link>
          </li>

          {/* Merchants */}
          <li>
            <button
              onClick={() => toggleMerchantType('all')}
              className={`flex items-center justify-between w-full p-2 rounded-md ${activeMerchantType === 'all' ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <div className="flex items-center">
                <MerchantsIcon />
                <span className="ml-3">All Merchants</span>
              </div>
              <span>{activeMerchantType === 'all' ? '▼' : '▶'}</span>
            </button>

            {activeMerchantType === 'all' && (
              <ul className="pl-6 mt-2 space-y-1">
                <li>
                  <Link
                    href="/admin/merchants"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">All Merchants</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/admin/merchants/create"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/create') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">Create Merchant</span>
                  </Link>
                </li>
              </ul>
            )}
          </li>

          {/* Restaurant Merchants */}
          <li>
            <button
              onClick={() => toggleMerchantType('restaurant')}
              className={`flex items-center justify-between w-full p-2 rounded-md ${activeMerchantType === 'restaurant' ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <div className="flex items-center">
                <RestaurantIcon />
                <span className="ml-3">Restaurants</span>
              </div>
              <span>{activeMerchantType === 'restaurant' ? '▼' : '▶'}</span>
            </button>

            {activeMerchantType === 'restaurant' && (
              <ul className="pl-6 mt-2 space-y-1">
                <li>
                  <Link
                    href="/admin/merchants/restaurant"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/restaurant') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">All Restaurants</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/admin/merchants/restaurant/menu"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/restaurant/menu') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">Menu Management</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/admin/merchants/restaurant/reservations"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/restaurant/reservations') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">Reservations</span>
                  </Link>
                </li>
              </ul>
            )}
          </li>

          {/* Retail Merchants */}
          <li>
            <button
              onClick={() => toggleMerchantType('retail')}
              className={`flex items-center justify-between w-full p-2 rounded-md ${activeMerchantType === 'retail' ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <div className="flex items-center">
                <RetailIcon />
                <span className="ml-3">Retail Stores</span>
              </div>
              <span>{activeMerchantType === 'retail' ? '▼' : '▶'}</span>
            </button>

            {activeMerchantType === 'retail' && (
              <ul className="pl-6 mt-2 space-y-1">
                <li>
                  <Link
                    href="/admin/merchants/retail"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/retail') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">All Retail Stores</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/admin/merchants/retail/products"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/retail/products') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">Product Management</span>
                  </Link>
                </li>
                <li>
                  <Link
                    href="/admin/merchants/retail/inventory"
                    className={`flex items-center p-2 rounded-md ${isActive('/admin/merchants/retail/inventory') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
                  >
                    <span className="ml-3">Inventory</span>
                  </Link>
                </li>
              </ul>
            )}
          </li>

          {/* Users */}
          <li>
            <Link
              href="/admin/users"
              className={`flex items-center p-2 rounded-md ${isActive('/admin/users') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <UsersIcon />
              <span className="ml-3">Users</span>
            </Link>
          </li>

          {/* Orders */}
          <li>
            <Link
              href="/admin/orders"
              className={`flex items-center p-2 rounded-md ${isActive('/admin/orders') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <OrdersIcon />
              <span className="ml-3">Orders</span>
            </Link>
          </li>

          {/* Settings */}
          <li>
            <Link
              href="/admin/settings"
              className={`flex items-center p-2 rounded-md ${isActive('/admin/settings') ? 'bg-sidebar-accent text-sidebar-accent-foreground' : 'hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'}`}
            >
              <SettingsIcon />
              <span className="ml-3">Settings</span>
            </Link>
          </li>
        </ul>
      </nav>
    </aside>
  );
}
