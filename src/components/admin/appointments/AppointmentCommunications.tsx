'use client';

import { useState } from 'react';
import { 
  useSendAppointmentCommunicationMutation,
  useScheduleAppointmentRemindersMutation
} from '@/lib/redux/api/endpoints/appointmentApi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Mail, MessageSquare, Calendar, Clock, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

interface AppointmentCommunicationsProps {
  merchantId: string;
  appointmentId: string;
  appointment: any;
}

export default function AppointmentCommunications({ 
  merchantId, 
  appointmentId,
  appointment
}: AppointmentCommunicationsProps) {
  const [communicationType, setCommunicationType] = useState<'confirmation' | 'reminder' | 'cancellation'>('confirmation');
  const [communicationMethod, setCommunicationMethod] = useState<'email' | 'sms'>('email');
  
  // Mutations
  const [sendCommunication, { isLoading: isSending }] = useSendAppointmentCommunicationMutation();
  const [scheduleReminders, { isLoading: isScheduling }] = useScheduleAppointmentRemindersMutation();
  
  // Handle sending a communication
  const handleSendCommunication = async () => {
    try {
      await sendCommunication({
        merchantId,
        appointmentId,
        type: communicationType,
        method: communicationMethod,
      }).unwrap();
      
      toast.success('Communication sent', {
        description: `The ${communicationType} ${communicationMethod} has been sent successfully.`,
      });
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to send communication. Please try again.',
      });
    }
  };
  
  // Handle scheduling reminders
  const handleScheduleReminders = async () => {
    try {
      await scheduleReminders({
        merchantId,
        appointmentId,
      }).unwrap();
      
      toast.success('Reminders scheduled', {
        description: 'Appointment reminders have been scheduled successfully.',
      });
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to schedule reminders. Please try again.',
      });
    }
  };
  
  // Check if the user has an email or phone
  const hasEmail = !!appointment?.user?.email;
  const hasPhone = !!appointment?.user?.phone;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Communications</CardTitle>
        <CardDescription>
          Send communications to the customer about this appointment
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="send" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="send">Send Message</TabsTrigger>
            <TabsTrigger value="schedule">Schedule Reminders</TabsTrigger>
          </TabsList>
          
          <TabsContent value="send" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="communication-type">Message Type</Label>
                  <Select
                    value={communicationType}
                    onValueChange={(value) => setCommunicationType(value as any)}
                  >
                    <SelectTrigger id="communication-type">
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="confirmation">Confirmation</SelectItem>
                      <SelectItem value="reminder">Reminder</SelectItem>
                      <SelectItem value="cancellation">Cancellation</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="communication-method">Send Via</Label>
                  <Select
                    value={communicationMethod}
                    onValueChange={(value) => setCommunicationMethod(value as any)}
                  >
                    <SelectTrigger id="communication-method">
                      <SelectValue placeholder="Select method" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="email" disabled={!hasEmail}>
                        <div className="flex items-center">
                          <Mail className="mr-2 h-4 w-4" />
                          <span>Email</span>
                          {!hasEmail && <span className="ml-2 text-xs text-red-500">(No email)</span>}
                        </div>
                      </SelectItem>
                      <SelectItem value="sms" disabled={!hasPhone}>
                        <div className="flex items-center">
                          <MessageSquare className="mr-2 h-4 w-4" />
                          <span>SMS</span>
                          {!hasPhone && <span className="ml-2 text-xs text-red-500">(No phone)</span>}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="pt-2">
                <Button 
                  onClick={handleSendCommunication} 
                  disabled={isSending || (!hasEmail && communicationMethod === 'email') || (!hasPhone && communicationMethod === 'sms')}
                  className="w-full"
                >
                  {isSending ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      {communicationMethod === 'email' ? (
                        <Mail className="mr-2 h-4 w-4" />
                      ) : (
                        <MessageSquare className="mr-2 h-4 w-4" />
                      )}
                      Send {communicationType.charAt(0).toUpperCase() + communicationType.slice(1)}
                    </>
                  )}
                </Button>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="schedule" className="space-y-4 pt-4">
            <div className="space-y-4">
              <div className="flex items-start space-x-4 p-4 border rounded-md">
                <div className="bg-primary/10 rounded-full p-2">
                  <Calendar className="h-5 w-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-sm font-medium">Appointment Reminders</h3>
                  <p className="text-sm text-muted-foreground mt-1">
                    Schedule automatic reminders for this appointment based on your settings.
                  </p>
                  <div className="flex items-center mt-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    <span>Reminders will be sent according to your notification settings</span>
                  </div>
                </div>
              </div>
              
              <div className="pt-2">
                <Button 
                  onClick={handleScheduleReminders} 
                  disabled={isScheduling || (!hasEmail && !hasPhone)}
                  className="w-full"
                >
                  {isScheduling ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Scheduling...
                    </>
                  ) : (
                    <>
                      <Calendar className="mr-2 h-4 w-4" />
                      Schedule Reminders
                    </>
                  )}
                </Button>
              </div>
              
              {!hasEmail && !hasPhone && (
                <div className="flex items-center p-3 text-sm bg-yellow-50 text-yellow-800 rounded-md">
                  <AlertCircle className="h-4 w-4 mr-2 flex-shrink-0" />
                  <span>Customer has no email or phone number. Cannot schedule reminders.</span>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="w-full flex flex-col space-y-2">
          <div className="flex items-center text-xs text-muted-foreground">
            <CheckCircle className="h-3 w-3 mr-1" />
            <span>
              {hasEmail ? 'Email available' : 'No email available'} 
              {hasEmail && hasPhone ? ' • ' : ''}
              {hasPhone ? 'Phone available' : 'No phone available'}
            </span>
          </div>
          <div className="flex items-center text-xs text-muted-foreground">
            <Calendar className="h-3 w-3 mr-1" />
            <span>
              Appointment: {appointment?.date} at {appointment?.startTime}
            </span>
          </div>
        </div>
      </CardFooter>
    </Card>
  );
}
