'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Mail, MessageSquare, Users, Clock, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface CampaignListProps {
  merchantId: string;
}

// Placeholder campaign data
const mockCampaigns = [
  {
    id: '1',
    name: 'Monthly Newsletter',
    type: 'email',
    status: 'scheduled',
    recipientCount: 124,
    scheduledFor: new Date('2023-12-15T10:00:00'),
    templateName: 'December Newsletter',
  },
  {
    id: '2',
    name: 'Holiday Promotion',
    type: 'email',
    status: 'draft',
    recipientCount: 250,
    templateName: 'Holiday Special Offers',
  },
  {
    id: '3',
    name: 'Appointment Reminder',
    type: 'sms',
    status: 'completed',
    recipientCount: 45,
    sentAt: new Date('2023-11-28T14:30:00'),
    templateName: 'SMS Appointment Reminder',
  },
];

export default function CampaignList({ merchantId }: CampaignListProps) {

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'draft':
        return 'bg-muted text-muted-foreground border-border';
      case 'scheduled':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'in_progress':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'cancelled':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  // Format date
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  // Get campaign icon based on type
  const getCampaignIcon = (type: string) => {
    return type === 'email' ? <Mail className="h-4 w-4" /> : <MessageSquare className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Campaigns</h2>

        <Button>
          <Users className="h-4 w-4 mr-2" />
          Create Campaign
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {mockCampaigns.map((campaign) => (
          <Card key={campaign.id}>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg">{campaign.name}</CardTitle>
                <div className="flex items-center space-x-1">
                  {getCampaignIcon(campaign.type)}
                  <span className="text-xs font-medium capitalize">{campaign.type}</span>
                </div>
              </div>
              <CardDescription>
                <Badge className={`${getStatusColor(campaign.status)} mt-2`}>
                  {campaign.status.charAt(0).toUpperCase() + campaign.status.slice(1)}
                </Badge>
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex items-center text-sm text-muted-foreground">
                  <Users className="h-4 w-4 mr-2" />
                  <span>{campaign.recipientCount} recipients</span>
                </div>

                {campaign.scheduledFor && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Calendar className="h-4 w-4 mr-2" />
                    <span>Scheduled for {formatDate(campaign.scheduledFor)}</span>
                  </div>
                )}

                {campaign.sentAt && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="h-4 w-4 mr-2" />
                    <span>Sent on {formatDate(campaign.sentAt)}</span>
                  </div>
                )}

                <div className="flex items-center text-sm text-muted-foreground">
                  <Mail className="h-4 w-4 mr-2" />
                  <span>Template: {campaign.templateName}</span>
                </div>
              </div>
            </CardContent>
            <CardFooter className="border-t pt-4 flex justify-between">
              <Button variant="outline" size="sm">
                View Details
              </Button>

              {campaign.status === 'draft' && (
                <Button size="sm">
                  Edit Campaign
                </Button>
              )}

              {campaign.status === 'scheduled' && (
                <Button variant="outline" size="sm" className="text-red-500 border-red-200 hover:bg-red-50">
                  Cancel
                </Button>
              )}
            </CardFooter>
          </Card>
        ))}
      </div>

      {mockCampaigns.length === 0 && (
        <div className="flex flex-col items-center justify-center p-8 text-center border rounded-lg">
          <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
            <AlertCircle className="h-6 w-6 text-gray-500" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No campaigns found</h3>
          <p className="text-gray-500 mb-4">
            You haven't created any communication campaigns yet.
          </p>
          <Button>
            Create Campaign
          </Button>
        </div>
      )}

      <div className="text-center text-sm text-muted-foreground mt-8">
        <p>This is a placeholder component. Campaign functionality will be implemented in a future update.</p>
      </div>
    </div>
  );
}
