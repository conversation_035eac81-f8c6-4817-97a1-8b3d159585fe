'use client';

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Mail, MessageSquare, Send, Users, BarChart, Settings } from 'lucide-react';
import TemplateList from './TemplateList';
import TemplateForm from './TemplateForm';
import SendCommunication from './SendCommunication';
import { CampaignList, CampaignForm } from './campaigns';
import { CommunicationAnalytics } from './analytics';
import { CommunicationSettings } from './settings';

interface CommunicationsPageProps {
  merchantId: string;
}

export default function CommunicationsPage({ merchantId }: CommunicationsPageProps) {
  const [activeTab, setActiveTab] = useState('templates');

  // Template state
  const [isTemplateFormOpen, setIsTemplateFormOpen] = useState(false);
  const [editingTemplateId, setEditingTemplateId] = useState<string | undefined>(undefined);

  // Campaign state
  const [isCampaignFormOpen, setIsCampaignFormOpen] = useState(false);
  const [editingCampaignId, setEditingCampaignId] = useState<string | undefined>(undefined);

  // Handle creating a new template
  const handleCreateTemplate = () => {
    setEditingTemplateId(undefined);
    setIsTemplateFormOpen(true);
  };

  // Handle editing a template
  const handleEditTemplate = (templateId: string) => {
    setEditingTemplateId(templateId);
    setIsTemplateFormOpen(true);
  };

  // Handle template form success
  const handleTemplateFormSuccess = () => {
    setIsTemplateFormOpen(false);
    setEditingTemplateId(undefined);
  };

  // Handle template form cancel
  const handleTemplateFormCancel = () => {
    setIsTemplateFormOpen(false);
    setEditingTemplateId(undefined);
  };

  // Handle creating a new campaign
  const handleCreateCampaign = () => {
    setEditingCampaignId(undefined);
    setIsCampaignFormOpen(true);
  };

  // Handle editing a campaign
  const handleEditCampaign = (campaignId: string) => {
    setEditingCampaignId(campaignId);
    setIsCampaignFormOpen(true);
  };

  // Handle campaign form success
  const handleCampaignFormSuccess = () => {
    setIsCampaignFormOpen(false);
    setEditingCampaignId(undefined);
  };

  // Handle campaign form cancel
  const handleCampaignFormCancel = () => {
    setIsCampaignFormOpen(false);
    setEditingCampaignId(undefined);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold tracking-tight">Communications</h1>

        <div className="flex space-x-2">
          {activeTab === 'templates' && (
            <Button onClick={handleCreateTemplate}>
              Create Template
            </Button>
          )}

          {activeTab === 'send' && (
            <Button>
              <Send className="h-4 w-4 mr-2" />
              Send Message
            </Button>
          )}

          {activeTab === 'campaigns' && (
            <Button onClick={handleCreateCampaign}>
              <Users className="h-4 w-4 mr-2" />
              Create Campaign
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="templates">
            <Mail className="h-4 w-4 mr-2" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="send">
            <Send className="h-4 w-4 mr-2" />
            Send
          </TabsTrigger>
          <TabsTrigger value="campaigns">
            <Users className="h-4 w-4 mr-2" />
            Campaigns
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
          <TabsTrigger value="settings">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="space-y-4">
          <TemplateList
            merchantId={merchantId}
            onCreateTemplate={handleCreateTemplate}
            onEditTemplate={handleEditTemplate}
          />
        </TabsContent>

        <TabsContent value="send" className="space-y-4">
          <SendCommunication merchantId={merchantId} />
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <CampaignList
            merchantId={merchantId}
            onCreateCampaign={handleCreateCampaign}
            onEditCampaign={handleEditCampaign}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <CommunicationAnalytics merchantId={merchantId} />
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <CommunicationSettings merchantId={merchantId} />
        </TabsContent>
      </Tabs>

      <Dialog open={isTemplateFormOpen} onOpenChange={setIsTemplateFormOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {editingTemplateId ? 'Edit Template' : 'Create Template'}
            </DialogTitle>
            <DialogDescription>
              {editingTemplateId
                ? 'Edit your communication template with customizable variables.'
                : 'Create a new communication template with customizable variables.'}
            </DialogDescription>
          </DialogHeader>

          <TemplateForm
            merchantId={merchantId}
            templateId={editingTemplateId}
            onSuccess={handleTemplateFormSuccess}
            onCancel={handleTemplateFormCancel}
          />
        </DialogContent>
      </Dialog>

      <Dialog open={isCampaignFormOpen} onOpenChange={setIsCampaignFormOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>
              {editingCampaignId ? 'Edit Campaign' : 'Create Campaign'}
            </DialogTitle>
            <DialogDescription>
              {editingCampaignId
                ? 'Edit your communication campaign settings.'
                : 'Create a new communication campaign to reach your customers.'}
            </DialogDescription>
          </DialogHeader>

          {/* <CampaignForm
            merchantId={merchantId}
            campaignId={editingCampaignId}
            onSuccess={handleCampaignFormSuccess}
            onCancel={handleCampaignFormCancel}
          /> */}
        </DialogContent>
      </Dialog>
    </div>
  );
}
