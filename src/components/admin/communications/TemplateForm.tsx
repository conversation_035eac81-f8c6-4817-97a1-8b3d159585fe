'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { communicationTemplateSchema } from '@/lib/validations/communicationSchema';
import {
  useGetTemplateByIdQuery,
  useCreateTemplateMutation,
  useUpdateTemplateMutation
} from '@/lib/redux/api/endpoints/communicationApi';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { Loader2, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import VariableSelector from './VariableSelector';
import TemplatePreview from './TemplatePreview';

// Form schema based on the Zod validation schema
const formSchema = communicationTemplateSchema;
type FormValues = z.infer<typeof formSchema>;

interface TemplateFormProps {
  merchantId: string;
  templateId?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function TemplateForm({ merchantId, templateId, onSuccess, onCancel }: TemplateFormProps) {
  // Using sonner toast
  const [activeTab, setActiveTab] = useState<'edit' | 'preview'>('edit');
  const [previewData, setPreviewData] = useState<Record<string, string>>({});

  // Fetch template if editing
  const { data: existingTemplate, isLoading: isLoadingTemplate, isError: templateError } =
    useGetTemplateByIdQuery(
      { merchantId, templateId: templateId! },
      { skip: !templateId }
    );

  // Mutations
  const [createTemplate, { isLoading: isCreating }] = useCreateTemplateMutation();
  const [updateTemplate, { isLoading: isUpdating }] = useUpdateTemplateMutation();

  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      type: 'email',
      subject: '',
      content: '',
      variables: [],
      isDefault: false,
      category: 'custom',
    },
  });

  // Update form values when template data is loaded
  useEffect(() => {
    if (existingTemplate) {
      form.reset({
        name: existingTemplate.name,
        type: existingTemplate.type,
        subject: existingTemplate.subject || '',
        content: existingTemplate.content,
        variables: existingTemplate.variables,
        isDefault: existingTemplate.isDefault,
        category: existingTemplate.category,
      });

      // Initialize preview data with empty values for all variables
      const initialPreviewData: Record<string, string> = {};
      existingTemplate.variables.forEach(variable => {
        initialPreviewData[variable] = `[${variable}]`;
      });
      setPreviewData(initialPreviewData);
    }
  }, [existingTemplate, form]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      if (templateId) {
        // Update existing template
        await updateTemplate({
          merchantId,
          templateId,
          ...values,
        }).unwrap();

        toast.success('Template updated', {
          description: 'The template has been updated successfully.',
        });
      } else {
        // Create new template
        await createTemplate({
          merchantId,
          ...values,
        }).unwrap();

        toast.success('Template created', {
          description: 'The template has been created successfully.',
        });
      }

      onSuccess();
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to save the template. Please try again.',
      });
    }
  };

  // Extract variables from content
  const extractVariables = () => {
    const content = form.getValues('content');
    const regex = /{{([^{}]+)}}/g;
    const matches = content.match(regex) || [];
    const variables = matches.map(match => match.replace(/{{|}}/g, ''));

    // Update form variables
    form.setValue('variables', [...new Set(variables)]);

    // Update preview data with empty values for new variables
    const newPreviewData = { ...previewData };
    variables.forEach(variable => {
      if (!newPreviewData[variable]) {
        newPreviewData[variable] = `[${variable}]`;
      }
    });
    setPreviewData(newPreviewData);
  };

  // Handle variable selection
  const handleVariableSelect = (variable: string) => {
    const contentField = form.getValues('content');
    const cursorPosition = document.getElementById('content')?.selectionStart || contentField.length;

    const newContent =
      contentField.substring(0, cursorPosition) +
      `{{${variable}}}` +
      contentField.substring(cursorPosition);

    form.setValue('content', newContent);
    extractVariables();
  };

  // Handle preview data change
  const handlePreviewDataChange = (variable: string, value: string) => {
    setPreviewData(prev => ({
      ...prev,
      [variable]: value,
    }));
  };

  // Loading state
  if (templateId && isLoadingTemplate) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Error state
  if (templateId && templateError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Failed to load template</h3>
        <p className="text-gray-500 mb-4">There was an error loading the template.</p>
        <Button onClick={onCancel}>Go Back</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'edit' | 'preview')}>
        <TabsList className="mb-4">
          <TabsTrigger value="edit">Edit Template</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>

        <TabsContent value="edit">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Template Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter template name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Type</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="email">Email</SelectItem>
                            <SelectItem value="sms">SMS</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="appointment_confirmation">Appointment Confirmation</SelectItem>
                            <SelectItem value="appointment_reminder">Appointment Reminder</SelectItem>
                            <SelectItem value="appointment_cancellation">Appointment Cancellation</SelectItem>
                            <SelectItem value="marketing">Marketing</SelectItem>
                            <SelectItem value="custom">Custom</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {form.watch('type') === 'email' && (
                    <FormField
                      control={form.control}
                      name="subject"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Subject</FormLabel>
                          <FormControl>
                            <Input placeholder="Enter email subject" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  <FormField
                    control={form.control}
                    name="isDefault"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Default Template</FormLabel>
                          <FormDescription>
                            Make this the default template for its category
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="space-y-6">
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Content</FormLabel>
                        <FormControl>
                          <Textarea
                            id="content"
                            placeholder="Enter template content"
                            className="min-h-[200px]"
                            {...field}
                            onBlur={() => extractVariables()}
                          />
                        </FormControl>
                        <FormDescription>
                          Use {{variable}} syntax to add variables
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <VariableSelector onSelectVariable={handleVariableSelect} />
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={onCancel}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isCreating || isUpdating}>
                  {isCreating || isUpdating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {templateId ? 'Updating...' : 'Creating...'}
                    </>
                  ) : (
                    templateId ? 'Update Template' : 'Create Template'
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="preview">
          <Card>
            <CardContent className="pt-6">
              <TemplatePreview
                template={{
                  type: form.watch('type'),
                  subject: form.watch('subject'),
                  content: form.watch('content'),
                  variables: form.watch('variables'),
                }}
                previewData={previewData}
                onPreviewDataChange={handlePreviewDataChange}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
