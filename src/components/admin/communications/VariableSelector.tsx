'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Tabs,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface VariableSelectorProps {
  onSelectVariable: (variable: string) => void;
}

// Define variable categories and their variables
const variableCategories = [
  {
    id: 'appointment',
    name: 'Appointment',
    variables: [
      { id: 'appointmentId', name: 'Appointment ID', description: 'Unique identifier for the appointment' },
      { id: 'appointmentDate', name: 'Date', description: 'Appointment date' },
      { id: 'appointmentTime', name: 'Time', description: 'Appointment time' },
      { id: 'appointmentDuration', name: 'Duration', description: 'Appointment duration in minutes' },
      { id: 'appointmentStatus', name: 'Status', description: 'Current status of the appointment' },
      { id: 'appointmentNotes', name: 'Notes', description: 'Additional notes for the appointment' },
    ],
  },
  {
    id: 'service',
    name: 'Service',
    variables: [
      { id: 'serviceName', name: 'Name', description: 'Name of the service' },
      { id: 'serviceDescription', name: 'Description', description: 'Description of the service' },
      { id: 'servicePrice', name: 'Price', description: 'Price of the service' },
      { id: 'serviceDuration', name: 'Duration', description: 'Duration of the service in minutes' },
      { id: 'serviceCategory', name: 'Category', description: 'Category of the service' },
    ],
  },
  {
    id: 'staff',
    name: 'Staff',
    variables: [
      { id: 'staffName', name: 'Name', description: 'Name of the staff member' },
      { id: 'staffEmail', name: 'Email', description: 'Email of the staff member' },
      { id: 'staffPhone', name: 'Phone', description: 'Phone number of the staff member' },
      { id: 'staffTitle', name: 'Title', description: 'Job title of the staff member' },
    ],
  },
  {
    id: 'customer',
    name: 'Customer',
    variables: [
      { id: 'customerName', name: 'Name', description: 'Customer\'s full name' },
      { id: 'customerFirstName', name: 'First Name', description: 'Customer\'s first name' },
      { id: 'customerLastName', name: 'Last Name', description: 'Customer\'s last name' },
      { id: 'customerEmail', name: 'Email', description: 'Customer\'s email address' },
      { id: 'customerPhone', name: 'Phone', description: 'Customer\'s phone number' },
      { id: 'customerAddress', name: 'Address', description: 'Customer\'s address' },
    ],
  },
  {
    id: 'merchant',
    name: 'Business',
    variables: [
      { id: 'merchantName', name: 'Name', description: 'Business name' },
      { id: 'merchantAddress', name: 'Address', description: 'Business address' },
      { id: 'merchantPhone', name: 'Phone', description: 'Business phone number' },
      { id: 'merchantEmail', name: 'Email', description: 'Business email address' },
      { id: 'merchantWebsite', name: 'Website', description: 'Business website URL' },
    ],
  },
];

export default function VariableSelector({ onSelectVariable }: VariableSelectorProps) {
  const [activeTab, setActiveTab] = useState('appointment');
  const [searchQuery, setSearchQuery] = useState('');
  
  // Filter variables based on search query
  const filteredVariables = searchQuery
    ? variableCategories.flatMap(category => 
        category.variables.filter(variable => 
          variable.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          variable.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          variable.id.toLowerCase().includes(searchQuery.toLowerCase())
        ).map(variable => ({ ...variable, category: category.name }))
      )
    : [];
  
  // Get variables for the active tab
  const activeCategory = variableCategories.find(category => category.id === activeTab);
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">Template Variables</CardTitle>
        <CardDescription>
          Insert variables into your template
        </CardDescription>
        <div className="relative mt-2">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search variables..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {searchQuery ? (
          <div className="p-4">
            <h3 className="text-sm font-medium mb-2">Search Results</h3>
            {filteredVariables.length > 0 ? (
              <div className="grid grid-cols-2 gap-2">
                {filteredVariables.map((variable) => (
                  <Button
                    key={variable.id}
                    variant="outline"
                    size="sm"
                    className="justify-start h-auto py-2 px-3"
                    onClick={() => onSelectVariable(variable.id)}
                  >
                    <div className="flex flex-col items-start text-left">
                      <span className="text-xs font-medium">{variable.name}</span>
                      <Badge variant="secondary" className="mt-1 text-xs">
                        {variable.category}
                      </Badge>
                    </div>
                  </Button>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No variables found</p>
            )}
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <div className="border-b">
              <TabsList className="w-full justify-start rounded-none border-b px-4">
                {variableCategories.map((category) => (
                  <TabsTrigger
                    key={category.id}
                    value={category.id}
                    className="data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none"
                  >
                    {category.name}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>
            
            {variableCategories.map((category) => (
              <TabsContent key={category.id} value={category.id} className="p-4">
                <div className="grid grid-cols-2 gap-2">
                  {category.variables.map((variable) => (
                    <Button
                      key={variable.id}
                      variant="outline"
                      size="sm"
                      className="justify-start h-auto py-2 px-3"
                      onClick={() => onSelectVariable(variable.id)}
                    >
                      <div className="flex flex-col items-start text-left">
                        <span className="text-xs font-medium">{variable.name}</span>
                        <span className="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                          {variable.description}
                        </span>
                      </div>
                    </Button>
                  ))}
                </div>
              </TabsContent>
            ))}
          </Tabs>
        )}
      </CardContent>
    </Card>
  );
}
