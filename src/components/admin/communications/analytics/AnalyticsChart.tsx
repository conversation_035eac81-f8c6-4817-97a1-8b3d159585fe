'use client';

import { useEffect, useState } from 'react';
import { 
  <PERSON><PERSON>hart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  BarChart,
  Bar
} from 'recharts';
import { format, parseISO } from 'date-fns';

interface AnalyticsChartProps {
  data?: any[];
  type?: 'email' | 'sms';
  metrics?: ('sent' | 'delivered' | 'opened' | 'clicked')[];
  chartType?: 'line' | 'bar';
}

export default function AnalyticsChart({ 
  data, 
  type,
  metrics = ['sent', 'delivered', 'opened', 'clicked'],
  chartType = 'line'
}: AnalyticsChartProps) {
  const [chartData, setChartData] = useState<any[]>([]);
  
  useEffect(() => {
    if (!data) return;
    
    // Process data for the chart
    const processedData = data.map(item => {
      const result: any = {
        date: item.date,
        formattedDate: format(parseISO(item.date), 'MMM d'),
      };
      
      // Add metrics based on type
      if (!type) {
        // Overall metrics
        result.sent = item.totalSent;
        result.delivered = item.delivered;
        result.opened = item.opened;
        result.clicked = item.clicked;
      } else if (type === 'email') {
        // Email metrics
        const emailData = item.email || item;
        result.sent = emailData.totalSent;
        result.delivered = emailData.delivered;
        result.opened = emailData.opened;
        result.clicked = emailData.clicked;
      } else if (type === 'sms') {
        // SMS metrics
        const smsData = item.sms || item;
        result.sent = smsData.totalSent;
        result.delivered = smsData.delivered;
        result.opened = smsData.opened;
        result.clicked = smsData.clicked;
      }
      
      return result;
    });
    
    setChartData(processedData);
  }, [data, type]);
  
  if (!data || data.length === 0) {
    return <div className="text-center py-8 text-muted-foreground">No data available for the selected period</div>;
  }
  
  // Define colors for metrics
  const metricColors = {
    sent: '#6366f1',
    delivered: '#10b981',
    opened: '#3b82f6',
    clicked: '#f59e0b',
  };
  
  // Define labels for metrics
  const metricLabels = {
    sent: 'Sent',
    delivered: 'Delivered',
    opened: 'Opened',
    clicked: 'Clicked',
  };
  
  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-md shadow-md">
          <p className="font-medium">{label}</p>
          <div className="space-y-1 mt-2">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center">
                <div 
                  className="w-3 h-3 rounded-full mr-2" 
                  style={{ backgroundColor: entry.color }}
                />
                <span className="text-sm">{entry.name}: {entry.value}</span>
              </div>
            ))}
          </div>
        </div>
      );
    }
    return null;
  };
  
  return (
    <div className="w-full h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        {chartType === 'line' ? (
          <LineChart
            data={chartData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="formattedDate" 
              tick={{ fontSize: 12 }}
            />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {metrics.includes('sent') && (
              <Line 
                type="monotone" 
                dataKey="sent" 
                name={metricLabels.sent}
                stroke={metricColors.sent} 
                activeDot={{ r: 8 }} 
              />
            )}
            {metrics.includes('delivered') && (
              <Line 
                type="monotone" 
                dataKey="delivered" 
                name={metricLabels.delivered}
                stroke={metricColors.delivered} 
              />
            )}
            {metrics.includes('opened') && (
              <Line 
                type="monotone" 
                dataKey="opened" 
                name={metricLabels.opened}
                stroke={metricColors.opened} 
              />
            )}
            {metrics.includes('clicked') && (
              <Line 
                type="monotone" 
                dataKey="clicked" 
                name={metricLabels.clicked}
                stroke={metricColors.clicked} 
              />
            )}
          </LineChart>
        ) : (
          <BarChart
            data={chartData}
            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis 
              dataKey="formattedDate" 
              tick={{ fontSize: 12 }}
            />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            {metrics.includes('sent') && (
              <Bar 
                dataKey="sent" 
                name={metricLabels.sent}
                fill={metricColors.sent} 
              />
            )}
            {metrics.includes('delivered') && (
              <Bar 
                dataKey="delivered" 
                name={metricLabels.delivered}
                fill={metricColors.delivered} 
              />
            )}
            {metrics.includes('opened') && (
              <Bar 
                dataKey="opened" 
                name={metricLabels.opened}
                fill={metricColors.opened} 
              />
            )}
            {metrics.includes('clicked') && (
              <Bar 
                dataKey="clicked" 
                name={metricLabels.clicked}
                fill={metricColors.clicked} 
              />
            )}
          </BarChart>
        )}
      </ResponsiveContainer>
    </div>
  );
}
