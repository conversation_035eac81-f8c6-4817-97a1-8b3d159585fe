'use client';

import { useState } from 'react';
import { useGetMerchantAnalyticsQuery } from '@/lib/redux/api/endpoints/communicationAnalyticsApi';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format, subDays, startOfMonth, endOfMonth } from 'date-fns';
import { Calendar as CalendarIcon, ChevronDown, Loader2, AlertCircle, Mail, MessageSquare, BarChart } from 'lucide-react';
import { cn } from '@/lib/utils';
import AnalyticsSummary from './AnalyticsSummary';
import AnalyticsChart from './AnalyticsChart';

interface CommunicationAnalyticsProps {
  merchantId: string;
}

export default function CommunicationAnalytics({ merchantId }: CommunicationAnalyticsProps) {
  const [dateRange, setDateRange] = useState<{
    from: Date;
    to: Date;
  }>({
    from: subDays(new Date(), 30),
    to: new Date(),
  });
  
  const [activeTab, setActiveTab] = useState<'overview' | 'email' | 'sms'>('overview');
  
  // Fetch analytics
  const { data: analytics, isLoading, isError, refetch } = useGetMerchantAnalyticsQuery({
    merchantId,
    startDate: dateRange.from.toISOString(),
    endDate: dateRange.to.toISOString(),
  });
  
  // Handle date range selection
  const handleDateRangeChange = (range: { from: Date; to: Date }) => {
    setDateRange(range);
  };
  
  // Handle preset date range selection
  const handlePresetRange = (days: number) => {
    setDateRange({
      from: subDays(new Date(), days),
      to: new Date(),
    });
  };
  
  // Handle month selection
  const handleMonthRange = () => {
    const today = new Date();
    setDateRange({
      from: startOfMonth(today),
      to: endOfMonth(today),
    });
  };
  
  // Render loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // Render error state
  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Failed to load analytics</h3>
        <p className="text-gray-500 mb-4">There was an error loading the communication analytics.</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-2xl font-bold">Communication Analytics</h2>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => handlePresetRange(7)}
              className={cn(
                dateRange.from.getTime() === subDays(new Date(), 7).setHours(0, 0, 0, 0) ? 'bg-primary text-primary-foreground' : ''
              )}
            >
              7 Days
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => handlePresetRange(30)}
              className={cn(
                dateRange.from.getTime() === subDays(new Date(), 30).setHours(0, 0, 0, 0) ? 'bg-primary text-primary-foreground' : ''
              )}
            >
              30 Days
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => handleMonthRange()}
              className={cn(
                dateRange.from.getTime() === startOfMonth(new Date()).setHours(0, 0, 0, 0) ? 'bg-primary text-primary-foreground' : ''
              )}
            >
              This Month
            </Button>
          </div>
          
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="sm" className="ml-auto">
                <CalendarIcon className="h-4 w-4 mr-2" />
                {format(dateRange.from, 'MMM d, yyyy')} - {format(dateRange.to, 'MMM d, yyyy')}
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="end">
              <Calendar
                mode="range"
                selected={dateRange}
                onSelect={handleDateRangeChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'overview' | 'email' | 'sms')}>
        <TabsList>
          <TabsTrigger value="overview">
            <BarChart className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="email">
            <Mail className="h-4 w-4 mr-2" />
            Email
          </TabsTrigger>
          <TabsTrigger value="sms">
            <MessageSquare className="h-4 w-4 mr-2" />
            SMS
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Sent</CardTitle>
                <CardDescription>All communications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics?.overall.totalSent}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics?.email.totalSent} emails, {analytics?.sms.totalSent} SMS
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Delivery Rate</CardTitle>
                <CardDescription>Successfully delivered</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(analytics?.overall.deliveryRate * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics?.overall.delivered} of {analytics?.overall.totalSent} delivered
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Engagement</CardTitle>
                <CardDescription>Opens and clicks</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(analytics?.overall.openRate * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics?.overall.opened} opens, {analytics?.overall.clicked} clicks
                </p>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader>
              <CardTitle>Communication Performance</CardTitle>
              <CardDescription>
                Overview of all communications from {format(dateRange.from, 'MMM d, yyyy')} to {format(dateRange.to, 'MMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsChart data={analytics?.daily} />
            </CardContent>
          </Card>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Email Performance</CardTitle>
                <CardDescription>
                  Key metrics for email communications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AnalyticsSummary analytics={analytics?.email} />
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader>
                <CardTitle>SMS Performance</CardTitle>
                <CardDescription>
                  Key metrics for SMS communications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AnalyticsSummary analytics={analytics?.sms} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="email" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Performance</CardTitle>
              <CardDescription>
                Detailed metrics for email communications from {format(dateRange.from, 'MMM d, yyyy')} to {format(dateRange.to, 'MMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsSummary analytics={analytics?.email} detailed />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>Email Engagement Over Time</CardTitle>
              <CardDescription>
                Daily email performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsChart 
                data={analytics?.daily} 
                type="email" 
                metrics={['sent', 'delivered', 'opened', 'clicked']} 
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="sms" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>SMS Performance</CardTitle>
              <CardDescription>
                Detailed metrics for SMS communications from {format(dateRange.from, 'MMM d, yyyy')} to {format(dateRange.to, 'MMM d, yyyy')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsSummary analytics={analytics?.sms} detailed />
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle>SMS Engagement Over Time</CardTitle>
              <CardDescription>
                Daily SMS performance metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AnalyticsChart 
                data={analytics?.daily} 
                type="sms" 
                metrics={['sent', 'delivered']} 
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
