'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  useGetCampaignByIdQuery, 
  useCreateCampaignMutation, 
  useUpdateCampaignMutation,
  useGetSegmentsQuery,
  useGetSegmentEmailsQuery,
  useGetSegmentPhonesQuery
} from '@/lib/redux/api/endpoints/campaignApi';
import { useGetTemplatesQuery } from '@/lib/redux/api/endpoints/communicationApi';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, AlertCircle, Mail, MessageSquare, Calendar, Users, Plus, X } from 'lucide-react';
import { toast } from 'sonner';
import { format, addDays } from 'date-fns';

// Form schema
const campaignFormSchema = z.object({
  name: z.string().min(1, 'Campaign name is required'),
  type: z.enum(['email', 'sms'], {
    errorMap: () => ({ message: 'Type must be either email or sms' }),
  }),
  templateId: z.string().min(1, 'Template is required'),
  segmentId: z.string().optional(),
  scheduledFor: z.string().optional(),
  status: z.enum(['draft', 'scheduled']).default('draft'),
});

type FormValues = z.infer<typeof campaignFormSchema>;

interface CampaignFormProps {
  merchantId: string;
  campaignId?: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export default function CampaignForm({ 
  merchantId, 
  campaignId, 
  onSuccess, 
  onCancel 
}: CampaignFormProps) {
  const [recipientSource, setRecipientSource] = useState<'segment' | 'manual'>('segment');
  const [manualRecipients, setManualRecipients] = useState<string[]>([]);
  const [newRecipient, setNewRecipient] = useState('');
  
  // Fetch campaign if editing
  const { data: existingCampaign, isLoading: isLoadingCampaign, isError: campaignError } = 
    useGetCampaignByIdQuery(
      { merchantId, campaignId: campaignId! },
      { skip: !campaignId }
    );
  
  // Fetch templates
  const { data: templates, isLoading: isLoadingTemplates } = useGetTemplatesQuery({
    merchantId,
  });
  
  // Fetch segments
  const { data: segments, isLoading: isLoadingSegments } = useGetSegmentsQuery({
    merchantId,
  });
  
  // Mutations
  const [createCampaign, { isLoading: isCreating }] = useCreateCampaignMutation();
  const [updateCampaign, { isLoading: isUpdating }] = useUpdateCampaignMutation();
  
  // Form setup
  const form = useForm<FormValues>({
    resolver: zodResolver(campaignFormSchema),
    defaultValues: {
      name: '',
      type: 'email',
      templateId: '',
      segmentId: '',
      scheduledFor: '',
      status: 'draft',
    },
  });
  
  // Watch form values
  const campaignType = form.watch('type');
  const segmentId = form.watch('segmentId');
  
  // Fetch segment emails or phones based on campaign type and selected segment
  const { data: segmentEmails, isLoading: isLoadingEmails } = useGetSegmentEmailsQuery(
    { merchantId, segmentId: segmentId! },
    { skip: !segmentId || campaignType !== 'email' || recipientSource !== 'segment' }
  );
  
  const { data: segmentPhones, isLoading: isLoadingPhones } = useGetSegmentPhonesQuery(
    { merchantId, segmentId: segmentId! },
    { skip: !segmentId || campaignType !== 'sms' || recipientSource !== 'segment' }
  );
  
  // Update form values when campaign data is loaded
  useEffect(() => {
    if (existingCampaign) {
      form.reset({
        name: existingCampaign.name,
        type: existingCampaign.type as 'email' | 'sms',
        templateId: existingCampaign.templateId,
        segmentId: existingCampaign.metadata?.segmentId || '',
        scheduledFor: existingCampaign.scheduledFor ? format(new Date(existingCampaign.scheduledFor), 'yyyy-MM-dd\'T\'HH:mm') : '',
        status: existingCampaign.status as 'draft' | 'scheduled',
      });
      
      // Determine if recipients were from a segment or manual
      if (existingCampaign.metadata?.segmentId) {
        setRecipientSource('segment');
      } else {
        setRecipientSource('manual');
        setManualRecipients(existingCampaign.recipients || []);
      }
    }
  }, [existingCampaign, form]);
  
  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    try {
      // Get recipients based on source
      let recipients: string[] = [];
      
      if (recipientSource === 'segment' && values.segmentId) {
        if (values.type === 'email') {
          recipients = segmentEmails || [];
        } else {
          recipients = segmentPhones || [];
        }
      } else {
        recipients = manualRecipients;
      }
      
      if (recipients.length === 0) {
        toast.error('Error', {
          description: 'At least one recipient is required.',
        });
        return;
      }
      
      const campaignData = {
        name: values.name,
        type: values.type,
        templateId: values.templateId,
        recipients,
        scheduledFor: values.scheduledFor ? new Date(values.scheduledFor).toISOString() : undefined,
        status: values.scheduledFor ? 'scheduled' : 'draft',
        metadata: {
          segmentId: recipientSource === 'segment' ? values.segmentId : undefined,
        },
      };
      
      if (campaignId) {
        // Update existing campaign
        await updateCampaign({
          merchantId,
          campaignId,
          ...campaignData,
        }).unwrap();
        
        toast.success('Campaign updated', {
          description: 'The campaign has been updated successfully.',
        });
      } else {
        // Create new campaign
        await createCampaign({
          merchantId,
          ...campaignData,
        }).unwrap();
        
        toast.success('Campaign created', {
          description: 'The campaign has been created successfully.',
        });
      }
      
      onSuccess();
    } catch (error) {
      toast.error('Error', {
        description: 'Failed to save the campaign. Please try again.',
      });
    }
  };
  
  // Handle adding a manual recipient
  const handleAddRecipient = () => {
    if (!newRecipient) return;
    
    // Validate email or phone based on campaign type
    if (campaignType === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(newRecipient)) {
        toast.error('Invalid email', {
          description: 'Please enter a valid email address.',
        });
        return;
      }
    } else {
      const phoneRegex = /^\+?[0-9]{10,15}$/;
      if (!phoneRegex.test(newRecipient)) {
        toast.error('Invalid phone number', {
          description: 'Please enter a valid phone number.',
        });
        return;
      }
    }
    
    if (!manualRecipients.includes(newRecipient)) {
      setManualRecipients([...manualRecipients, newRecipient]);
    }
    setNewRecipient('');
  };
  
  // Handle removing a manual recipient
  const handleRemoveRecipient = (recipient: string) => {
    setManualRecipients(manualRecipients.filter(r => r !== recipient));
  };
  
  // Loading state
  if (campaignId && isLoadingCampaign) {
    return (
      <div className="flex justify-center items-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  // Error state
  if (campaignId && campaignError) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h3 className="text-lg font-semibold mb-2">Failed to load campaign</h3>
        <p className="text-gray-500 mb-4">There was an error loading the campaign.</p>
        <Button onClick={onCancel}>Go Back</Button>
      </div>
    );
  }
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Campaign Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter campaign name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Campaign Type</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={!!campaignId} // Can't change type when editing
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="email">
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2" />
                          <span>Email Campaign</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="sms">
                        <div className="flex items-center">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          <span>SMS Campaign</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="templateId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Template</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a template" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {isLoadingTemplates ? (
                        <SelectItem value="loading" disabled>
                          Loading templates...
                        </SelectItem>
                      ) : templates && templates.length > 0 ? (
                        templates
                          .filter(template => template.type === campaignType)
                          .map((template) => (
                            <SelectItem key={template.id} value={template.id!}>
                              {template.name}
                            </SelectItem>
                          ))
                      ) : (
                        <SelectItem value="none" disabled>
                          No templates available
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="scheduledFor"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Schedule (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      min={format(new Date(), 'yyyy-MM-dd\'T\'HH:mm')}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Leave empty to save as draft
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <div className="space-y-6">
            <div>
              <h3 className="text-sm font-medium mb-2">Recipients</h3>
              <Tabs value={recipientSource} onValueChange={(value) => setRecipientSource(value as 'segment' | 'manual')}>
                <TabsList className="w-full">
                  <TabsTrigger value="segment" className="flex-1">
                    <Users className="h-4 w-4 mr-2" />
                    From Segment
                  </TabsTrigger>
                  <TabsTrigger value="manual" className="flex-1">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Manually
                  </TabsTrigger>
                </TabsList>
                
                <TabsContent value="segment" className="mt-4">
                  <FormField
                    control={form.control}
                    name="segmentId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Customer Segment</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a segment" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {isLoadingSegments ? (
                              <SelectItem value="loading" disabled>
                                Loading segments...
                              </SelectItem>
                            ) : segments && segments.length > 0 ? (
                              segments.map((segment) => (
                                <SelectItem key={segment.id} value={segment.id!}>
                                  {segment.name}
                                </SelectItem>
                              ))
                            ) : (
                              <SelectItem value="none" disabled>
                                No segments available
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select a customer segment to target
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  {segmentId && (
                    <Card className="mt-4">
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm">Recipient Preview</CardTitle>
                      </CardHeader>
                      <CardContent>
                        {campaignType === 'email' ? (
                          isLoadingEmails ? (
                            <div className="flex items-center">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              <span className="text-sm">Loading email addresses...</span>
                            </div>
                          ) : segmentEmails && segmentEmails.length > 0 ? (
                            <div className="text-sm">
                              <span className="font-medium">{segmentEmails.length}</span> email recipients
                              <div className="mt-2 text-xs text-muted-foreground">
                                {segmentEmails.slice(0, 3).map((email, index) => (
                                  <div key={index}>{email}</div>
                                ))}
                                {segmentEmails.length > 3 && (
                                  <div>And {segmentEmails.length - 3} more...</div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground">
                              No email addresses found in this segment
                            </div>
                          )
                        ) : (
                          isLoadingPhones ? (
                            <div className="flex items-center">
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              <span className="text-sm">Loading phone numbers...</span>
                            </div>
                          ) : segmentPhones && segmentPhones.length > 0 ? (
                            <div className="text-sm">
                              <span className="font-medium">{segmentPhones.length}</span> SMS recipients
                              <div className="mt-2 text-xs text-muted-foreground">
                                {segmentPhones.slice(0, 3).map((phone, index) => (
                                  <div key={index}>{phone}</div>
                                ))}
                                {segmentPhones.length > 3 && (
                                  <div>And {segmentPhones.length - 3} more...</div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <div className="text-sm text-muted-foreground">
                              No phone numbers found in this segment
                            </div>
                          )
                        )}
                      </CardContent>
                    </Card>
                  )}
                </TabsContent>
                
                <TabsContent value="manual" className="mt-4">
                  <div className="space-y-4">
                    <div className="flex space-x-2">
                      <Input
                        placeholder={campaignType === 'email' ? "Enter email address" : "Enter phone number"}
                        value={newRecipient}
                        onChange={(e) => setNewRecipient(e.target.value)}
                      />
                      <Button type="button" onClick={handleAddRecipient}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add
                      </Button>
                    </div>
                    
                    <div className="border rounded-md p-4">
                      <h4 className="text-sm font-medium mb-2">
                        {manualRecipients.length} {campaignType === 'email' ? 'Email' : 'SMS'} Recipients
                      </h4>
                      
                      {manualRecipients.length > 0 ? (
                        <div className="space-y-2 max-h-[200px] overflow-y-auto">
                          {manualRecipients.map((recipient, index) => (
                            <div key={index} className="flex justify-between items-center text-sm p-2 bg-gray-50 rounded">
                              <span>{recipient}</span>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => handleRemoveRecipient(recipient)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="text-sm text-muted-foreground">
                          No recipients added yet
                        </div>
                      )}
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </div>
        
        <div className="flex justify-end space-x-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isCreating || isUpdating}>
            {isCreating || isUpdating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {campaignId ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              campaignId ? 'Update Campaign' : 'Create Campaign'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
