'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Mail, MessageSquare, Settings, Check, AlertCircle, Loader2 } from 'lucide-react';
import {
  emailConfig,
  smsConfig,
  isEmailProviderConfigured,
  isSMSProviderConfigured,
  getEmailProviderDescription,
  getSMSProviderDescription
} from '@/lib/communications';

// Email settings schema
const emailSettingsSchema = z.object({
  provider: z.enum(['brevo', 'mailgun', 'mock']),
  brevoApiKey: z.string().optional(),
  emailSenderAddress: z.string().email().optional(),
  emailSenderName: z.string().optional(),

  mailgunApiKey: z.string().optional(),
  mailgunDomain: z.string().optional(),
  mailgunFromEmail: z.string().email().optional(),
  defaultFromEmail: z.string().email(),
  defaultReplyToEmail: z.string().email(),
}).refine(data => {
  if (data.provider === 'brevo') {
    return !!data.brevoApiKey && !!data.emailSenderAddress;
  }
  return true;
}, {
  message: "Brevo API key and from email are required when using Brevo",
  path: ['brevoApiKey'],
}).refine(data => {
  if (data.provider === 'mailgun') {
    return !!data.mailgunApiKey && !!data.mailgunDomain && !!data.mailgunFromEmail;
  }
  return true;
}, {
  message: "Mailgun API key, domain, and from email are required when using Mailgun",
  path: ['mailgunApiKey'],
});

// SMS settings schema
const smsSettingsSchema = z.object({
  provider: z.enum(['mock']),
  defaultSender: z.string(),

  // Keep these fields for future implementation
  twilioAccountSid: z.string().optional(),
  twilioAuthToken: z.string().optional(),
  twilioPhoneNumber: z.string().optional(),
  nexmoApiKey: z.string().optional(),
  nexmoApiSecret: z.string().optional(),
  nexmoSender: z.string().optional(),
});

interface CommunicationSettingsProps {
  merchantId: string;
}

export default function CommunicationSettings({ merchantId }: CommunicationSettingsProps) {
  const [activeTab, setActiveTab] = useState<'email' | 'sms'>('email');
  const [isSaving, setIsSaving] = useState(false);
  const [isTestingBrevo, setIsTestingBrevo] = useState(false);

  const [isTestingMailgun, setIsTestingMailgun] = useState(false);
  // SMS provider testing states will be added in the future

  // Email form
  const emailForm = useForm<z.infer<typeof emailSettingsSchema>>({
    resolver: zodResolver(emailSettingsSchema),
    defaultValues: {
      provider: emailConfig.provider as 'brevo' | 'mailgun' | 'mock',
      brevoApiKey: emailConfig.brevo?.apiKey || '',
      emailSenderAddress: emailConfig.brevo?.fromEmail || '',
      emailSenderName: emailConfig.brevo?.fromName || '',

      mailgunApiKey: emailConfig.mailgun.apiKey,
      mailgunDomain: emailConfig.mailgun.domain,
      mailgunFromEmail: emailConfig.mailgun.fromEmail,
      defaultFromEmail: emailConfig.defaultFromEmail,
      defaultReplyToEmail: emailConfig.defaultReplyToEmail,
    },
  });

  // SMS form
  const smsForm = useForm<z.infer<typeof smsSettingsSchema>>({
    resolver: zodResolver(smsSettingsSchema),
    defaultValues: {
      provider: 'mock',
      defaultSender: smsConfig.defaultSender,

      // Keep these fields for future implementation
      twilioAccountSid: '',
      twilioAuthToken: '',
      twilioPhoneNumber: '',
      nexmoApiKey: '',
      nexmoApiSecret: '',
      nexmoSender: '',
    },
  });

  // Watch form values
  const emailProvider = emailForm.watch('provider');

  // Handle saving email settings
  const onSaveEmailSettings = async (_data: z.infer<typeof emailSettingsSchema>) => {
    setIsSaving(true);

    try {
      // In a real implementation, this would save the settings to the database
      // and update environment variables

      // For now, just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('Email settings saved', {
        description: 'Your email provider settings have been saved successfully.',
      });
    } catch (_error) {
      toast.error('Error', {
        description: 'Failed to save email settings. Please try again.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle saving SMS settings
  const onSaveSMSSettings = async (_data: z.infer<typeof smsSettingsSchema>) => {
    setIsSaving(true);

    try {
      // In a real implementation, this would save the settings to the database
      // and update environment variables

      // For now, just simulate a save
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast.success('SMS settings saved', {
        description: 'Your SMS provider settings have been saved successfully.',
      });
    } catch (_error) {
      toast.error('Error', {
        description: 'Failed to save SMS settings. Please try again.',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle testing Brevo
  const handleTestBrevo = async () => {
    setIsTestingBrevo(true);

    try {
      // In a real implementation, this would send a test email using Brevo

      // For now, just simulate a test
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Brevo test successful', {
        description: 'A test email was sent successfully using Brevo.',
      });
    } catch (_error) {
      toast.error('Error', {
        description: 'Failed to send test email using Brevo. Please check your settings.',
      });
    } finally {
      setIsTestingBrevo(false);
    }
  };



  // Handle testing Mailgun
  const handleTestMailgun = async () => {
    setIsTestingMailgun(true);

    try {
      // In a real implementation, this would send a test email using Mailgun

      // For now, just simulate a test
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast.success('Mailgun test successful', {
        description: 'A test email was sent successfully using Mailgun.',
      });
    } catch (_error) {
      toast.error('Error', {
        description: 'Failed to send test email using Mailgun. Please check your settings.',
      });
    } finally {
      setIsTestingMailgun(false);
    }
  };

  // SMS provider testing functions will be added in the future

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Communication Settings</h2>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'email' | 'sms')}>
        <TabsList>
          <TabsTrigger value="email">
            <Mail className="h-4 w-4 mr-2" />
            Email Settings
          </TabsTrigger>
          <TabsTrigger value="sms">
            <MessageSquare className="h-4 w-4 mr-2" />
            SMS Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="email" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>Email Provider Configuration</CardTitle>
              <CardDescription>
                Configure your email provider for sending emails to customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...emailForm}>
                <form onSubmit={emailForm.handleSubmit(onSaveEmailSettings)} className="space-y-6">
                  <FormField
                    control={emailForm.control}
                    name="provider"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email Provider</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select email provider" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="brevo">Brevo</SelectItem>

                            <SelectItem value="mailgun">Mailgun</SelectItem>
                            <SelectItem value="mock">Mock (Development Only)</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the email provider you want to use for sending emails
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {emailProvider === 'brevo' && (
                    <div className="space-y-4">
                      <FormField
                        control={emailForm.control}
                        name="brevoApiKey"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Brevo API Key</FormLabel>
                            <FormControl>
                              <Input type="password" placeholder="Enter your Brevo API key" {...field} />
                            </FormControl>
                            <FormDescription>
                              Your Brevo API key for authentication
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="emailSenderAddress"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Sender Address</FormLabel>
                            <FormControl>
                              <Input placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormDescription>
                              The email address that will appear as the sender
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="emailSenderName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Sender Name</FormLabel>
                            <FormControl>
                              <Input placeholder="Company Name" {...field} />
                            </FormControl>
                            <FormDescription>
                              The name that will appear as the sender
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleTestBrevo}
                          disabled={isTestingBrevo || !emailForm.getValues('brevoApiKey')}
                        >
                          {isTestingBrevo ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Testing...
                            </>
                          ) : (
                            <>
                              <Mail className="mr-2 h-4 w-4" />
                              Test Brevo Connection
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  )}



                  {emailProvider === 'mailgun' && (
                    <div className="space-y-4">
                      <FormField
                        control={emailForm.control}
                        name="mailgunApiKey"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mailgun API Key</FormLabel>
                            <FormControl>
                              <Input type="password" placeholder="Enter your Mailgun API key" {...field} />
                            </FormControl>
                            <FormDescription>
                              Your Mailgun API key for authentication
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="mailgunDomain"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mailgun Domain</FormLabel>
                            <FormControl>
                              <Input placeholder="mg.example.com" {...field} />
                            </FormControl>
                            <FormDescription>
                              Your Mailgun domain for sending emails
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={emailForm.control}
                        name="mailgunFromEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Mailgun From Email</FormLabel>
                            <FormControl>
                              <Input placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormDescription>
                              The email address that will appear as the sender
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="pt-2">
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleTestMailgun}
                          disabled={
                            isTestingMailgun ||
                            !emailForm.getValues('mailgunApiKey') ||
                            !emailForm.getValues('mailgunDomain')
                          }
                        >
                          {isTestingMailgun ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Testing...
                            </>
                          ) : (
                            <>
                              <Mail className="mr-2 h-4 w-4" />
                              Test Mailgun Connection
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  )}

                  <Separator />

                  <div className="space-y-4">
                    <FormField
                      control={emailForm.control}
                      name="defaultFromEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default From Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormDescription>
                            The default email address that will appear as the sender
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={emailForm.control}
                      name="defaultReplyToEmail"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Reply-To Email</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormDescription>
                            The default email address that customers can reply to
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Settings className="mr-2 h-4 w-4" />
                          Save Email Settings
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <div className="w-full">
                <div className="flex items-center">
                  {isEmailProviderConfigured() ? (
                    <div className="flex items-center text-green-600">
                      <Check className="h-4 w-4 mr-2" />
                      <span className="font-medium">Email provider configured:</span>
                      <span className="ml-1">{getEmailProviderDescription()}</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-amber-600">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      <span>Email provider not properly configured. Using mock provider for development.</span>
                    </div>
                  )}
                </div>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="sms" className="space-y-6 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>SMS Provider Configuration</CardTitle>
              <CardDescription>
                Configure your SMS provider for sending text messages to customers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...smsForm}>
                <form onSubmit={smsForm.handleSubmit(onSaveSMSSettings)} className="space-y-6">
                  <FormField
                    control={smsForm.control}
                    name="provider"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>SMS Provider</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select SMS provider" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="mock">Mock (Development Only)</SelectItem>
                            {/* Real SMS providers will be added in the future */}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Select the SMS provider you want to use for sending text messages
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* SMS provider configuration UI will be added in the future */}
                  <div className="p-4 bg-gray-50 rounded-md text-gray-500 text-sm">
                    <p>SMS provider configuration is currently disabled.</p>
                    <p className="mt-2">Real SMS providers (Twilio, Vonage/Nexmo) will be implemented in a future update.</p>
                    <p className="mt-2">For now, only the mock provider is available for development and testing.</p>
                  </div>

                  <Separator />

                  <div className="space-y-4">
                    <FormField
                      control={smsForm.control}
                      name="defaultSender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Default Sender ID</FormLabel>
                          <FormControl>
                            <Input placeholder="COMPANY" {...field} />
                          </FormControl>
                          <FormDescription>
                            The default sender ID that will appear on SMS messages
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="flex justify-end">
                    <Button type="submit" disabled={isSaving}>
                      {isSaving ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Settings className="mr-2 h-4 w-4" />
                          Save SMS Settings
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </CardContent>
            <CardFooter className="border-t pt-4">
              <div className="w-full">
                <div className="flex items-center">
                  {isSMSProviderConfigured() ? (
                    <div className="flex items-center text-green-600">
                      <Check className="h-4 w-4 mr-2" />
                      <span className="font-medium">SMS provider configured:</span>
                      <span className="ml-1">{getSMSProviderDescription()}</span>
                    </div>
                  ) : (
                    <div className="flex items-center text-amber-600">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      <span>SMS provider not properly configured. Using mock provider for development.</span>
                    </div>
                  )}
                </div>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
