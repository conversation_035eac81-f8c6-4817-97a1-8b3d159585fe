'use client';

import { useGetMerchantsQuery } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import {
  useGetMenuItemsQuery,
  useGetReservationsQuery,
  useGetTablesQuery
} from '@/lib/redux/api/endpoints/restaurant/restaurantApi';
import { useState } from 'react';
import Link from 'next/link';
import { format, parseISO, isToday } from 'date-fns';

interface RestaurantDashboardProps {
  merchantId: string;
}

export default function RestaurantDashboard({ merchantId }: RestaurantDashboardProps) {
  const { data: merchant, isLoading: merchantLoading } = useGetMerchantsQuery({
    page: 1,
    limit: 50,
    sort_by: 'created_at',
    sort_order: 'desc'
  }, {
    selectFromResult: (result) => ({
      ...result,
      data: result.data?.data?.find(m => m.id === merchantId && m.type === 'restaurant')
    })
  });

  const { data: menuItems, isLoading: menuLoading } = useGetMenuItemsQuery(merchantId);
  const { data: reservations, isLoading: reservationsLoading } = useGetReservationsQuery(merchantId);
  const { data: tables, isLoading: tablesLoading } = useGetTablesQuery(merchantId);

  const [activeTab, setActiveTab] = useState<'overview' | 'menu' | 'reservations' | 'orders'>('overview');

  // Filter today's reservations
  const todayReservations = reservations?.filter(reservation =>
    isToday(parseISO(reservation.date)) &&
    reservation.status !== 'cancelled'
  ) || [];

  // Get table name by ID
  const getTableName = (tableId: string) => {
    const table = tables?.find(t => t.id === tableId);
    return table ? `Table ${table.number}` : 'Unknown Table';
  };

  if (merchantLoading || menuLoading || reservationsLoading || tablesLoading) {
    return <div className="p-4">Loading restaurant data...</div>;
  }

  if (!merchant) {
    return <div className="p-4">Restaurant not found or not a restaurant type merchant.</div>;
  }

  return (
    <div className="bg-white rounded-lg shadow-sm overflow-hidden">
      <div className="p-6 border-b">
        <div className="flex items-center">
          <img
            src={merchant.logo || '/placeholder-image.svg'}
            alt={merchant.name}
            className="h-16 w-16 rounded-full mr-4 object-cover"
            onError={(e) => {
              e.currentTarget.src = '/placeholder-image.svg';
            }}
          />
          <div>
            <h1 className="text-2xl font-bold">{merchant.name}</h1>
            <p className="text-gray-600">{merchant.address}</p>
          </div>
        </div>
      </div>

      <div className="border-b">
        <nav className="flex">
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'overview' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('overview')}
          >
            Overview
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'menu' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('menu')}
          >
            Menu
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'reservations' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('reservations')}
          >
            Reservations
          </button>
          <button
            className={`px-4 py-3 font-medium ${
              activeTab === 'orders' ? 'text-blue-600 border-b-2 border-blue-600' : 'text-gray-600'
            }`}
            onClick={() => setActiveTab('orders')}
          >
            Orders
          </button>
        </nav>
      </div>

      <div className="p-6">
        {activeTab === 'overview' && (
          <div>
            <h2 className="text-lg font-semibold mb-4">Restaurant Overview</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Menu Items</h3>
                <p className="text-2xl font-bold">{menuItems?.length || 0}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Today's Reservations</h3>
                <p className="text-2xl font-bold">{todayReservations.length}</p>
              </div>
              <div className="p-4 border rounded-md">
                <h3 className="font-medium text-gray-500">Tables</h3>
                <p className="text-2xl font-bold">{tables?.length || 0}</p>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="font-medium mb-2">Restaurant Settings</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Cuisine Type</h4>
                  <p>{merchant.settings?.cuisineType || 'Not specified'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Price Range</h4>
                  <p>{merchant.settings?.priceRange || 'Not specified'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Seating Capacity</h4>
                  <p>{merchant.settings?.seatingCapacity || 'Not specified'}</p>
                </div>
                <div className="p-4 border rounded-md">
                  <h4 className="text-sm font-medium text-gray-500">Features</h4>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {merchant.settings?.reservationEnabled && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                        Reservations
                      </span>
                    )}
                    {merchant.settings?.deliveryEnabled && (
                      <span className="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                        Delivery
                      </span>
                    )}
                    {merchant.settings?.takeoutEnabled && (
                      <span className="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                        Takeout
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'menu' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Menu Items</h2>
              <Link
                href={`/admin/merchants/restaurant/${merchantId}/menu/create`}
                className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
              >
                Add Item
              </Link>
            </div>

            {menuItems && menuItems.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Name
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {menuItems.map((item) => (
                      <tr key={item.id}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {item.image && (
                              <img
                                src={item.image}
                                alt={item.name}
                                className="h-10 w-10 rounded-full mr-3 object-cover"
                              />
                            )}
                            <div className="font-medium text-gray-900">{item.name}</div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {item.category}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          ${item.price.toFixed(2)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            item.available
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {item.available ? 'Available' : 'Unavailable'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/admin/merchants/restaurant/${merchantId}/menu/${item.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900 mr-3"
                          >
                            Edit
                          </Link>
                          <button className="text-red-600 hover:text-red-900">
                            Delete
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No menu items found. Add your first menu item to get started.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'reservations' && (
          <div>
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold">Reservations</h2>
              <div className="flex space-x-2">
                <Link
                  href={`/admin/merchants/restaurant/${merchantId}/tables`}
                  className="px-3 py-1 bg-gray-600 text-white text-sm rounded-md hover:bg-gray-700"
                >
                  Manage Tables
                </Link>
                <Link
                  href={`/admin/merchants/restaurant/${merchantId}/reservations`}
                  className="px-3 py-1 bg-blue-600 text-white text-sm rounded-md hover:bg-blue-700"
                >
                  View All Reservations
                </Link>
              </div>
            </div>

            {reservations && reservations.length > 0 ? (
              <div>
                <h3 className="font-medium mb-2">Today's Reservations</h3>
                {todayReservations.length > 0 ? (
                  <div className="overflow-x-auto mb-6">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Time
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Customer
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Table
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Party Size
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {todayReservations
                          .sort((a, b) => a.time.localeCompare(b.time))
                          .map((reservation) => (
                            <tr key={reservation.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {reservation.time}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{reservation.customerName}</div>
                                <div className="text-sm text-gray-500">{reservation.customerEmail}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {getTableName(reservation.tableId)}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {reservation.partySize}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                  ${reservation.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                    reservation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                    reservation.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                                    'bg-blue-100 text-blue-800'}`}>
                                  {reservation.status.charAt(0).toUpperCase() + reservation.status.slice(1)}
                                </span>
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500 mb-6">
                    <p>No reservations for today.</p>
                  </div>
                )}

                <h3 className="font-medium mb-2">Upcoming Reservations</h3>
                {reservations.filter(r =>
                  !isToday(parseISO(r.date)) &&
                  parseISO(r.date) > new Date() &&
                  r.status !== 'cancelled'
                ).length > 0 ? (
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Time
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Customer
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Party Size
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {reservations
                          .filter(r =>
                            !isToday(parseISO(r.date)) &&
                            parseISO(r.date) > new Date() &&
                            r.status !== 'cancelled'
                          )
                          .sort((a, b) => parseISO(a.date).getTime() - parseISO(b.date).getTime())
                          .slice(0, 5) // Show only the next 5 upcoming reservations
                          .map((reservation) => (
                            <tr key={reservation.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {format(parseISO(reservation.date), 'MMM d, yyyy')}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {reservation.time}
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm font-medium text-gray-900">{reservation.customerName}</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {reservation.partySize}
                              </td>
                            </tr>
                          ))}
                      </tbody>
                    </table>
                  </div>
                ) : (
                  <div className="text-center py-4 text-gray-500">
                    <p>No upcoming reservations.</p>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>No reservations found. Create your first reservation to get started.</p>
                <div className="mt-4">
                  <Link
                    href={`/admin/merchants/restaurant/${merchantId}/reservations`}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                  >
                    Manage Reservations
                  </Link>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'orders' && (
          <div className="text-center py-8 text-gray-500">
            <p>Order management coming soon.</p>
          </div>
        )}
      </div>
    </div>
  );
}
