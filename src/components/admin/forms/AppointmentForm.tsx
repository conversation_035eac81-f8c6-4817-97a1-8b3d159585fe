'use client';

import { useState, useEffect } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Appointment, appointmentSchema } from '@/lib/validations/appointmentSchema';
import {
  useCreateAppointmentMutation,
  useUpdateAppointmentMutation,
  useGetServicesQuery,
  useGetStaffQuery,
  useGetAvailableTimeSlotsQuery
} from '@/lib/redux/api/endpoints/serviceApi';

interface AppointmentFormProps {
  merchantId: string;
  initialData?: Appointment & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function AppointmentForm({
  merchantId,
  initialData,
  onSuccess,
  mode
}: AppointmentFormProps) {
  const [createAppointment, { isLoading: isCreating }] = useCreateAppointmentMutation();
  const [updateAppointment, { isLoading: isUpdating }] = useUpdateAppointmentMutation();

  const { data: services } = useGetServicesQuery(merchantId);
  const { data: staff } = useGetStaffQuery(merchantId);

  const [selectedDate, setSelectedDate] = useState<string>(
    initialData?.date || new Date().toISOString().split('T')[0]
  );
  const [selectedServiceId, setSelectedServiceId] = useState<string>(
    initialData?.serviceId || ''
  );

  const { data: timeSlots, isLoading: isLoadingTimeSlots } = useGetAvailableTimeSlotsQuery({
    merchantId,
    serviceId: selectedServiceId,
    date: selectedDate,
  }, { skip: !selectedServiceId || !selectedDate });

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isLoading = isCreating || isUpdating || isLoadingTimeSlots;

  // Get selected service
  const selectedService = services?.find(service => service.id === selectedServiceId);

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isDirty }
  } = useForm<Appointment>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: initialData || {
      serviceId: '',
      staffId: '',
      userId: '',
      date: new Date().toISOString().split('T')[0],
      startTime: '',
      endTime: '',
      status: 'scheduled',
      notes: '',
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      numberOfPeople: 1,
    }
  });

  const watchServiceId = watch('serviceId');
  const watchDate = watch('date');
  const watchStartTime = watch('startTime');

  // Update selected service and date when form values change
  useEffect(() => {
    if (watchServiceId && watchServiceId !== selectedServiceId) {
      setSelectedServiceId(watchServiceId);
    }

    if (watchDate && watchDate !== selectedDate) {
      setSelectedDate(watchDate);
    }
  }, [watchServiceId, watchDate, selectedServiceId, selectedDate]);

  // Update end time when start time changes
  useEffect(() => {
    if (watchStartTime && selectedService) {
      // Calculate end time based on service duration
      const [hours, minutes] = watchStartTime.split(':').map(Number);
      const startDate = new Date();
      startDate.setHours(hours, minutes, 0, 0);

      const endDate = new Date(startDate);
      endDate.setMinutes(endDate.getMinutes() + selectedService.duration);

      const endTime = `${endDate.getHours().toString().padStart(2, '0')}:${endDate.getMinutes().toString().padStart(2, '0')}`;
      setValue('endTime', endTime);
    }
  }, [watchStartTime, selectedService, setValue]);

  // Form submission handler
  const onSubmit: SubmitHandler<Appointment> = async (data) => {
    try {
      let result;

      if (mode === 'create') {
        // Create new appointment
        result = await createAppointment({
          merchantId,
          ...data,
        }).unwrap();

        setSuccess('Appointment created successfully');
      } else {
        // Update existing appointment
        if (!initialData?.id) {
          throw new Error('Appointment ID is required for updates');
        }

        result = await updateAppointment({
          merchantId,
          appointmentId: initialData.id,
          data,
        }).unwrap();

        setSuccess('Appointment updated successfully');
      }

      setError(null);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} appointment. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing appointment:`, err);
    }
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
      <h2 className="text-lg font-semibold mb-4 text-foreground">
        {mode === 'create' ? 'Create Appointment' : 'Edit Appointment'}
      </h2>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 dark:bg-red-950 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-700 dark:text-red-300">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6 dark:bg-green-950 dark:border-green-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-700 dark:text-green-300">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="serviceId" className="block text-sm font-medium text-gray-700 mb-1">
              Service *
            </label>
            <select
              id="serviceId"
              {...register('serviceId')}
              className={`w-full p-2 border rounded-md ${errors.serviceId ? 'border-red-500' : ''}`}
            >
              <option value="">Select a service</option>
              {services?.map((service) => (
                <option key={service.id} value={service.id}>
                  {service.name} ({service.duration} min, ${service.price})
                </option>
              ))}
            </select>
            {errors.serviceId && (
              <p className="mt-1 text-sm text-red-600">{errors.serviceId.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="staffId" className="block text-sm font-medium text-gray-700 mb-1">
              Staff Member
            </label>
            <select
              id="staffId"
              {...register('staffId')}
              className={`w-full p-2 border rounded-md ${errors.staffId ? 'border-red-500' : ''}`}
            >
              <option value="">Any available staff</option>
              {staff?.map((staffMember) => (
                <option key={staffMember.id} value={staffMember.id}>
                  {staffMember.name} - {staffMember.role}
                </option>
              ))}
            </select>
            {errors.staffId && (
              <p className="mt-1 text-sm text-red-600">{errors.staffId.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
              Date *
            </label>
            <input
              id="date"
              type="date"
              {...register('date')}
              min={new Date().toISOString().split('T')[0]}
              className={`w-full p-2 border rounded-md ${errors.date ? 'border-red-500' : ''}`}
            />
            {errors.date && (
              <p className="mt-1 text-sm text-red-600">{errors.date.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="startTime" className="block text-sm font-medium text-gray-700 mb-1">
              Start Time *
            </label>
            <select
              id="startTime"
              {...register('startTime')}
              className={`w-full p-2 border rounded-md ${errors.startTime ? 'border-red-500' : ''}`}
              disabled={!selectedServiceId || isLoadingTimeSlots}
            >
              <option value="">Select a time</option>
              {timeSlots?.filter(slot => slot.available).map((slot, index) => (
                <option key={index} value={slot.startTime}>
                  {slot.startTime}
                </option>
              ))}
            </select>
            {errors.startTime && (
              <p className="mt-1 text-sm text-red-600">{errors.startTime.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Name *
            </label>
            <input
              id="customerName"
              {...register('customerName')}
              className={`w-full p-2 border rounded-md ${errors.customerName ? 'border-red-500' : ''}`}
            />
            {errors.customerName && (
              <p className="mt-1 text-sm text-red-600">{errors.customerName.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="customerEmail" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Email *
            </label>
            <input
              id="customerEmail"
              type="email"
              {...register('customerEmail')}
              className={`w-full p-2 border rounded-md ${errors.customerEmail ? 'border-red-500' : ''}`}
            />
            {errors.customerEmail && (
              <p className="mt-1 text-sm text-red-600">{errors.customerEmail.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="customerPhone" className="block text-sm font-medium text-gray-700 mb-1">
              Customer Phone
            </label>
            <input
              id="customerPhone"
              {...register('customerPhone')}
              className={`w-full p-2 border rounded-md ${errors.customerPhone ? 'border-red-500' : ''}`}
            />
            {errors.customerPhone && (
              <p className="mt-1 text-sm text-red-600">{errors.customerPhone.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="numberOfPeople" className="block text-sm font-medium text-gray-700 mb-1">
              Number of People *
            </label>
            <input
              id="numberOfPeople"
              type="number"
              {...register('numberOfPeople', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.numberOfPeople ? 'border-red-500' : ''}`}
              min="1"
            />
            {errors.numberOfPeople && (
              <p className="mt-1 text-sm text-red-600">{errors.numberOfPeople.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="userId" className="block text-sm font-medium text-gray-700 mb-1">
              User ID *
            </label>
            <input
              id="userId"
              {...register('userId')}
              className={`w-full p-2 border rounded-md ${errors.userId ? 'border-red-500' : ''}`}
            />
            {errors.userId && (
              <p className="mt-1 text-sm text-red-600">{errors.userId.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status *
            </label>
            <select
              id="status"
              {...register('status')}
              className={`w-full p-2 border rounded-md ${errors.status ? 'border-red-500' : ''}`}
            >
              <option value="scheduled">Scheduled</option>
              <option value="confirmed">Confirmed</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no-show">No Show</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <textarea
              id="notes"
              {...register('notes')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.notes ? 'border-red-500' : ''}`}
            />
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes.message}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Appointment' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
