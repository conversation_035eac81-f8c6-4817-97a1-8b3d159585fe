'use client';

import { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { License, licenseSchema } from '@/lib/validations/digitalProductSchema';
import {
  useCreateLicenseMutation,
  useUpdateLicenseMutation,
  useGetDigitalProductsQuery
} from '@/lib/redux/api/endpoints/digitalApi';

interface LicenseFormProps {
  merchantId: string;
  initialData?: License & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function LicenseForm({
  merchantId,
  initialData,
  onSuccess,
  mode
}: LicenseFormProps) {
  const [createLicense, { isLoading: isCreating }] = useCreateLicenseMutation();
  const [updateLicense, { isLoading: isUpdating }] = useUpdateLicenseMutation();
  const { data: products } = useGetDigitalProductsQuery(merchantId);

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isLoading = isCreating || isUpdating;

  // Generate a random license key
  const generateLicenseKey = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 5; i++) {
      for (let j = 0; j < 5; j++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      if (i < 4) result += '-';
    }
    return result;
  };

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors, isDirty }
  } = useForm<License>({
    resolver: zodResolver(licenseSchema),
    defaultValues: initialData || {
      productId: '',
      userId: '',
      orderId: '',
      licenseKey: generateLicenseKey(),
      activationDate: new Date().toISOString().split('T')[0],
      expiryDate: null,
      maxActivations: 1,
      currentActivations: 0,
      status: 'active',
    }
  });

  // Form submission handler
  const onSubmit: SubmitHandler<License> = async (data) => {
    try {
      let result;

      if (mode === 'create') {
        // Create new license
        result = await createLicense({
          merchantId,
          ...data,
        }).unwrap();

        setSuccess('License created successfully');
      } else {
        // Update existing license
        if (!initialData?.id) {
          throw new Error('License ID is required for updates');
        }

        result = await updateLicense({
          merchantId,
          licenseId: initialData.id,
          data,
        }).unwrap();

        setSuccess('License updated successfully');
      }

      setError(null);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} license. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing license:`, err);
    }
  };

  // Generate a new license key
  const handleGenerateKey = () => {
    setValue('licenseKey', generateLicenseKey(), { shouldDirty: true });
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
      <h2 className="text-lg font-semibold mb-4 text-foreground">
        {mode === 'create' ? 'Create License' : 'Edit License'}
      </h2>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 dark:bg-red-950 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-700 dark:text-red-300">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6 dark:bg-green-950 dark:border-green-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-700 dark:text-green-300">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="productId" className="block text-sm font-medium text-gray-700 mb-1">
              Product *
            </label>
            <select
              id="productId"
              {...register('productId')}
              className={`w-full p-2 border rounded-md ${errors.productId ? 'border-red-500' : ''}`}
            >
              <option value="">Select a product</option>
              {products?.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.name}
                </option>
              ))}
            </select>
            {errors.productId && (
              <p className="mt-1 text-sm text-red-600">{errors.productId.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="userId" className="block text-sm font-medium text-gray-700 mb-1">
              User ID *
            </label>
            <input
              id="userId"
              {...register('userId')}
              className={`w-full p-2 border rounded-md ${errors.userId ? 'border-red-500' : ''}`}
            />
            {errors.userId && (
              <p className="mt-1 text-sm text-red-600">{errors.userId.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="orderId" className="block text-sm font-medium text-gray-700 mb-1">
              Order ID *
            </label>
            <input
              id="orderId"
              {...register('orderId')}
              className={`w-full p-2 border rounded-md ${errors.orderId ? 'border-red-500' : ''}`}
            />
            {errors.orderId && (
              <p className="mt-1 text-sm text-red-600">{errors.orderId.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="licenseKey" className="block text-sm font-medium text-gray-700 mb-1">
              License Key *
            </label>
            <div className="flex">
              <input
                id="licenseKey"
                {...register('licenseKey')}
                className={`flex-1 p-2 border rounded-l-md ${errors.licenseKey ? 'border-red-500' : ''}`}
              />
              <button
                type="button"
                onClick={handleGenerateKey}
                className="px-3 py-2 bg-gray-200 text-gray-700 rounded-r-md hover:bg-gray-300"
              >
                Generate
              </button>
            </div>
            {errors.licenseKey && (
              <p className="mt-1 text-sm text-red-600">{errors.licenseKey.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="activationDate" className="block text-sm font-medium text-gray-700 mb-1">
              Activation Date *
            </label>
            <input
              id="activationDate"
              type="date"
              {...register('activationDate')}
              className={`w-full p-2 border rounded-md ${errors.activationDate ? 'border-red-500' : ''}`}
            />
            {errors.activationDate && (
              <p className="mt-1 text-sm text-red-600">{errors.activationDate.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="expiryDate" className="block text-sm font-medium text-gray-700 mb-1">
              Expiry Date
            </label>
            <input
              id="expiryDate"
              type="date"
              {...register('expiryDate')}
              className={`w-full p-2 border rounded-md ${errors.expiryDate ? 'border-red-500' : ''}`}
            />
            {errors.expiryDate && (
              <p className="mt-1 text-sm text-red-600">{errors.expiryDate.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="maxActivations" className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Activations *
            </label>
            <input
              id="maxActivations"
              type="number"
              {...register('maxActivations', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.maxActivations ? 'border-red-500' : ''}`}
              min="1"
            />
            {errors.maxActivations && (
              <p className="mt-1 text-sm text-red-600">{errors.maxActivations.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="currentActivations" className="block text-sm font-medium text-gray-700 mb-1">
              Current Activations *
            </label>
            <input
              id="currentActivations"
              type="number"
              {...register('currentActivations', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.currentActivations ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.currentActivations && (
              <p className="mt-1 text-sm text-red-600">{errors.currentActivations.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status *
            </label>
            <select
              id="status"
              {...register('status')}
              className={`w-full p-2 border rounded-md ${errors.status ? 'border-red-500' : ''}`}
            >
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="revoked">Revoked</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create License' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
