'use client';

import { useState } from 'react';
import { useF<PERSON>, SubmitHandler, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { MenuItem, menuItemSchema } from '@/lib/validations/menuItemSchema';
import {
  useCreateMenuItemMutation,
  useUpdateMenuItemMutation
} from '@/lib/redux/api/endpoints/restaurant/restaurantApi';

interface MenuItemFormProps {
  merchantId: string;
  initialData?: MenuItem & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function MenuItemForm({
  merchantId,
  initialData,
  onSuccess,
  mode
}: MenuItemFormProps) {
  const [createMenuItem, { isLoading: isCreating }] = useCreateMenuItemMutation();
  const [updateMenuItem, { isLoading: isUpdating }] = useUpdateMenuItemMutation();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isLoading = isCreating || isUpdating;

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty }
  } = useForm<MenuItem>({
    resolver: zodResolver(menuItemSchema),
    defaultValues: initialData || {
      name: '',
      description: '',
      price: 0,
      category: '',
      image: '',
      available: true,
      preparationTime: 0,
      ingredients: [],
      allergens: [],
      nutritionalInfo: {},
    }
  });

  // Set up field arrays for ingredients and allergens
  const {
    fields: ingredientFields,
    append: appendIngredient,
    remove: removeIngredient
  } = useFieldArray({
    control,
    name: 'ingredients',
  });

  const {
    fields: allergenFields,
    append: appendAllergen,
    remove: removeAllergen
  } = useFieldArray({
    control,
    name: 'allergens',
  });

  // Form submission handler
  const onSubmit: SubmitHandler<MenuItem> = async (data) => {
    try {
      let result;

      if (mode === 'create') {
        // Create new menu item
        result = await createMenuItem({
          merchantId,
          ...data,
        }).unwrap();

        setSuccess('Menu item created successfully');
      } else {
        // Update existing menu item
        if (!initialData?.id) {
          throw new Error('Menu item ID is required for updates');
        }

        result = await updateMenuItem({
          merchantId,
          itemId: initialData.id,
          data,
        }).unwrap();

        setSuccess('Menu item updated successfully');
      }

      setError(null);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} menu item. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing menu item:`, err);
    }
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
      <h2 className="text-lg font-semibold mb-4 text-foreground">
        {mode === 'create' ? 'Add Menu Item' : 'Edit Menu Item'}
      </h2>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 dark:bg-red-950 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-700 dark:text-red-300">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6 dark:bg-green-950 dark:border-green-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-700 dark:text-green-300">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Item Name *
            </label>
            <input
              id="name"
              {...register('name')}
              className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category *
            </label>
            <input
              id="category"
              {...register('category')}
              className={`w-full p-2 border rounded-md ${errors.category ? 'border-red-500' : ''}`}
              placeholder="e.g., Appetizers, Main Course, Desserts"
            />
            {errors.category && (
              <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Price *
            </label>
            <input
              id="price"
              type="number"
              step="0.01"
              {...register('price', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.price ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="preparationTime" className="block text-sm font-medium text-gray-700 mb-1">
              Preparation Time (minutes)
            </label>
            <input
              id="preparationTime"
              type="number"
              {...register('preparationTime', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.preparationTime ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.preparationTime && (
              <p className="mt-1 text-sm text-red-600">{errors.preparationTime.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="image" className="block text-sm font-medium text-gray-700 mb-1">
              Image URL
            </label>
            <input
              id="image"
              type="url"
              {...register('image')}
              className={`w-full p-2 border rounded-md ${errors.image ? 'border-red-500' : ''}`}
              placeholder="https://example.com/image.jpg"
            />
            {errors.image && (
              <p className="mt-1 text-sm text-red-600">{errors.image.message}</p>
            )}
          </div>

          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                {...register('available')}
                className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Available</span>
            </label>
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Ingredients
          </label>

          <div className="space-y-2 mb-2">
            {ingredientFields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`ingredients.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="e.g., Tomatoes, Cheese, Flour"
                />
                <button
                  type="button"
                  onClick={() => removeIngredient(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>

          <button
            type="button"
            onClick={() => appendIngredient('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Ingredient
          </button>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Allergens
          </label>

          <div className="space-y-2 mb-2">
            {allergenFields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`allergens.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="e.g., Gluten, Dairy, Nuts"
                />
                <button
                  type="button"
                  onClick={() => removeAllergen(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>

          <button
            type="button"
            onClick={() => appendAllergen('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Allergen
          </button>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Item' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
