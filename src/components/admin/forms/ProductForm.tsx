'use client';

import { useState } from 'react';
import { use<PERSON><PERSON>, SubmitHandler, useFieldArray, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Product, productSchema, ProductVariant } from '@/lib/validations/productSchema';
import {
  useCreateProductMutation,
  useUpdateProductMutation
} from '@/lib/redux/api/endpoints/retailApi';

interface ProductFormProps {
  merchantId: string;
  initialData?: Product & { id?: string };
  onSuccess?: (data: any) => void;
  mode: 'create' | 'edit';
}

export default function ProductForm({
  merchantId,
  initialData,
  onSuccess,
  mode
}: ProductFormProps) {
  const [createProduct, { isLoading: isCreating }] = useCreateProductMutation();
  const [updateProduct, { isLoading: isUpdating }] = useUpdateProductMutation();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const isLoading = isCreating || isUpdating;

  // Set up react-hook-form with zod validation
  const {
    register,
    handleSubmit,
    control,
    formState: { errors, isDirty },
    watch
  } = useForm<Product>({
    resolver: zodResolver(productSchema),
    defaultValues: initialData || {
      name: '',
      description: '',
      price: 0,
      salePrice: null,
      category: '',
      images: [],
      sku: '',
      barcode: '',
      weight: 0,
      dimensions: {
        length: 0,
        width: 0,
        height: 0,
      },
      inventoryCount: 0,
      inventoryThreshold: 5,
      available: true,
      attributes: {},
      variants: [],
    }
  });

  // Set up field arrays for images and variants
  const {
    fields: imageFields,
    append: appendImage,
    remove: removeImage
  } = useFieldArray({
    control,
    name: 'images',
  });

  const {
    fields: variantFields,
    append: appendVariant,
    remove: removeVariant
  } = useFieldArray({
    control,
    name: 'variants',
  });

  // Form submission handler
  const onSubmit: SubmitHandler<Product> = async (data) => {
    try {
      let result;

      if (mode === 'create') {
        // Create new product
        result = await createProduct({
          merchantId,
          ...data,
        }).unwrap();

        setSuccess('Product created successfully');
      } else {
        // Update existing product
        if (!initialData?.id) {
          throw new Error('Product ID is required for updates');
        }

        result = await updateProduct({
          merchantId,
          productId: initialData.id,
          data,
        }).unwrap();

        setSuccess('Product updated successfully');
      }

      setError(null);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result);
      }

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err) {
      setError(`Failed to ${mode} product. Please try again.`);
      setSuccess(null);
      console.error(`Error ${mode}ing product:`, err);
    }
  };

  // Add a new variant
  const addVariant = () => {
    appendVariant({
      name: '',
      sku: '',
      price: 0,
      inventoryCount: 0,
      attributes: {},
    } as ProductVariant);
  };

  return (
    <div className="bg-card p-6 rounded-lg shadow-sm border border-border">
      <h2 className="text-lg font-semibold mb-4 text-foreground">
        {mode === 'create' ? 'Add Product' : 'Edit Product'}
      </h2>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6 dark:bg-red-950 dark:border-red-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-red-700 dark:text-red-300">❌</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4 mb-6 dark:bg-green-950 dark:border-green-800">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-green-700 dark:text-green-300">✓</span>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700 dark:text-green-300">{success}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Product Name *
            </label>
            <input
              id="name"
              {...register('name')}
              className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : ''}`}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
              Category *
            </label>
            <input
              id="category"
              {...register('category')}
              className={`w-full p-2 border rounded-md ${errors.category ? 'border-red-500' : ''}`}
              placeholder="e.g., Clothing, Electronics, Home"
            />
            {errors.category && (
              <p className="mt-1 text-sm text-red-600">{errors.category.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-1">
              Regular Price *
            </label>
            <input
              id="price"
              type="number"
              step="0.01"
              {...register('price', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.price ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.price && (
              <p className="mt-1 text-sm text-red-600">{errors.price.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="salePrice" className="block text-sm font-medium text-gray-700 mb-1">
              Sale Price
            </label>
            <input
              id="salePrice"
              type="number"
              step="0.01"
              {...register('salePrice', {
                setValueAs: v => v === '' ? null : parseFloat(v),
                valueAsNumber: true
              })}
              className={`w-full p-2 border rounded-md ${errors.salePrice ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.salePrice && (
              <p className="mt-1 text-sm text-red-600">{errors.salePrice.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="sku" className="block text-sm font-medium text-gray-700 mb-1">
              SKU *
            </label>
            <input
              id="sku"
              {...register('sku')}
              className={`w-full p-2 border rounded-md ${errors.sku ? 'border-red-500' : ''}`}
            />
            {errors.sku && (
              <p className="mt-1 text-sm text-red-600">{errors.sku.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="barcode" className="block text-sm font-medium text-gray-700 mb-1">
              Barcode
            </label>
            <input
              id="barcode"
              {...register('barcode')}
              className={`w-full p-2 border rounded-md ${errors.barcode ? 'border-red-500' : ''}`}
            />
            {errors.barcode && (
              <p className="mt-1 text-sm text-red-600">{errors.barcode.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="inventoryCount" className="block text-sm font-medium text-gray-700 mb-1">
              Inventory Count *
            </label>
            <input
              id="inventoryCount"
              type="number"
              {...register('inventoryCount', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.inventoryCount ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.inventoryCount && (
              <p className="mt-1 text-sm text-red-600">{errors.inventoryCount.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="inventoryThreshold" className="block text-sm font-medium text-gray-700 mb-1">
              Low Stock Threshold
            </label>
            <input
              id="inventoryThreshold"
              type="number"
              {...register('inventoryThreshold', { valueAsNumber: true })}
              className={`w-full p-2 border rounded-md ${errors.inventoryThreshold ? 'border-red-500' : ''}`}
              min="0"
            />
            {errors.inventoryThreshold && (
              <p className="mt-1 text-sm text-red-600">{errors.inventoryThreshold.message}</p>
            )}
          </div>

          <div className="md:col-span-2">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              id="description"
              {...register('description')}
              rows={3}
              className={`w-full p-2 border rounded-md ${errors.description ? 'border-red-500' : ''}`}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description.message}</p>
            )}
          </div>

          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                {...register('available')}
                className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Available</span>
            </label>
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-md font-medium mb-2">Product Images</h3>

          <div className="space-y-2 mb-2">
            {imageFields.map((field, index) => (
              <div key={field.id} className="flex items-center">
                <input
                  {...register(`images.${index}` as const)}
                  className="flex-1 p-2 border rounded-md"
                  placeholder="https://example.com/image.jpg"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="ml-2 p-2 text-red-600 hover:text-red-800"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>

          <button
            type="button"
            onClick={() => appendImage('')}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            + Add Image URL
          </button>
        </div>

        <div className="mb-6">
          <h3 className="text-md font-medium mb-2">Product Dimensions</h3>

          <div className="grid grid-cols-3 gap-4">
            <div>
              <label htmlFor="length" className="block text-sm font-medium text-gray-700 mb-1">
                Length
              </label>
              <input
                id="length"
                type="number"
                step="0.01"
                {...register('dimensions.length', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.dimensions?.length ? 'border-red-500' : ''}`}
                min="0"
              />
              {errors.dimensions?.length && (
                <p className="mt-1 text-sm text-red-600">{errors.dimensions.length.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="width" className="block text-sm font-medium text-gray-700 mb-1">
                Width
              </label>
              <input
                id="width"
                type="number"
                step="0.01"
                {...register('dimensions.width', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.dimensions?.width ? 'border-red-500' : ''}`}
                min="0"
              />
              {errors.dimensions?.width && (
                <p className="mt-1 text-sm text-red-600">{errors.dimensions.width.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="height" className="block text-sm font-medium text-gray-700 mb-1">
                Height
              </label>
              <input
                id="height"
                type="number"
                step="0.01"
                {...register('dimensions.height', { valueAsNumber: true })}
                className={`w-full p-2 border rounded-md ${errors.dimensions?.height ? 'border-red-500' : ''}`}
                min="0"
              />
              {errors.dimensions?.height && (
                <p className="mt-1 text-sm text-red-600">{errors.dimensions.height.message}</p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || (mode === 'edit' && !isDirty)}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed"
          >
            {isLoading ? 'Saving...' : mode === 'create' ? 'Create Product' : 'Save Changes'}
          </button>
        </div>
      </form>
    </div>
  );
}
