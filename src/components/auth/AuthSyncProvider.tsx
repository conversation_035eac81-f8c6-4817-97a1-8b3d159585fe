'use client'

import { useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useDispatch } from 'react-redux'
import { setUser, setLoading, clearError } from '@/lib/redux/slices/authSlice'

interface AuthSyncProviderProps {
  children: React.ReactNode
}

export default function AuthSyncProvider({ children }: AuthSyncProviderProps) {
  const { data: session, status } = useSession()
  const dispatch = useDispatch()

  useEffect(() => {
    if (status === 'loading') {
      dispatch(setLoading(true))
    } else {
      dispatch(setLoading(false))
      
      if (session?.user) {
        dispatch(setUser({
          id: session.user.id,
          email: session.user.email,
          name: session.user.name,
          role: session.user.role,
        }))
        dispatch(clearError())
      } else {
        dispatch(setUser(null))
      }
    }
  }, [session, status, dispatch])

  return <>{children}</>
}
