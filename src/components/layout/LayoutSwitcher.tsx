'use client';

import React from 'react';
import { useNavigation } from '@/lib/context/NavigationContext';
import { NavigationType } from '@/lib/types/navigation';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Layout, 
  PanelLeft, 
  PanelRight, 
  Menu, 
  Navigation,
  Check
} from 'lucide-react';
import { toast } from 'sonner';

const layoutOptions = [
  {
    type: NavigationType.HEADER,
    name: 'Header Navigation',
    description: 'Expandable tabs at the top',
    icon: <Layout className="h-4 w-4" />
  },
  {
    type: NavigationType.SIDEBAR_LEFT,
    name: 'Left Sidebar',
    description: 'Collapsible sidebar on the left',
    icon: <PanelLeft className="h-4 w-4" />
  },
  {
    type: NavigationType.SIDEBAR_RIGHT,
    name: 'Right Sidebar',
    description: 'Collapsible sidebar on the right',
    icon: <PanelRight className="h-4 w-4" />
  },
  {
    type: NavigationType.DRAWER,
    name: 'Drawer Navigation',
    description: 'Hamburger menu with drawer',
    icon: <Menu className="h-4 w-4" />
  },
  {
    type: NavigationType.BOTTOM,
    name: 'Bottom Navigation',
    description: 'Navigation bar at the bottom',
    icon: <Navigation className="h-4 w-4" />
  }
];

interface LayoutSwitcherProps {
  variant?: 'button' | 'compact';
  showLabel?: boolean;
}

export default function LayoutSwitcher({ 
  variant = 'button', 
  showLabel = true 
}: LayoutSwitcherProps) {
  const { settings, updateSettings } = useNavigation();

  const handleLayoutChange = (type: NavigationType) => {
    updateSettings({ type });
    const selectedOption = layoutOptions.find(option => option.type === type);
    toast.success(`Layout changed to ${selectedOption?.name}`);
  };

  const currentLayout = layoutOptions.find(option => option.type === settings.type);

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            {currentLayout?.icon}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Layout Options</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {layoutOptions.map((option) => (
            <DropdownMenuItem
              key={option.type}
              onClick={() => handleLayoutChange(option.type)}
              className="flex items-center justify-between"
            >
              <div className="flex items-center gap-2">
                {option.icon}
                <span>{option.name}</span>
              </div>
              {settings.type === option.type && (
                <Check className="h-4 w-4 text-green-500" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="border-[#e5e1dc] text-[#181510]">
          {currentLayout?.icon}
          {showLabel && <span className="ml-2">{currentLayout?.name}</span>}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64">
        <DropdownMenuLabel>Choose Layout</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {layoutOptions.map((option) => (
          <DropdownMenuItem
            key={option.type}
            onClick={() => handleLayoutChange(option.type)}
            className="flex flex-col items-start gap-1 p-3"
          >
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                {option.icon}
                <span className="font-medium">{option.name}</span>
              </div>
              {settings.type === option.type && (
                <Check className="h-4 w-4 text-green-500" />
              )}
            </div>
            <p className="text-xs text-[#8a745c] ml-6">{option.description}</p>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
