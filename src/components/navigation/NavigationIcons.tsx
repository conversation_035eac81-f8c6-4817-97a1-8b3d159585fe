'use client';

import React from 'react';
import { 
  LayoutDashboard, 
  ShoppingCart, 
  Menu as MenuIcon, 
  Table, 
  Star, 
  Users, 
  BarChart3,
  Settings,
  Home,
  Calendar,
  MessageSquare,
  Bell,
  User,
  LogOut
} from 'lucide-react';
import { NavigationItem } from '@/lib/types/navigation';

// Icon size for navigation items
const ICON_SIZE = 20;

// Function to add icons to navigation items
export function addIconsToNavItems(navItems: Omit<NavigationItem, 'icon'>[]): NavigationItem[] {
  const iconMap: Record<string, React.ReactNode> = {
    'Dashboard': <LayoutDashboard size={ICON_SIZE} />,
    'Orders': <ShoppingCart size={ICON_SIZE} />,
    'Menu': <MenuIcon size={ICON_SIZE} />,
    'Tables': <Table size={ICON_SIZE} />,
    'Reviews': <Star size={ICON_SIZE} />,
    'Staff': <Users size={ICON_SIZE} />,
    'Reports': <BarChart3 size={ICON_SIZE} />,
    'Settings': <Settings size={ICON_SIZE} />,
    'Home': <Home size={ICON_SIZE} />,
    'Calendar': <Calendar size={ICON_SIZE} />,
    'Messages': <MessageSquare size={ICON_SIZE} />,
    'Notifications': <Bell size={ICON_SIZE} />,
    'Profile': <User size={ICON_SIZE} />,
    'Logout': <LogOut size={ICON_SIZE} />
  };

  return navItems.map(item => ({
    ...item,
    icon: iconMap[item.name] || null
  }));
}
