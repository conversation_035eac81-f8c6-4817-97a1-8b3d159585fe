'use client';

import React, { useState } from 'react';
import { Link, usePathname } from '@/i18n/navigation';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import {
  User,
  Settings,
  LogOut,
  CreditCard,
  Bell,
  HelpCircle
} from 'lucide-react';
import { useTranslations } from 'next-intl';

interface ProfileMenuProps {
  userImage?: string;
  userName?: string;
  userRole?: string;
}

export default function ProfileMenu({
  userImage = 'https://lh3.googleusercontent.com/aida-public/AB6AXuBTI0Z2xPUTyhRVeL1K9vLjQGbi-1UHxVlqwNVDF-iTr92vrWHqqemi73fpc8wOTxMsEWHp2sMWTYeuRGEAvkoprEmdcqQKjv1ysjZPz-Oxjj0nwmNth_9fIg4-n1G3JRRNt6U7R2hsx-S0tKSQkNScC_IoX_R82wZ86pqUnzHoPxbvYmN3_czqGASpoFbwwL2zK_CrjOxPpt8-qcbimmno5Mh8rjWKzJy07NeVJl7_QAhgvUVft3cXpPUn8wHTLxZSJXRyU2pkKbK3',
  userName = 'John Doe',
  userRole = 'Restaurant Owner'
}: ProfileMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const t = useTranslations('navigation');
  const pathname = usePathname();

  // Extract slugShop and slugBranch from pathname
  // Pattern: /app/restaurant/[slugShop]/[slugBranch]/...
  const pathParts = pathname.split('/').filter(Boolean);
  const slugShop = pathParts.length > 2 ? pathParts[2] : '';
  const slugBranch = pathParts.length > 3 ? pathParts[3] : '';

  const handleLogout = () => {
    // Implement logout functionality
    console.log('Logging out...');
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <button
          className="flex items-center gap-2 rounded-full focus:outline-none"
          aria-label="Open profile menu"
        >
          <div
            className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-8 hover:ring-2 hover:ring-[#e5ccb2] transition-all duration-200"
            style={{ backgroundImage: `url("${userImage}")` }}
          ></div>
        </button>
      </PopoverTrigger>
      <PopoverContent
        className="w-64 p-0 bg-[#fbfaf9] border-[#e5e1dc] shadow-lg"
        align="end"
        sideOffset={8}
      >
        <div className="p-4 border-b border-[#e5e1dc]">
          <div className="flex items-center gap-3">
            <div
              className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-12"
              style={{ backgroundImage: `url("${userImage}")` }}
            ></div>
            <div>
              <div className="text-sm font-medium text-[#181510]">{userName}</div>
              <div className="text-xs text-[#8a745c]">{userRole}</div>
            </div>
          </div>
        </div>

        <div className="py-2">
          {slugShop && slugBranch ? (
            <>
              <Link
                href={`/app/restaurant/${slugShop}/${slugBranch}/settings/profile`}
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <User size={16} className="text-[#8a745c]" />
                {t('profile')}
              </Link>

              <Link
                href={`/app/restaurant/${slugShop}/${slugBranch}/settings`}
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <Settings size={16} className="text-[#8a745c]" />
                {t('settings')}
              </Link>

              <Link
                href={`/app/restaurant/${slugShop}/${slugBranch}/settings/billing`}
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <CreditCard size={16} className="text-[#8a745c]" />
                Billing
              </Link>

              <Link
                href={`/app/restaurant/${slugShop}/${slugBranch}/settings/notifications`}
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <Bell size={16} className="text-[#8a745c]" />
                Notifications
              </Link>

              <Link
                href={`/app/restaurant/${slugShop}/${slugBranch}/help`}
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <HelpCircle size={16} className="text-[#8a745c]" />
                Help & Support
              </Link>
            </>
          ) : (
            <>
              <Link
                href="/app/restaurant/settings/profile"
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <User size={16} className="text-[#8a745c]" />
                {t('profile')}
              </Link>

              <Link
                href="/app/restaurant/settings"
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <Settings size={16} className="text-[#8a745c]" />
                {t('settings')}
              </Link>

              <Link
                href="/app/restaurant/settings/billing"
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <CreditCard size={16} className="text-[#8a745c]" />
                Billing
              </Link>

              <Link
                href="/app/restaurant/settings/notifications"
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <Bell size={16} className="text-[#8a745c]" />
                Notifications
              </Link>

              <Link
                href="/app/restaurant/help"
                className="flex items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
                onClick={() => setIsOpen(false)}
              >
                <HelpCircle size={16} className="text-[#8a745c]" />
                Help & Support
              </Link>
            </>
          )}
        </div>

        <div className="border-t border-[#e5e1dc] py-2">
          <button
            onClick={() => {
              handleLogout();
              setIsOpen(false);
            }}
            className="flex w-full items-center gap-3 px-4 py-2 text-sm text-[#181510] hover:bg-[#f1edea] transition-colors"
          >
            <LogOut size={16} className="text-[#8a745c]" />
            {t('logout')}
          </button>
        </div>
      </PopoverContent>
    </Popover>
  );
}
