'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { NavigationItem } from '@/lib/types/navigation';
import { ChevronLeft, ChevronRight, Search, Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  She<PERSON>,
  <PERSON>etContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
  SheetClose,
  SheetFooter
} from '@/components/ui/sheet';
import NotificationPopover from '@/components/notifications/NotificationPopover';
import ProfileMenu from '@/components/navigation/ProfileMenu';

interface SidebarNavigationProps {
  navItems?: NavigationItem[];
  position?: 'left' | 'right';
  isCollapsed?: boolean;
  showIcons?: boolean;
  showLabels?: boolean;
  setIsCollapsed?: (collapsed: boolean) => void;
}

export default function SidebarNavigation({
  navItems = [],
  position = 'left',
  isCollapsed = false,
  showIcons = true,
  showLabels = true,
  setIsCollapsed,
}: SidebarNavigationProps) {
  // Create a toggle function if setIsCollapsed is provided
  const onToggleCollapse = setIsCollapsed ? () => setIsCollapsed(!isCollapsed) : undefined;
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  // Determine the side for the sheet based on the position prop
  const side = position === 'left' ? 'left' : 'right';

  return (
    <>
      {/* Collapsed sidebar */}
      {isCollapsed && (
        <div
          className={cn(
            'flex flex-col h-screen bg-[#fbfaf9] border-solid transition-all duration-300 w-16 absolute z-50 top-16 left-0',
            position === 'left' ? 'border-r border-r-[#f1edea]' : 'border-l border-l-[#f1edea]',
          )}
        >
          {/* <div className="flex items-center justify-center p-4 border-b border-[#f1edea]">
            <div className="size-4 mx-auto">
              <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                  fill="currentColor"
                ></path>
              </svg>
            </div>
          </div> */}

          <button
            onClick={onToggleCollapse}
            className="mx-auto my-3 p-2 rounded-full hover:bg-[#f1edea] text-[#8a745c]"
          >
            {position === 'left' ? <ChevronRight size={18} /> : <ChevronLeft size={18} />}
          </button>

          <nav className="flex-1 overflow-y-auto py-4">
            <ul className="space-y-1">
              {navItems.map((item) => (
                <li key={item.href}>
                  <Link
                    href={item.href}
                    className={cn(
                      'flex items-center justify-center px-4 py-2 text-sm font-medium',
                      pathname === item.href
                        ? 'bg-[#f1edea] text-[#181510] font-bold'
                        : 'text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]'
                    )}
                  >
                    {showIcons && item.icon && (
                      <span className="text-current">
                        {item.icon}
                      </span>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>

          {/* <div className="p-4 border-t border-[#f1edea] flex flex-col items-center gap-4">
            <NotificationPopover />

            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <button className="text-[#8a745c] hover:text-[#181510] transition-colors">
                  <Menu size={20} />
                </button>
              </SheetTrigger>
              <SheetContent side={side as "left" | "right" | "top" | "bottom"} className="w-64 p-0 bg-[#fbfaf9]">
                <SheetHeader className="p-4 border-b border-[#f1edea]">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="size-4">
                        <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                            fill="currentColor"
                          ></path>
                        </svg>
                      </div>
                      <SheetTitle className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">
                        Table Manager
                      </SheetTitle>
                    </div>
                    <SheetClose className="text-[#8a745c] hover:text-[#181510] transition-colors">
                      <X size={18} />
                    </SheetClose>
                  </div>
                </SheetHeader>

                <div className="px-4 py-3">
                  <div className="flex w-full items-stretch rounded-lg h-10 bg-[#f1edea]">
                    <div className="text-[#8a745c] flex border-none items-center justify-center pl-4 rounded-l-lg border-r-0">
                      <Search size={18} />
                    </div>
                    <input
                      placeholder="Search"
                      className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-full placeholder:text-[#8a745c] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                      defaultValue=""
                    />
                  </div>
                </div>

                <nav className="flex-1 overflow-y-auto py-4">
                  <ul className="space-y-1">
                    {navItems.map((item) => (
                      <li key={item.href}>
                        <SheetClose asChild>
                          <Link
                            href={item.href}
                            className={cn(
                              'flex items-center px-4 py-2 text-sm font-medium',
                              pathname === item.href
                                ? 'bg-[#f1edea] text-[#181510] font-bold'
                                : 'text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]'
                            )}
                          >
                            {showIcons && item.icon && (
                              <span className="text-current mr-3">
                                {item.icon}
                              </span>
                            )}
                            {showLabels && <span>{item.name}</span>}
                          </Link>
                        </SheetClose>
                      </li>
                    ))}
                  </ul>
                </nav>

                <SheetFooter className="p-4 border-t border-[#f1edea] flex-row justify-between">
                  <NotificationPopover />
                  <ProfileMenu />
                </SheetFooter>
              </SheetContent>
            </Sheet>
          </div> */}
        </div>
      )}

      {/* Expanded sidebar */}
      {!isCollapsed && (
        <div
          className={cn(
            'fixed top-16 bottom-0 z-50 flex flex-col h-screen bg-[#fbfaf9] border-solid transition-all duration-300 w-64 right-0',
            position === 'left' ? 'left-0 border-r border-r-[#f1edea]' : 'right-0 border-l border-l-[#f1edea]'
          )}
        >
            <div className="flex items-center justify-between p-4 border-b border-[#f1edea]">
              <div className="flex items-center gap-3">
                <div className="size-4">
                  <svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                      d="M44 11.2727C44 14.0109 39.8386 16.3957 33.69 17.6364C39.8386 18.877 44 21.2618 44 24C44 26.7382 39.8386 29.123 33.69 30.3636C39.8386 31.6043 44 33.9891 44 36.7273C44 40.7439 35.0457 44 24 44C12.9543 44 4 40.7439 4 36.7273C4 33.9891 8.16144 31.6043 14.31 30.3636C8.16144 29.123 4 26.7382 4 24C4 21.2618 8.16144 18.877 14.31 17.6364C8.16144 16.3957 4 14.0109 4 11.2727C4 7.25611 12.9543 4 24 4C35.0457 4 44 7.25611 44 11.2727Z"
                      fill="currentColor"
                    ></path>
                  </svg>
                </div>
                <h2 className="text-[#181510] text-lg font-bold leading-tight tracking-[-0.015em]">Table Manager</h2>
              </div>
              <button
                onClick={onToggleCollapse}
                className="text-[#8a745c] hover:text-[#181510] transition-colors"
              >
                {position === 'left' ? <ChevronLeft size={18} /> : <ChevronRight size={18} />}
              </button>
            </div>

            <div className="px-4 py-3">
              <div className="flex w-full items-stretch rounded-lg h-10 bg-[#f1edea]">
                <div className="text-[#8a745c] flex border-none items-center justify-center pl-4 rounded-l-lg border-r-0">
                  <Search size={18} />
                </div>
                <input
                  placeholder="Search"
                  className="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-lg text-[#181510] focus:outline-0 focus:ring-0 border-none bg-[#f1edea] focus:border-none h-full placeholder:text-[#8a745c] px-4 rounded-l-none border-l-0 pl-2 text-base font-normal leading-normal"
                  defaultValue=""
                />
              </div>
            </div>

            <nav className="flex-1 overflow-y-auto py-4">
              <ul className="space-y-1">
                {navItems.map((item) => (
                  <li key={item.href}>
                    <Link
                      href={item.href}
                      className={cn(
                        'flex items-center px-4 py-2 text-sm font-medium',
                        pathname === item.href
                          ? 'bg-[#f1edea] text-[#181510] font-bold'
                          : 'text-[#8a745c] hover:bg-[#f1edea] hover:text-[#181510]'
                      )}
                    >
                      {showIcons && item.icon && (
                        <span className="text-current mr-3">
                          {item.icon}
                        </span>
                      )}
                      {showLabels && <span>{item.name}</span>}
                    </Link>
                  </li>
                ))}
              </ul>
            </nav>

            <div className="p-4 border-t border-[#f1edea] flex items-center justify-between">
              <NotificationPopover />
              <ProfileMenu />
            </div>
          </div>
      )}
    </>
  );
}
