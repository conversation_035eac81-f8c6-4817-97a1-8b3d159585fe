'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { NotificationBell } from './NotificationBell';

interface NotificationBellWrapperProps {
  className?: string;
}

export function NotificationBellWrapper({ className }: NotificationBellWrapperProps) {
  const pathname = usePathname();
  
  // Extract shop and branch slugs from the current path
  // Expected path format: /[locale]/app/restaurant/[shopSlug]/[branchSlug]/...
  const pathSegments = pathname.split('/').filter(Boolean);
  
  // Find the restaurant segment and extract slugs
  const restaurantIndex = pathSegments.findIndex(segment => segment === 'restaurant');
  
  if (restaurantIndex === -1 || pathSegments.length < restaurantIndex + 3) {
    // Not in a restaurant route or missing required segments
    return null;
  }
  
  const shopSlug = pathSegments[restaurantIndex + 1];
  const branchSlug = pathSegments[restaurantIndex + 2];
  
  if (!shopSlug || !branchSlug) {
    return null;
  }
  
  return (
    <NotificationBell 
      className={className}
      shopSlug={shopSlug}
      branchSlug={branchSlug}
    />
  );
}
