'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { toast } from 'sonner';

// Define the form schema
const reservationFormSchema = z.object({
  customerName: z.string().min(1, 'Customer name is required'),
  customerEmail: z.string().email('Invalid email address'),
  customerPhone: z.string().min(1, 'Phone number is required'),
  date: z.string().min(1, 'Date is required'),
  time: z.string().min(1, 'Time is required'),
  partySize: z.number().int().min(1, 'Party size must be at least 1'),
  tableId: z.string().min(1, 'Table is required'),
  specialRequests: z.string().optional(),
});

type ReservationFormValues = z.infer<typeof reservationFormSchema>;

interface ReservationFormProps {
  tables: { id: string; number: number }[];
  onSuccess?: (reservationData: any) => void;
  onCancel?: () => void;
  initialData?: ReservationFormValues;
}

export default function ReservationForm({ tables, onSuccess, onCancel, initialData }: ReservationFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isEditing = !!initialData;

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ReservationFormValues>({
    resolver: zodResolver(reservationFormSchema),
    defaultValues: initialData || {
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      date: new Date().toISOString().split('T')[0],
      time: '19:00',
      partySize: 2,
      tableId: '',
      specialRequests: '',
    },
  });

  const onSubmit = async (data: ReservationFormValues) => {
    setIsSubmitting(true);

    try {
      // Pass the data to the parent component for API handling
      if (onSuccess) {
        await onSuccess(data);
      }

      if (!isEditing) {
        reset();
      }
    } catch (error) {
      console.error('Error saving reservation:', error);
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} reservation. Please try again.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label htmlFor="customerName" className="block text-sm font-medium text-[#181510]">
            Customer Name *
          </label>
          <input
            id="customerName"
            type="text"
            {...register('customerName')}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          />
          {errors.customerName && (
            <p className="text-red-500 text-xs">{errors.customerName.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="customerEmail" className="block text-sm font-medium text-[#181510]">
            Email *
          </label>
          <input
            id="customerEmail"
            type="email"
            {...register('customerEmail')}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          />
          {errors.customerEmail && (
            <p className="text-red-500 text-xs">{errors.customerEmail.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="customerPhone" className="block text-sm font-medium text-[#181510]">
            Phone *
          </label>
          <input
            id="customerPhone"
            type="tel"
            {...register('customerPhone')}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          />
          {errors.customerPhone && (
            <p className="text-red-500 text-xs">{errors.customerPhone.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="partySize" className="block text-sm font-medium text-[#181510]">
            Party Size *
          </label>
          <input
            id="partySize"
            type="number"
            min="1"
            {...register('partySize', { valueAsNumber: true })}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          />
          {errors.partySize && (
            <p className="text-red-500 text-xs">{errors.partySize.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="date" className="block text-sm font-medium text-[#181510]">
            Date *
          </label>
          <input
            id="date"
            type="date"
            {...register('date')}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          />
          {errors.date && (
            <p className="text-red-500 text-xs">{errors.date.message}</p>
          )}
        </div>

        <div className="space-y-2">
          <label htmlFor="time" className="block text-sm font-medium text-[#181510]">
            Time *
          </label>
          <input
            id="time"
            type="time"
            {...register('time')}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          />
          {errors.time && (
            <p className="text-red-500 text-xs">{errors.time.message}</p>
          )}
        </div>

        <div className="space-y-2 md:col-span-2">
          <label htmlFor="tableId" className="block text-sm font-medium text-[#181510]">
            Table *
          </label>
          <select
            id="tableId"
            {...register('tableId')}
            className="w-full p-2 border rounded-md bg-[#fbfaf9] border-[#e2dcd4] text-[#181510]"
          >
            <option value="">Select a table</option>
            {tables.map((table) => (
              <option key={table.id} value={table.id}>
                Table {table.number}
              </option>
            ))}
          </select>
          {errors.tableId && (
            <p className="text-red-500 text-xs">{errors.tableId.message}</p>
          )}
        </div>

        <div className="space-y-2 md:col-span-2">
          <label htmlFor="specialRequests" className="block text-sm font-medium text-foreground">
            Special Requests
          </label>
          <textarea
            id="specialRequests"
            {...register('specialRequests')}
            rows={3}
            className="w-full p-2 border rounded-md bg-background border-border text-foreground"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <Button
          type="button"
          onClick={onCancel}
          variant="outline"
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
        >
          {isSubmitting
            ? (isEditing ? 'Updating...' : 'Creating...')
            : (isEditing ? 'Update Reservation' : 'Create Reservation')
          }
        </Button>
      </div>
    </form>
  );
}
