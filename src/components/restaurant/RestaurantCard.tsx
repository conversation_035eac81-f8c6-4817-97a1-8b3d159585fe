/**
 * Reusable restaurant card component
 * Displays restaurant information in a card format
 */

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Phone, Mail, Users, Calendar, Star, ArrowRight, Edit, Trash2 } from 'lucide-react';
import { MESSAGES } from '@/lib/constants/messages';
import { cn } from '@/lib/utils';
import { Link } from '@/i18n/navigation';
import { Shop } from '@/lib/types/shop';
import { RestaurantImage } from '@/components/ui/image-with-fallback';

interface RestaurantCardProps {
  restaurant: Shop;
  onView?: (restaurant: Shop) => void;
  onEdit?: (restaurant: Shop) => void;
  onDelete?: (restaurant: Shop) => void;
  showActions?: boolean;
  showBranches?: boolean;
  compact?: boolean;
  className?: string;
}

export function RestaurantCard({
  restaurant,
  onView,
  onEdit,
  onDelete,
  showActions = true,
  showBranches = true,
  compact = false,
  className
}: RestaurantCardProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'inactive':
        return 'bg-muted text-muted-foreground border-border';
      case 'suspended':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  };

  const getStatusText = (status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  };

  const formatAddress = (address: Shop['address']) => {
    if (!address) return '';

    const parts = [];
    if (address.street) parts.push(address.street);
    if (address.city) parts.push(address.city);
    if (address.state) parts.push(address.state);
    if (address.country) parts.push(address.country);

    return parts.join(', ');
  };

  const isActive = restaurant.status.toLowerCase() === 'active';

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-md overflow-hidden',
      !isActive && 'opacity-60',
      className
    )}>
      {/* Restaurant Image */}
      <div className="relative">
        <RestaurantImage
          src={restaurant.logo || ''}
          alt={restaurant.name}
          className="h-48 w-full object-cover"
          containerClassName="h-48 w-full"
        />
        <div className="absolute top-4 right-4">
          <Badge className={getStatusColor(restaurant.status)}>
            {getStatusText(restaurant.status)}
          </Badge>
        </div>
        {showBranches && restaurant.branches && restaurant.branches.length > 0 && (
          <div className="absolute top-4 left-4">
            <Badge className="bg-primary text-primary-foreground">
              {restaurant.branches.length} {restaurant.branches.length === 1 ? 'Branch' : 'Branches'}
            </Badge>
          </div>
        )}
      </div>

      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-bold text-foreground text-lg mb-2 line-clamp-1">
              {restaurant.name}
            </h3>

            {!compact && restaurant.description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                {restaurant.description}
              </p>
            )}

            {/* Location */}
            {restaurant.address && (
              <div className="flex items-center text-sm text-muted-foreground mb-2">
                <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                <span className="line-clamp-1">
                  {formatAddress(restaurant.address)}
                </span>
              </div>
            )}

            {/* Contact Info */}
            {!compact && (
              <div className="space-y-1">
                {restaurant.phone && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Phone className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span>{restaurant.phone}</span>
                  </div>
                )}
                {restaurant.email && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Mail className="h-4 w-4 mr-2 flex-shrink-0" />
                    <span className="line-clamp-1">{restaurant.email}</span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={cn('pt-0', compact && 'pb-3')}>
        {/* Branches List */}
        {showBranches && restaurant.branches && restaurant.branches.length > 0 && !compact && (
          <div className="mb-4">
            <h4 className="text-sm font-medium text-foreground mb-2">Branches:</h4>
            <div className="space-y-1">
              {restaurant.branches.slice(0, 3).map((branch) => (
                <Link
                  key={branch.id}
                  href={`/app/restaurant/${restaurant.slug}/${branch.slug}`}
                  className="flex items-center justify-between p-2 bg-muted rounded-md hover:bg-accent transition-colors"
                >
                  <div>
                    <span className="text-sm font-medium text-foreground">{branch.name}</span>
                    {branch.address && (
                      <p className="text-xs text-muted-foreground line-clamp-1">{formatAddress(branch.address)}</p>
                    )}
                  </div>
                  <ArrowRight className="h-4 w-4 text-muted-foreground" />
                </Link>
              ))}
              {restaurant.branches.length > 3 && (
                <div className="text-xs text-muted-foreground text-center py-1">
                  +{restaurant.branches.length - 3} more branches
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quick Stats */}
        {!compact && (
          <div className="flex items-center gap-4 text-xs text-muted-foreground mb-4">
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{restaurant.branches?.length || 0} branches</span>
            </div>
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Since {new Date(restaurant.created_at).getFullYear()}</span>
            </div>
          </div>
        )}

        {/* Actions */}
        {showActions && (
          <div className="flex items-center justify-between pt-3 border-t border-border">
            <div className="flex space-x-2">
              {onView && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onView(restaurant)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  {MESSAGES.ACTION.VIEW}
                </Button>
              )}

              {/* View Restaurant Link */}
              <Link href={`/app/restaurant/${restaurant.slug}`}>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-muted-foreground hover:text-foreground"
                >
                  <ArrowRight className="h-4 w-4 mr-1" />
                  {MESSAGES.ACTION.OPEN}
                </Button>
              </Link>
            </div>

            <div className="flex space-x-2">
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onEdit(restaurant)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  {MESSAGES.ACTION.EDIT}
                </Button>
              )}
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onDelete(restaurant)}
                  className="text-destructive hover:text-destructive/80 hover:bg-destructive/10"
                >
                  <Trash2 className="h-4 w-4 mr-1" />
                  {MESSAGES.ACTION.DELETE}
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for lists
export function RestaurantCardCompact(props: Omit<RestaurantCardProps, 'compact'>) {
  return <RestaurantCard {...props} compact={true} />;
}

// Loading skeleton
export function RestaurantCardSkeleton({ compact = false }: { compact?: boolean }) {
  return (
    <Card className="animate-pulse overflow-hidden">
      <div className="h-48 bg-muted" />
      <CardHeader className={cn('pb-3', compact && 'pb-2')}>
        <div className="space-y-2">
          <div className="h-6 w-3/4 bg-muted rounded" />
          {!compact && <div className="h-4 w-full bg-muted rounded" />}
          <div className="h-4 w-1/2 bg-muted rounded" />
        </div>
      </CardHeader>
      <CardContent className={cn('pt-0', compact && 'pb-3')}>
        {!compact && (
          <div className="space-y-2 mb-4">
            <div className="h-4 w-1/4 bg-muted rounded" />
            <div className="space-y-1">
              <div className="h-8 bg-muted rounded" />
              <div className="h-8 bg-muted rounded" />
            </div>
          </div>
        )}
        <div className="pt-3 border-t border-border">
          <div className="flex justify-between">
            <div className="flex space-x-2">
              <div className="h-8 w-16 bg-muted rounded" />
              <div className="h-8 w-16 bg-muted rounded" />
            </div>
            <div className="h-8 w-16 bg-muted rounded" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
