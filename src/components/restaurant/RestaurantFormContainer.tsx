/**
 * Restaurant Form Container with Redux integration
 * Handles restaurant creation and editing with RTK Query
 */

'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { RestaurantForm } from './RestaurantForm';
import {
  useCreateRestaurantMutation,
  useUpdateRestaurantMutation,
  useUploadRestaurantLogoMutation,
  useGetRestaurantByIdQuery,
  type Restaurant,
  type CreateRestaurantRequest,
  type UpdateRestaurantRequest,
} from '@/lib/redux/api/endpoints/restaurant/restaurantApi';

interface RestaurantFormContainerProps {
  restaurantId?: string; // If provided, we're editing; otherwise creating
  onSuccess?: (restaurant: Restaurant) => void;
  onCancel?: () => void;
  className?: string;
}

export function RestaurantFormContainer({
  restaurantId,
  onSuccess,
  onCancel,
  className,
}: RestaurantFormContainerProps) {
  const router = useRouter();
  const isEditing = !!restaurantId;

  // RTK Query hooks
  const {
    data: restaurant,
    isLoading: isLoadingRestaurant,
    error: loadError,
  } = useGetRestaurantByIdQuery(restaurantId!, {
    skip: !restaurantId,
  });

  const [createRestaurant, { isLoading: isCreating }] = useCreateRestaurantMutation();
  const [updateRestaurant, { isLoading: isUpdating }] = useUpdateRestaurantMutation();
  const [uploadLogo, { isLoading: isUploadingLogo }] = useUploadRestaurantLogoMutation();

  const isLoading = isCreating || isUpdating || isUploadingLogo;

  // Handle form submission
  const handleSubmit = async (data: CreateRestaurantRequest & { logo?: string }) => {
    try {
      let result: Restaurant;

      if (isEditing && restaurantId) {
        // Update existing restaurant
        const updateData: UpdateRestaurantRequest = {
          name: data.name,
          description: data.description,
          email: data.email,
          phone: data.phone,
          address: data.address,
          city: data.city,
          state: data.state,
          country: data.country,
          postalCode: data.postalCode,
          timezone: data.timezone,
          currency: data.currency,
          status: data.status as 'active' | 'inactive' | 'suspended',
        };

        const response = await updateRestaurant({
          id: restaurantId,
          data: updateData,
        }).unwrap();

        result = response;
        toast.success('Restaurant updated successfully!');
      } else {
        // Create new restaurant
        const createData: CreateRestaurantRequest = {
          name: data.name,
          description: data.description,
          email: data.email,
          phone: data.phone,
          address: data.address,
          city: data.city,
          state: data.state,
          country: data.country,
          postalCode: data.postalCode,
          timezone: data.timezone,
          currency: data.currency,
        };

        const response = await createRestaurant(createData).unwrap();
        result = response;
        toast.success('Restaurant created successfully!');
      }

      // Handle logo upload if there's a file
      // Note: This would need to be implemented based on how you handle file uploads
      // For now, we'll skip the actual file upload implementation

      // Call success callback or navigate
      if (onSuccess) {
        onSuccess(result);
      } else {
        // Default navigation behavior
        router.push(`/app/restaurant/${result.id}`);
      }
    } catch (error: any) {
      console.error('Error saving restaurant:', error);
      
      // Extract error message
      let errorMessage = 'Failed to save restaurant';
      if (error?.data?.message) {
        errorMessage = error.data.message;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    } else {
      // Default navigation behavior
      router.back();
    }
  };

  // Show loading state while fetching restaurant data
  if (isEditing && isLoadingRestaurant) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#8a745c] mx-auto mb-4"></div>
          <p className="text-[#8a745c]">Loading restaurant data...</p>
        </div>
      </div>
    );
  }

  // Show error state if failed to load restaurant
  if (isEditing && loadError) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <p className="text-red-600 mb-4">Failed to load restaurant data</p>
          <button
            onClick={handleCancel}
            className="px-4 py-2 bg-[#8a745c] text-white rounded hover:bg-[#6d5a48]"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // Prepare initial data for editing
  const initialData = isEditing && restaurant ? {
    name: restaurant.name,
    description: restaurant.description || '',
    email: restaurant.email || '',
    phone: restaurant.phone || '',
    address: restaurant.address || '',
    city: restaurant.city || '',
    state: restaurant.state || '',
    country: restaurant.country || '',
    postalCode: restaurant.postalCode || '',
    timezone: restaurant.timezone,
    currency: restaurant.currency,
    status: restaurant.status,
    logo: restaurant.logo || '',
  } : undefined;

  return (
    <RestaurantForm
      initialData={initialData}
      onSubmit={handleSubmit}
      onCancel={handleCancel}
      isLoading={isLoading}
      isEditing={isEditing}
      className={className}
    />
  );
}

// Export types for convenience
export type { RestaurantFormContainerProps };
