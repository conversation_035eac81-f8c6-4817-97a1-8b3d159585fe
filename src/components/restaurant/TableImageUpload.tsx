'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { TableImage } from '@/components/ui/image-with-fallback';
import { useUploadTableImageMutation } from '@/lib/redux/api/endpoints/restaurant/tablesApi';
import { getTableImageByNumber } from '@/data/sample-table-images';
import { Upload, X, Camera, RotateCcw } from 'lucide-react';
import { toast } from 'sonner';

interface TableImageUploadProps {
  shopId: string;
  branchId: string;
  tableId: string;
  tableName: string;
  tableNumber: string;
  currentImageUrl?: string;
  onImageUploaded?: (imageUrl: string) => void;
  className?: string;
}

export function TableImageUpload({
  shopId,
  branchId,
  tableId,
  tableName,
  tableNumber,
  currentImageUrl,
  onImageUploaded,
  className = ''
}: TableImageUploadProps) {
  const [uploadImage, { isLoading: isUploading }] = useUploadTableImageMutation();
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const displayImageUrl = previewUrl || currentImageUrl || getTableImageByNumber(parseInt(tableNumber) || 1);

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);

    // Upload the file
    handleUpload(file);
  };

  const handleUpload = async (file: File) => {
    try {
      const result = await uploadImage({
        shopId,
        branchId,
        tableId,
        file
      }).unwrap();

      toast.success('Table image uploaded successfully');
      setPreviewUrl(null); // Clear preview since we now have the real URL
      onImageUploaded?.(result.imageUrl);
    } catch (error) {
      console.error('Upload error:', error);
      toast.error('Failed to upload image. Please try again.');
      setPreviewUrl(null); // Clear preview on error
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleChooseFile = () => {
    fileInputRef.current?.click();
  };

  const handleRemoveImage = () => {
    // TODO: Implement remove image API call
    setPreviewUrl(null);
    toast.success('Image removed successfully');
  };

  const handleResetToDefault = () => {
    setPreviewUrl(null);
    // This will show the default sample image
    toast.success('Reset to default image');
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Image Display */}
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="relative h-64 w-full">
            <TableImage
              src={displayImageUrl}
              alt={`${tableName} - Table ${tableNumber}`}
              className="w-full h-full object-cover"
            />
            
            {/* Loading overlay */}
            {isUploading && (
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                <div className="text-white text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
                  <p className="text-sm">Uploading...</p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Upload Controls */}
      <div className="space-y-3">
        {/* Drag and Drop Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            isDragOver
              ? 'border-[#e5ccb2] bg-[#f1edea]'
              : 'border-[#e2dcd4] hover:border-[#e5ccb2] hover:bg-[#f9f8f7]'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <Camera className="mx-auto h-8 w-8 text-[#8a745c] mb-2" />
          <p className="text-[#181510] font-medium mb-1">
            Drag and drop an image here, or click to browse
          </p>
          <p className="text-[#8a745c] text-sm mb-4">
            Supported formats: JPG, PNG. Max size: 5MB
          </p>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
            disabled={isUploading}
          />
          
          <Button
            onClick={handleChooseFile}
            disabled={isUploading}
            className="bg-[#e5ccb2] text-[#181510] hover:bg-[#d4b896]"
          >
            <Upload className="w-4 h-4 mr-2" />
            Choose File
          </Button>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          {currentImageUrl && (
            <Button
              variant="outline"
              onClick={handleRemoveImage}
              disabled={isUploading}
              className="flex-1 border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
            >
              <X className="w-4 h-4 mr-2" />
              Remove Image
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={handleResetToDefault}
            disabled={isUploading}
            className="flex-1 border-[#e2dcd4] text-[#8a745c] hover:bg-[#f1edea]"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Use Default
          </Button>
        </div>
      </div>
    </div>
  );
}
