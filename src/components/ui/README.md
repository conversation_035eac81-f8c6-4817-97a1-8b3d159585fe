# UI Components

This directory contains reusable UI components for the application.

## CircularRevealHeading

A circular loading component with text segments and image reveal on hover.

### Usage

```tsx
import { CircularRevealHeading } from "@/components/ui/circular-reveal-heading";

// Define the items for the circular reveal heading
const items = [
  {
    text: "ITEM ONE",
    image: "/path/to/image1.jpg"
  },
  {
    text: "ITEM TWO",
    image: "/path/to/image2.jpg"
  },
  {
    text: "ITEM THREE",
    image: "/path/to/image3.jpg"
  }
];

// Basic usage
<CircularRevealHeading
  items={items}
  centerText={
    <div className="text-center">
      <h2 className="text-xl font-semibold">Title</h2>
      <p className="text-sm">Subtitle</p>
    </div>
  }
  size="md"
/>

// Available sizes: 'sm', 'md', 'lg'
```

## AppLoading

A wrapper component for CircularRevealHeading that provides predefined loading states for different sections of the application.

### Usage

```tsx
import { AppLoading } from "@/components/ui/app-loading";

// Default loading
<AppLoading />

// Restaurant loading
<AppLoading type="restaurant" />

// Table loading
<AppLoading type="table" />

// Menu loading
<AppLoading type="menu" />

// Custom loading
<AppLoading
  type="default"
  title="Custom Title"
  subtitle="Custom subtitle text..."
  size="lg"
/>
```

### Props

- `type`: 'default' | 'restaurant' | 'table' | 'menu'
- `title`: Optional custom title
- `subtitle`: Optional custom subtitle
- `size`: 'sm' | 'md' | 'lg'
- `className`: Optional additional CSS classes

## ImageWithFallback

A reusable image component with fallback support for handling broken or missing images gracefully.

### Usage

```tsx
import {
  ImageWithFallback,
  MenuItemImage,
  ProductImage,
  AvatarImage,
  BannerImage
} from "@/components/ui/image-with-fallback";

// Basic usage
<ImageWithFallback
  src="/path/to/image.jpg"
  alt="Description"
  className="w-full h-full object-cover"
/>

// Menu item image (specialized variant)
<MenuItemImage
  src="/path/to/menu-item.jpg"
  alt="Menu item name"
/>

// Product image (specialized variant)
<ProductImage
  src="/path/to/product.jpg"
  alt="Product name"
/>

// Avatar image (specialized variant)
<AvatarImage
  src="/path/to/avatar.jpg"
  alt="User name"
/>

// Banner image (specialized variant)
<BannerImage
  src="/path/to/banner.jpg"
  alt="Banner description"
/>

// Custom fallback
<ImageWithFallback
  src="/path/to/image.jpg"
  alt="Description"
  fallback={<div>Custom fallback content</div>}
/>
```

### Props

- `src`: Image source URL (required)
- `alt`: Alt text for accessibility (required)
- `fallback`: Custom fallback React node
- `fallbackIcon`: Emoji or icon to show in default fallback (default: '🖼️')
- `fallbackText`: Text to show in default fallback (default: 'No Image Available')
- `containerClassName`: CSS classes for the container div
- `onError`: Callback function when image fails to load
- All standard img element props are supported

### Features

- **Graceful fallback**: Shows a nice fallback UI when images fail to load
- **Loading state**: Displays loading indicator while image loads
- **Specialized variants**: Pre-configured components for common use cases
- **Customizable**: Supports custom fallback content and styling
- **Accessible**: Proper alt text handling
- **Earth tone styling**: Matches the application's design system
