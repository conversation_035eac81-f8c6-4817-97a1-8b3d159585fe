'use client';

import { CircularRevealHeading } from "@/components/ui/circular-reveal-heading";
import { defaultItems, restaurantItems, tableItems, menuItems } from "@/lib/constants/loadingItems";

interface AppLoadingProps {
  type?: 'default' | 'restaurant' | 'table' | 'menu';
  title?: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function AppLoading({
  type = 'default',
  title,
  subtitle,
  size = 'md',
  className = ""
}: AppLoadingProps) {
  // Select the appropriate items based on the type
  const getItems = () => {
    switch (type) {
      case 'restaurant':
        return restaurantItems;
      case 'table':
        return tableItems;
      case 'menu':
        return menuItems;
      default:
        return defaultItems;
    }
  };

  // Set default title and subtitle based on type
  const getTitle = () => {
    if (title) return title;

    switch (type) {
      case 'restaurant':
        return 'DineDash';
      case 'table':
        return 'Table Manager';
      case 'menu':
        return 'Menu Manager';
      default:
        return 'Loading';
    }
  };

  const getSubtitle = () => {
    if (subtitle) return subtitle;

    switch (type) {
      case 'restaurant':
        return 'Loading restaurant data...';
      case 'table':
        return 'Loading table layout...';
      case 'menu':
        return 'Loading menu items...';
      default:
        return 'Please wait...';
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center min-h-[70vh] ${className}`} data-testid="app-loading">
      <CircularRevealHeading
        items={getItems()}
        centerText={
          <div className="text-center">
            <h2 className="text-xl font-semibold text-[#181510]">{getTitle()}</h2>
            <p className="text-sm text-[#8a745c]">{getSubtitle()}</p>
          </div>
        }
        size={size}
        className="mx-auto"
      />
    </div>
  );
}
