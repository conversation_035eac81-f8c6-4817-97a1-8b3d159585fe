'use client';

import Link from 'next/link';
import { ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  return (
    <nav className={cn('flex flex-wrap gap-2 p-4', className)}>
      {items.map((item, index) => {
        const isLast = index === items.length - 1;

        return (
          <div key={index} className="flex items-center">
            {item.href && !isLast ? (
              <Link
                href={item.href}
                className="text-muted-foreground text-base font-medium leading-normal hover:text-foreground"
              >
                {item.label}
              </Link>
            ) : (
              <span className={cn(
                'text-base font-medium leading-normal',
                isLast ? 'text-foreground' : 'text-muted-foreground'
              )}>
                {item.label}
              </span>
            )}

            {!isLast && (
              <span className="text-muted-foreground text-base font-medium leading-normal mx-2">/</span>
            )}
          </div>
        );
      })}
    </nav>
  );
}
