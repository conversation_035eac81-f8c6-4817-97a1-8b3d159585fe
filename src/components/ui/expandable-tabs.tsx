"use client"

import * as React from "react"
import { ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface ExpandableTabsProps extends React.HTMLAttributes<HTMLDivElement> {
  defaultValue?: string
  value?: string
  onValueChange?: (value: string) => void
  expandable?: boolean
  expandedDefault?: boolean
  className?: string
  children?: React.ReactNode
}

interface ExpandableTabsContextProps {
  value: string
  onValueChange: (value: string) => void
  expanded: boolean
  setExpanded: React.Dispatch<React.SetStateAction<boolean>>
}

const ExpandableTabsContext = React.createContext<ExpandableTabsContextProps | undefined>(undefined)

function useExpandableTabs() {
  const context = React.useContext(ExpandableTabsContext)
  if (!context) {
    throw new Error("useExpandableTabs must be used within a ExpandableTabs")
  }
  return context
}

const ExpandableTabs = React.forwardRef<HTMLDivElement, ExpandableTabsProps>(
  ({ defaultValue, value, onValueChange, expandable = true, expandedDefault = false, className, children, ...props }, ref) => {
    const [internalValue, setInternalValue] = React.useState(defaultValue || "")
    const [expanded, setExpanded] = React.useState(expandedDefault)

    const handleValueChange = React.useCallback(
      (newValue: string) => {
        setInternalValue(newValue)
        onValueChange?.(newValue)
      },
      [onValueChange]
    )

    const contextValue = React.useMemo(
      () => ({
        value: value !== undefined ? value : internalValue,
        onValueChange: handleValueChange,
        expanded,
        setExpanded,
      }),
      [value, internalValue, handleValueChange, expanded, setExpanded]
    )

    return (
      <ExpandableTabsContext.Provider value={contextValue}>
        <div
          ref={ref}
          className={cn(
            "relative flex flex-col items-start justify-start gap-2",
            className
          )}
          {...props}
        >
          {children}
        </div>
      </ExpandableTabsContext.Provider>
    )
  }
)
ExpandableTabs.displayName = "ExpandableTabs"

interface ExpandableTabsListProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string
  children?: React.ReactNode
}

const ExpandableTabsList = React.forwardRef<HTMLDivElement, ExpandableTabsListProps>(
  ({ className, children, ...props }, ref) => {
    const { expanded, setExpanded } = useExpandableTabs()

    return (
      <div
        ref={ref}
        className={cn(
          "relative flex w-full flex-row items-center justify-start gap-1 overflow-hidden rounded-md bg-muted p-1",
          className
        )}
        {...props}
      >
        <div className={cn("flex flex-1 flex-row flex-wrap items-center justify-start gap-1", expanded ? "flex-wrap" : "overflow-x-auto")}>
          {children}
        </div>
        <button
          type="button"
          className="flex h-8 w-8 shrink-0 items-center justify-center rounded-sm hover:bg-background/50"
          onClick={() => setExpanded(!expanded)}
        >
          <ChevronDown className={cn("h-4 w-4 transition-transform", expanded ? "rotate-180" : "")} />
          <span className="sr-only">{expanded ? "Collapse" : "Expand"}</span>
        </button>
      </div>
    )
  }
)
ExpandableTabsList.displayName = "ExpandableTabsList"

interface ExpandableTabsTriggerProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  value: string
  disabled?: boolean
  className?: string
  children?: React.ReactNode
}

const ExpandableTabsTrigger = React.forwardRef<HTMLButtonElement, ExpandableTabsTriggerProps>(
  ({ value, disabled = false, className, children, ...props }, ref) => {
    const { value: selectedValue, onValueChange } = useExpandableTabs()
    const isSelected = selectedValue === value

    return (
      <button
        ref={ref}
        type="button"
        role="tab"
        aria-selected={isSelected}
        disabled={disabled}
        className={cn(
          "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",
          isSelected
            ? "bg-background text-foreground shadow"
            : "text-muted-foreground hover:bg-background/50 hover:text-foreground",
          className
        )}
        onClick={() => onValueChange(value)}
        {...props}
      >
        {children}
      </button>
    )
  }
)
ExpandableTabsTrigger.displayName = "ExpandableTabsTrigger"

interface ExpandableTabsContentProps extends React.HTMLAttributes<HTMLDivElement> {
  value: string
  className?: string
  children?: React.ReactNode
}

const ExpandableTabsContent = React.forwardRef<HTMLDivElement, ExpandableTabsContentProps>(
  ({ value, className, children, ...props }, ref) => {
    const { value: selectedValue } = useExpandableTabs()
    const isSelected = selectedValue === value

    if (!isSelected) return null

    return (
      <div
        ref={ref}
        role="tabpanel"
        className={cn("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
ExpandableTabsContent.displayName = "ExpandableTabsContent"

export {
  ExpandableTabs,
  ExpandableTabsList,
  ExpandableTabsTrigger,
  ExpandableTabsContent,
}
