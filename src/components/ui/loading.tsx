'use client';

import { CircularRevealHeading } from "@/components/ui/circular-reveal-heading";
import { defaultItems, restaurantItems, tableItems, menuItems } from "@/lib/constants/loadingItems";

interface LoadingProps {
  items: Array<{
    text: string;
    image: string;
  }>;
  title?: string;
  subtitle?: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function Loading({
  items,
  title = "Loading",
  subtitle = "Please wait...",
  size = "md",
  className = ""
}: LoadingProps) {
  return (
    <div className={`flex flex-col items-center justify-center min-h-[50vh] ${className}`}>
      <CircularRevealHeading
        items={items}
        centerText={
          <div className="text-center">
            <h2 className="text-xl font-semibold text-foreground">{title}</h2>
            <p className="text-sm text-muted-foreground">{subtitle}</p>
          </div>
        }
        size={size}
        className="mx-auto"
      />
    </div>
  );
}

export function DefaultLoading({
  title = "Loading",
  subtitle = "Please wait...",
  size = "md",
  className = ""
}: Omit<LoadingProps, 'items'>) {
  return (
    <Loading
      items={defaultItems}
      title={title}
      subtitle={subtitle}
      size={size}
      className={className}
    />
  );
}

export function RestaurantLoading({
  title = "DineDash",
  subtitle = "Loading restaurant data...",
  size = "md",
  className = ""
}: Omit<LoadingProps, 'items'>) {
  return (
    <Loading
      items={restaurantItems}
      title={title}
      subtitle={subtitle}
      size={size}
      className={className}
    />
  );
}

export function TableLoading({
  title = "Table Manager",
  subtitle = "Loading table layout...",
  size = "md",
  className = ""
}: Omit<LoadingProps, 'items'>) {
  return (
    <Loading
      items={tableItems}
      title={title}
      subtitle={subtitle}
      size={size}
      className={className}
    />
  );
}

export function MenuLoading({
  title = "Menu Manager",
  subtitle = "Loading menu items...",
  size = "md",
  className = ""
}: Omit<LoadingProps, 'items'>) {
  return (
    <Loading
      items={menuItems}
      title={title}
      subtitle={subtitle}
      size={size}
      className={className}
    />
  );
}
