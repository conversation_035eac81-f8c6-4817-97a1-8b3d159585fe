'use client';

import React from 'react';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Skeleton } from '@/components/ui/skeleton';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';
import { LucideIcon, AlertCircle } from 'lucide-react';

export interface BreadcrumbItem {
  label: string;
  href?: string;
}

export interface StatBadge {
  label: string;
  value: number | string;
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  className?: string;
  icon?: LucideIcon;
}

export interface ActionButton {
  label: string;
  onClick: () => void;
  icon?: LucideIcon;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  className?: string;
}

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  breadcrumbItems?: BreadcrumbItem[];
  actions?: ActionButton[];
  stats?: StatBadge[];
  isLoading?: boolean;
  className?: string;
}

export function PageHeader({
  title,
  description,
  icon: Icon,
  breadcrumbItems = [],
  actions = [],
  stats = [],
  isLoading = false,
  className
}: PageHeaderProps) {
  if (isLoading) {
    return (
      <Card className={cn('mb-6', className)}>
        <CardHeader>
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </div>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={cn('mb-6', className)}>
      <CardHeader>
        {/* Breadcrumb Navigation */}
        {breadcrumbItems.length > 0 && (
          <Breadcrumb items={breadcrumbItems} className="mb-4" />
        )}

        {/* Header Content */}
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-2xl font-bold text-[#181510] flex items-center gap-2">
              {Icon && <Icon className="h-6 w-6 text-[#8a745c]" />}
              {title}
            </CardTitle>
            {description && (
              <p className="text-[#8a745c] mt-2">
                {description}
              </p>
            )}
          </div>

          {/* Action Buttons */}
          {actions.length > 0 && (
            <div className="flex items-center gap-2">
              {actions.map((action, index) => {
                const ActionIcon = action.icon;
                return (
                  <Button
                    key={index}
                    onClick={action.onClick}
                    variant={action.variant || 'default'}
                    className={cn(
                      action.variant === 'default' && 'bg-primary hover:bg-primary/90 text-primary-foreground',
                      action.className
                    )}
                  >
                    {ActionIcon && <ActionIcon className="h-4 w-4 mr-2" />}
                    {action.label}
                  </Button>
                );
              })}
            </div>
          )}
        </div>

        {/* Stats Overview */}
        {stats.length > 0 && (
          <div className="flex items-center gap-4 mt-4 pt-4 border-t border-border">
            {stats.map((stat, index) => {
              const StatIcon = stat.icon;
              return (
                <Badge
                  key={index}
                  variant={stat.variant || 'outline'}
                  className={cn(
                    stat.variant === 'outline' && 'text-primary border-primary',
                    stat.className
                  )}
                >
                  {StatIcon && <StatIcon className="h-3 w-3 mr-1" />}
                  {stat.label}: {stat.value}
                </Badge>
              );
            })}
          </div>
        )}
      </CardHeader>
    </Card>
  );
}

// Loading skeleton for PageHeader
export function PageHeaderSkeleton({ className }: { className?: string }) {
  return (
    <Card className={cn('mb-6', className)}>
      <CardHeader>
        <div className="space-y-2">
          <Skeleton className="h-4 w-32" />
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-4 w-96" />
        </div>
      </CardHeader>
    </Card>
  );
}

// Error state for PageHeader
export function PageHeaderError({
  title,
  description,
  icon: Icon,
  breadcrumbItems = [],
  actions = [],
  error,
  onRetry,
  className
}: {
  title: string;
  description?: string;
  icon?: LucideIcon;
  breadcrumbItems?: BreadcrumbItem[];
  actions?: ActionButton[];
  error?: any;
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <div className={cn('mb-6', className)}>
      <PageHeader
        title={title}
        description={description}
        icon={Icon}
        breadcrumbItems={breadcrumbItems}
        actions={actions}
      />

      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load data. {error && 'message' in error ? error.message : 'Please try again later.'}
          {onRetry && (
            <div className="mt-2">
              <button
                onClick={onRetry}
                className="text-sm underline hover:no-underline"
              >
                Try again
              </button>
            </div>
          )}
        </AlertDescription>
      </Alert>
    </div>
  );
}
