'use client';

import React, { useState } from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StarRatingProps {
  rating: number;
  maxRating?: number;
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
  onChange?: (rating: number) => void;
  readOnly?: boolean;
}

export function StarRating({
  rating,
  maxRating = 5,
  size = 'md',
  color = '#e58219', // Default to the app's orange color
  className,
  onChange,
  readOnly = true,
}: StarRatingProps) {
  const [hoverRating, setHoverRating] = useState(0);
  
  // Determine star size based on the size prop
  const starSizes = {
    sm: 'w-3 h-3',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };
  
  const starSize = starSizes[size];
  
  // Handle mouse enter on a star
  const handleMouseEnter = (index: number) => {
    if (readOnly) return;
    setHoverRating(index);
  };
  
  // Handle mouse leave on the rating component
  const handleMouseLeave = () => {
    if (readOnly) return;
    setHoverRating(0);
  };
  
  // Handle click on a star
  const handleClick = (index: number) => {
    if (readOnly) return;
    onChange?.(index);
  };
  
  return (
    <div 
      className={cn('flex items-center', className)}
      onMouseLeave={handleMouseLeave}
    >
      {[...Array(maxRating)].map((_, index) => {
        const starValue = index + 1;
        const isFilled = hoverRating ? starValue <= hoverRating : starValue <= rating;
        
        return (
          <Star
            key={index}
            className={cn(
              starSize,
              'transition-colors',
              isFilled ? 'fill-current text-current' : 'fill-transparent',
              !readOnly && 'cursor-pointer'
            )}
            style={{ color: isFilled ? color : '#e5e1dc' }}
            onMouseEnter={() => handleMouseEnter(starValue)}
            onClick={() => handleClick(starValue)}
          />
        );
      })}
    </div>
  );
}
