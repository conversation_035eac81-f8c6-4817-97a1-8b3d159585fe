'use client';

import React, { createContext, useContext, useCallback, useEffect } from 'react';
import { useWebSocket } from '@/hooks/useWebSocket';
import { useDispatch } from 'react-redux';
import { toast } from 'sonner';
import { apiSlice } from '@/lib/redux/api/apiSlice';

interface NotificationContextType {
  isConnected: boolean;
  isConnecting: boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: React.ReactNode;
  shopSlug?: string;
  branchSlug?: string;
}

export function NotificationProvider({ children, shopSlug, branchSlug }: NotificationProviderProps) {
  const dispatch = useDispatch();

  const handleWebSocketMessage = useCallback((message: any) => {
    console.log('Received WebSocket message:', message);

    switch (message.type) {
      case 'notification':
        // Invalidate notifications cache to refetch data
        dispatch(apiSlice.util.invalidateTags(['Notifications']));

        // Show toast notification for urgent/high priority notifications
        if (message.data?.priority === 'urgent' || message.data?.priority === 'high') {
          toast.info(message.data.title, {
            description: message.data.message,
            duration: 5000,
          });
        }
        break;

      case 'order_update':
        // Invalidate order-related cache
        dispatch(apiSlice.util.invalidateTags(['Orders']));

        // Show toast for order updates
        if (message.data?.status) {
          toast.info(`Order ${message.data.orderId} ${message.data.status}`, {
            duration: 3000,
          });
        }
        break;

      case 'table_update':
        // Invalidate table-related cache
        dispatch(apiSlice.util.invalidateTags(['Tables']));
        break;

      case 'error':
        console.error('WebSocket error:', message.data);
        break;

      default:
        console.log('Unknown WebSocket message type:', message.type);
    }
  }, [dispatch]);

  const handleConnect = useCallback(() => {
    console.log('WebSocket connected successfully');
  }, []);

  const handleDisconnect = useCallback(() => {
    console.log('WebSocket disconnected');
  }, []);

  const handleError = useCallback((error: Event) => {
    console.error('WebSocket connection error:', error);
  }, []);

  const { isConnected, isConnecting } = useWebSocket({
    onMessage: handleWebSocketMessage,
    onConnect: handleConnect,
    onDisconnect: handleDisconnect,
    onError: handleError,
    reconnectInterval: 3000,
    maxReconnectAttempts: 5
  });

  const contextValue: NotificationContextType = {
    isConnected,
    isConnecting
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
    </NotificationContext.Provider>
  );
}

export function useNotificationContext() {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotificationContext must be used within a NotificationProvider');
  }
  return context;
}
