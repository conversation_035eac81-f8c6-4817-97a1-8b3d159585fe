import { useSession, signIn, signOut } from 'next-auth/react'
import { useSelector } from 'react-redux'
import { RootState } from '@/lib/redux/store'

export function useAuth() {
  const { data: session, status } = useSession()
  const authState = useSelector((state: RootState) => state.auth)

  const login = async (email: string, password: string) => {
    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      })

      if (result?.error) {
        throw new Error(result.error)
      }

      return result
    } catch (error) {
      throw error
    }
  }

  const logout = async () => {
    await signOut({ redirect: false })
  }

  return {
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: status === 'loading' || authState.isLoading,
    error: authState.error,
    session,
    login,
    logout,
  }
}
