/**
 * Hook for making authenticated API calls with NextAuth session
 */

import { useSession } from 'next-auth/react'
import { useCallback } from 'react'

interface FetchOptions extends RequestInit {
  timeout?: number
}

interface AuthenticatedFetchOptions extends FetchOptions {
  includeAuth?: boolean
}

export function useAuthenticatedFetch() {
  const { data: session } = useSession()

  const authenticatedFetch = useCallback(async (
    url: string,
    options: AuthenticatedFetchOptions = {}
  ) => {
    const {
      timeout = 30000,
      includeAuth = true,
      headers = {},
      ...restOptions
    } = options

    // Default headers
    const defaultHeaders: HeadersInit = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      ...headers,
    }

    // Add authentication headers if session is available and includeAuth is true
    if (includeAuth && session?.user) {
      defaultHeaders['X-User-ID'] = session.user.id
      defaultHeaders['X-User-Email'] = session.user.email
      defaultHeaders['X-User-Role'] = session.user.role
    }

    // Create abort controller for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(url, {
        ...restOptions,
        headers: defaultHeaders,
        signal: controller.signal,
      })

      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }, [session])

  const get = useCallback((url: string, options: AuthenticatedFetchOptions = {}) => {
    return authenticatedFetch(url, { ...options, method: 'GET' })
  }, [authenticatedFetch])

  const post = useCallback((url: string, data?: any, options: AuthenticatedFetchOptions = {}) => {
    return authenticatedFetch(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }, [authenticatedFetch])

  const put = useCallback((url: string, data?: any, options: AuthenticatedFetchOptions = {}) => {
    return authenticatedFetch(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }, [authenticatedFetch])

  const del = useCallback((url: string, options: AuthenticatedFetchOptions = {}) => {
    return authenticatedFetch(url, { ...options, method: 'DELETE' })
  }, [authenticatedFetch])

  return {
    fetch: authenticatedFetch,
    get,
    post,
    put,
    delete: del,
    isAuthenticated: !!session?.user,
    user: session?.user,
  }
}
