/**
 * Custom hook for menu item management
 * Separates business logic from UI components
 */

import { useState, useCallback } from 'react';
import { useGetMenuItemsQuery, useCreateMenuItemMutation, useUpdateMenuItemMutation, useDeleteMenuItemMutation } from '@/lib/redux/api/endpoints/restaurant/menuItemApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface MenuItemFilters {
  category?: string;
  available?: boolean;
  featured?: boolean;
  search?: string;
  minPrice?: number;
  maxPrice?: number;
}

export interface UseMenuItemsOptions {
  merchantId: string;
  filters?: MenuItemFilters;
  page?: number;
  limit?: number;
}

export function useMenuItems({
  merchantId,
  filters = {},
  page = 1,
  limit = 10
}: UseMenuItemsOptions) {
  const [localFilters, setLocalFilters] = useState<MenuItemFilters>(filters);

  // RTK Query hooks
  const {
    data: menuItemsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetMenuItemsQuery({
    merchantId,
    ...localFilters,
    page,
    limit
  });

  const [createMenuItem, { isLoading: isCreating }] = useCreateMenuItemMutation();
  const [updateMenuItem, { isLoading: isUpdating }] = useUpdateMenuItemMutation();
  const [deleteMenuItem, { isLoading: isDeleting }] = useDeleteMenuItemMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<MenuItemFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setLocalFilters({});
  }, []);

  // Menu item actions
  const handleCreateMenuItem = useCallback(async (menuItemData: any) => {
    try {
      await createMenuItem({
        merchantId,
        ...menuItemData
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.MENU_ITEM_CREATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, createMenuItem, refetch]);

  const handleUpdateMenuItem = useCallback(async (menuItemId: string, updates: any) => {
    try {
      await updateMenuItem({
        merchantId,
        menuItemId,
        ...updates
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.MENU_ITEM_UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, updateMenuItem, refetch]);

  const handleDeleteMenuItem = useCallback(async (menuItemId: string) => {
    try {
      await deleteMenuItem({
        merchantId,
        menuItemId
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.MENU_ITEM_DELETED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, deleteMenuItem, refetch]);

  const handleToggleAvailability = useCallback(async (menuItemId: string, available: boolean) => {
    try {
      await updateMenuItem({
        merchantId,
        menuItemId,
        available
      }).unwrap();

      const message = available
        ? 'Menu item is now available'
        : 'Menu item is now unavailable';
      errorHandlers.showSuccessToast(message);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, updateMenuItem, refetch]);

  const handleToggleFeatured = useCallback(async (menuItemId: string, featured: boolean) => {
    try {
      await updateMenuItem({
        merchantId,
        menuItemId,
        featured
      }).unwrap();

      const message = featured
        ? 'Menu item is now featured'
        : 'Menu item removed from featured';
      errorHandlers.showSuccessToast(message);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, updateMenuItem, refetch]);

  // Status helpers
  const getAvailabilityColor = useCallback((available: boolean) => {
    return available
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-red-100 text-red-800 border-red-200';
  }, []);

  const getAvailabilityText = useCallback((available: boolean) => {
    return available ? MESSAGES.STATUS.ACTIVE : MESSAGES.STATUS.INACTIVE;
  }, []);

  const getCategoryColor = useCallback((category: string) => {
    const colors = {
      'appetizer': 'bg-orange-50 text-orange-700 border-orange-200 dark:bg-orange-950 dark:text-orange-300',
      'main': 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300',
      'dessert': 'bg-pink-50 text-pink-700 border-pink-200 dark:bg-pink-950 dark:text-pink-300',
      'beverage': 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300',
      'side': 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300',
    };
    return colors[category.toLowerCase() as keyof typeof colors] || 'bg-muted text-muted-foreground border-border';
  }, []);

  // Data processing
  const menuItems = menuItemsData?.data || [];
  const pagination = menuItemsData?.pagination;
  const totalCount = pagination?.totalItems || 0;

  // Filter statistics
  const categoryCounts = menuItems.reduce((acc, item) => {
    const category = item.category || 'Uncategorized';
    acc[category] = (acc[category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const availableCount = menuItems.filter(item => item.available).length;
  const featuredCount = menuItems.filter(item => item.featured).length;

  return {
    // Data
    menuItems,
    pagination,
    totalCount,
    categoryCounts,
    availableCount,
    featuredCount,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isError,
    error,

    // Filters
    filters: localFilters,
    updateFilters,
    clearFilters,

    // Actions
    createMenuItem: handleCreateMenuItem,
    updateMenuItem: handleUpdateMenuItem,
    deleteMenuItem: handleDeleteMenuItem,
    toggleAvailability: handleToggleAvailability,
    toggleFeatured: handleToggleFeatured,
    refetch,

    // Helpers
    getAvailabilityColor,
    getAvailabilityText,
    getCategoryColor,
  };
}

// Hook for single menu item
export function useMenuItem(merchantId: string, menuItemId: string) {
  const {
    data: menuItem,
    isLoading,
    isError,
    error,
    refetch
  } = useGetMenuItemsQuery({
    merchantId,
    menuItemId
  });

  const [updateMenuItem, { isLoading: isUpdating }] = useUpdateMenuItemMutation();
  const [deleteMenuItem, { isLoading: isDeleting }] = useDeleteMenuItemMutation();

  const handleUpdate = useCallback(async (updates: any) => {
    try {
      await updateMenuItem({
        merchantId,
        menuItemId,
        ...updates
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.MENU_ITEM_UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, menuItemId, updateMenuItem, refetch]);

  const handleDelete = useCallback(async () => {
    try {
      await deleteMenuItem({
        merchantId,
        menuItemId
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.MENU_ITEM_DELETED);
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [merchantId, menuItemId, deleteMenuItem]);

  return {
    menuItem,
    isLoading,
    isUpdating,
    isDeleting,
    isError,
    error,
    updateMenuItem: handleUpdate,
    deleteMenuItem: handleDelete,
    refetch,
  };
}
