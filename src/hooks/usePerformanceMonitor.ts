/**
 * Performance monitoring hook to track API call efficiency
 * Helps measure the impact of our optimizations
 */

import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  apiCallStart: number;
  apiCallEnd?: number;
  dataSize?: number;
  cacheHit?: boolean;
}

export function usePerformanceMonitor(
  operationName: string,
  isLoading: boolean,
  data?: any
) {
  const metricsRef = useRef<PerformanceMetrics>({ apiCallStart: 0 });

  useEffect(() => {
    if (isLoading && metricsRef.current.apiCallStart === 0) {
      // API call started
      metricsRef.current.apiCallStart = performance.now();
      console.log(`🚀 [${operationName}] API call started`);
    }
  }, [isLoading, operationName]);

  useEffect(() => {
    if (!isLoading && metricsRef.current.apiCallStart > 0 && !metricsRef.current.apiCallEnd) {
      // API call completed
      metricsRef.current.apiCallEnd = performance.now();
      const duration = metricsRef.current.apiCallEnd - metricsRef.current.apiCallStart;
      
      // Calculate data size (rough estimate)
      const dataSize = data ? JSON.stringify(data).length : 0;
      metricsRef.current.dataSize = dataSize;

      console.log(`✅ [${operationName}] API call completed:`, {
        duration: `${duration.toFixed(2)}ms`,
        dataSize: `${(dataSize / 1024).toFixed(2)}KB`,
        efficiency: duration < 500 ? '🟢 Fast' : duration < 1000 ? '🟡 Moderate' : '🔴 Slow'
      });

      // Reset for next measurement
      metricsRef.current = { apiCallStart: 0 };
    }
  }, [isLoading, data, operationName]);

  return metricsRef.current;
}

/**
 * Hook to compare old vs new API patterns
 */
export function useApiPatternComparison(
  patternName: string,
  isOptimized: boolean,
  isLoading: boolean,
  data?: any
) {
  const pattern = isOptimized ? 'Optimized' : 'Legacy';
  const operationName = `${patternName} (${pattern})`;
  
  return usePerformanceMonitor(operationName, isLoading, data);
}
