/**
 * Enhanced useReports hook with backend-driven filtering, sorting, and pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { useState, useMemo, useCallback } from 'react';
import {
  useGetReportsQuery,
  ReportsFilters,
  ReportsListResponse,
  ReportsResponse
} from '@/lib/redux/api/endpoints/restaurant/reportsApi';

export interface UseReportsOptions {
  shopId: string;
  branchId: string;
  initialFilters?: ReportsFilters;
}

export function useReports({
  shopId,
  branchId,
  initialFilters = {}
}: UseReportsOptions) {
  const [filters, setFilters] = useState<ReportsFilters>({
    page: 1,
    limit: 20,
    sort_by: 'created_at',
    sort_order: 'desc',
    period: 'month',
    ...initialFilters
  });

  // Main reports query with backend-driven filtering
  const {
    data: reportsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReportsQuery({
    shopId,
    branchId,
    filters
  });

  // Extract data from the response
  const reports = reportsData?.data || [];
  const salesTrends = reportsData?.salesTrends || [];
  const popularItems = reportsData?.popularItems || [];
  const customerAnalytics = reportsData?.customerAnalytics;
  const revenueAnalytics = reportsData?.revenueAnalytics;
  const summary = reportsData?.summary;

  // Statistics calculation
  const stats = useMemo(() => {
    const totalReports = summary?.totalReports || 0;
    const totalRevenue = summary?.totalRevenue || 0;
    const totalOrders = summary?.totalOrders || 0;
    const averageOrderValue = summary?.averageOrderValue || 0;
    const revenueGrowth = summary?.revenueGrowth || 0;
    const ordersGrowth = summary?.ordersGrowth || 0;

    return {
      totalReports,
      totalRevenue,
      totalOrders,
      averageOrderValue,
      revenueGrowth,
      ordersGrowth,
    };
  }, [summary]);

  // Filter management functions
  const updateFilters = useCallback((newFilters: Partial<ReportsFilters>) => {
    setFilters(prev => ({
      ...prev,
      ...newFilters
    }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 20,
      sort_by: 'created_at',
      sort_order: 'desc',
      period: 'month',
    });
  }, []);

  const setSearch = useCallback((search: string) => {
    setFilters(prev => ({
      ...prev,
      search: search || undefined,
      page: 1 // Reset to first page when searching
    }));
  }, []);

  const setPeriod = useCallback((period: ReportsFilters['period']) => {
    setFilters(prev => ({
      ...prev,
      period: period || undefined,
      page: 1 // Reset to first page when filtering
    }));
  }, []);

  const setCategory = useCallback((category: string) => {
    setFilters(prev => ({
      ...prev,
      category: category || undefined,
      page: 1 // Reset to first page when filtering
    }));
  }, []);

  const setReportType = useCallback((report_type: ReportsFilters['report_type']) => {
    setFilters(prev => ({
      ...prev,
      report_type: report_type || undefined,
      page: 1 // Reset to first page when filtering
    }));
  }, []);

  const setDateRange = useCallback((startDate?: string, endDate?: string) => {
    setFilters(prev => ({
      ...prev,
      startDate: startDate || undefined,
      endDate: endDate || undefined,
      page: 1 // Reset to first page when filtering
    }));
  }, []);

  const setSorting = useCallback((sort_by: ReportsFilters['sort_by'], sort_order: ReportsFilters['sort_order'] = 'asc') => {
    setFilters(prev => ({
      ...prev,
      sort_by,
      sort_order,
      page: 1 // Reset to first page when sorting
    }));
  }, []);

  const setPage = useCallback((page: number) => {
    setFilters(prev => ({ ...prev, page }));
  }, []);

  const setLimit = useCallback((limit: number) => {
    setFilters(prev => ({
      ...prev,
      limit,
      page: 1 // Reset to first page when changing limit
    }));
  }, []);

  // Convenience functions for common operations
  const getActiveReports = () => {
    return reports.filter(report => report.status === 'ready');
  };

  const getReportsByCategory = (category: string) => {
    return reports.filter(report => report.category === category);
  };

  const getReportsByType = (type: string) => {
    return reports.filter(report => report.report_type === type);
  };

  // Utility functions
  const formatCurrency = useCallback((amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }, []);

  const formatPercentage = useCallback((value: number, decimals = 1) => {
    return `${value.toFixed(decimals)}%`;
  }, []);

  const calculateGrowth = useCallback((current: number, previous: number) => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  }, []);

  // Get period options for filters
  const periodOptions = useMemo(() => [
    { value: 'day', label: 'Daily' },
    { value: 'week', label: 'Weekly' },
    { value: 'month', label: 'Monthly' },
    { value: 'quarter', label: 'Quarterly' },
    { value: 'year', label: 'Yearly' }
  ], []);

  // Get category options for filters
  const categoryOptions = useMemo(() => [
    { value: 'sales', label: 'Sales' },
    { value: 'customers', label: 'Customers' },
    { value: 'staff', label: 'Staff' },
    { value: 'tables', label: 'Tables' },
    { value: 'financial', label: 'Financial' }
  ], []);

  // Get report type options for filters
  const reportTypeOptions = useMemo(() => [
    { value: 'sales', label: 'Sales Reports' },
    { value: 'customers', label: 'Customer Analytics' },
    { value: 'staff', label: 'Staff Performance' },
    { value: 'tables', label: 'Table Utilization' },
    { value: 'popular-items', label: 'Popular Items' }
  ], []);

  return {
    // Data
    reports,
    reportsData,
    salesTrends,
    popularItems,
    customerAnalytics,
    revenueAnalytics,
    stats,

    // Pagination
    total: reportsData?.total || 0,
    page: reportsData?.page || 1,
    limit: reportsData?.limit || 20,
    totalPages: reportsData?.totalPages || 0,

    // Loading and error states
    isLoading,
    isError,
    error,

    // Filters
    filters,
    updateFilters,
    clearFilters,
    setSearch,
    setPeriod,
    setCategory,
    setReportType,
    setDateRange,
    setSorting,
    setPage,
    setLimit,

    // Filter options
    periodOptions,
    categoryOptions,
    reportTypeOptions,

    // Actions
    refetch,

    // Convenience functions
    getActiveReports,
    getReportsByCategory,
    getReportsByType,

    // Utilities
    formatCurrency,
    formatPercentage,
    calculateGrowth,
  };
}