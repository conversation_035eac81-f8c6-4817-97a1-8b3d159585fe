/**
 * Custom hook for restaurant management
 * Separates business logic from UI components
 */

import { useState, useCallback } from 'react';
import { useGetShopsQuery, useGetShopQuery, useCreateShopMutation, useUpdateShopMutation, useDeleteShopMutation } from '@/lib/redux/api/endpoints/restaurant/shopApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface RestaurantFilters {
  status?: string;
  search?: string;
  city?: string;
  country?: string;
}

export interface UseRestaurantOptions {
  filters?: RestaurantFilters;
  page?: number;
  limit?: number;
}

export function useRestaurants({
  filters = {},
  page = 1,
  limit = 10
}: UseRestaurantOptions) {
  const [localFilters, setLocalFilters] = useState<RestaurantFilters>(filters);

  // RTK Query hooks
  const {
    data: restaurantsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetShopsQuery({
    page,
    limit,
    sort_by: 'created_at',
    sort_order: 'desc',
    ...localFilters
  });

  const [createRestaurant, { isLoading: isCreating }] = useCreateShopMutation();
  const [updateRestaurant, { isLoading: isUpdating }] = useUpdateShopMutation();
  const [deleteRestaurant, { isLoading: isDeleting }] = useDeleteShopMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<RestaurantFilters>) => {
    setLocalFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setLocalFilters({});
  }, []);

  // Transform frontend data to backend format
  const transformRestaurantData = useCallback((restaurantData: any) => {
    // Ensure we have a name
    if (!restaurantData.name || restaurantData.name.trim() === '') {
      throw new Error('Restaurant name is required');
    }

    // Generate slug from name if not provided
    const slug = restaurantData.slug || restaurantData.name
      .toLowerCase()
      .trim()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '');

    const transformedData = {
      name: restaurantData.name.trim(),
      slug: slug,
      description: restaurantData.description || '',
      shop_type: 'restaurant', // Default shop type
      email: restaurantData.email || '',
      phone: restaurantData.phone || '',
      website: restaurantData.website || '',
      logo: restaurantData.logo || '',
      cover_image: restaurantData.coverImage || '',
      address: {
        street: restaurantData.address || '',
        city: restaurantData.city || '',
        state: restaurantData.state || '',
        zip_code: restaurantData.postalCode || '',
        country: restaurantData.country || ''
      },
      cuisine_type: restaurantData.cuisine || 'general',
      price_range: restaurantData.priceRange || 'medium',
      social_media: {
        facebook: '',
        instagram: '',
        twitter: '',
        linkedin: '',
        youtube: '',
        tiktok: ''
      },
      business_hours: {
        monday: '09:00-22:00',
        tuesday: '09:00-22:00',
        wednesday: '09:00-22:00',
        thursday: '09:00-22:00',
        friday: '09:00-23:00',
        saturday: '10:00-23:00',
        sunday: '10:00-22:00'
      }
    };

    console.log('Transformed restaurant data:', transformedData);
    return transformedData;
  }, []);

  // Restaurant actions
  const handleCreateRestaurant = useCallback(async (restaurantData: any) => {
    try {
      const transformedData = transformRestaurantData(restaurantData);
      await createRestaurant(transformedData).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.CREATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [createRestaurant, refetch, transformRestaurantData]);

  const handleUpdateRestaurant = useCallback(async (restaurantId: string, updates: any) => {
    try {
      await updateRestaurant({
        id: restaurantId,
        ...updates
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateRestaurant, refetch]);

  const handleDeleteRestaurant = useCallback(async (restaurantId: string) => {
    try {
      await deleteRestaurant(restaurantId).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteRestaurant, refetch]);

  // Status helpers
  const getStatusColor = useCallback((status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'inactive':
        return 'bg-muted text-muted-foreground border-border';
      case 'suspended':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  }, []);

  const getStatusText = useCallback((status: string) => {
    return MESSAGES.STATUS[status.toUpperCase() as keyof typeof MESSAGES.STATUS] || status;
  }, []);

  // Data processing
  const restaurants = restaurantsData?.data || [];
  const totalCount = restaurantsData?.total || 0;

  // Filter statistics
  const statusCounts = restaurants.reduce((acc, restaurant) => {
    const status = restaurant.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const cityCounts = restaurants.reduce((acc, restaurant) => {
    const city = restaurant.address?.city || 'Unknown';
    acc[city] = (acc[city] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return {
    // Data
    restaurants,
    totalCount,
    statusCounts,
    cityCounts,

    // Loading states
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isError,
    error,

    // Filters
    filters: localFilters,
    updateFilters,
    clearFilters,

    // Actions
    createRestaurant: handleCreateRestaurant,
    updateRestaurant: handleUpdateRestaurant,
    deleteRestaurant: handleDeleteRestaurant,
    refetch,

    // Helpers
    getStatusColor,
    getStatusText,
  };
}

// Hook for single restaurant
export function useRestaurant(restaurantId: string) {
  const {
    data: restaurant,
    isLoading,
    isError,
    error,
    refetch
  } = useGetShopQuery(restaurantId);

  const [updateRestaurant, { isLoading: isUpdating }] = useUpdateShopMutation();
  const [deleteRestaurant, { isLoading: isDeleting }] = useDeleteShopMutation();

  const handleUpdate = useCallback(async (updates: any) => {
    try {
      await updateRestaurant({
        id: restaurantId,
        ...updates
      }).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.UPDATED);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [restaurantId, updateRestaurant, refetch]);

  const handleDelete = useCallback(async () => {
    try {
      await deleteRestaurant(restaurantId).unwrap();

      errorHandlers.showSuccessToast(MESSAGES.SUCCESS.DELETED);
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [restaurantId, deleteRestaurant]);

  return {
    restaurant,
    isLoading,
    isUpdating,
    isDeleting,
    isError,
    error,
    updateRestaurant: handleUpdate,
    deleteRestaurant: handleDelete,
    refetch,
  };
}
