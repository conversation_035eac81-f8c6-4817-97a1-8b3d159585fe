/**
 * Custom hook for reviews management
 * Separates business logic from UI components
 */

import { useState, useCallback, useMemo } from 'react';
import {
  useGetReviewsQuery,
  useGetReviewQuery,
  useGetReviewStatsQuery,
  useGetRecentReviewsQuery,
  useGetPendingReviewsQuery,
  useRespondToReviewMutation,
  useUpdateReviewResponseMutation,
  useDeleteReviewResponseMutation,
  useUpdateReviewStatusMutation,
  useFlagReviewMutation,
  useHideReviewMutation,
  useShowReviewMutation,
  useSyncExternalReviewsMutation,
  useExportReviewsMutation,
  useGetReviewInsightsQuery,
  // Slug-based hooks
  useRespondToReviewBySlugMutation,
  useUpdateReviewStatusBySlugMutation,
  useFlagReviewBySlugMutation,
  useApproveReviewBySlugMutation,
  useRejectReviewBySlugMutation,
  type Review,
  type ReviewFilters,
} from '@/lib/redux/api/endpoints/restaurant/reviewsApi';
import { errorHandlers } from '@/lib/utils/error-handling';
import { MESSAGES } from '@/lib/constants/messages';

export interface UseReviewsOptions {
  shopSlug: string;
  branchSlug: string;
  shopId?: string;
  branchId?: string;
  initialFilters?: ReviewFilters;
}

export function useReviews({
  shopSlug,
  branchSlug,
  shopId,
  branchId,
  initialFilters = {}
}: UseReviewsOptions) {
  const [filters, setFilters] = useState<ReviewFilters>({
    page: 1,
    limit: 20,
    ...initialFilters
  });

  // Skip queries if shopSlug or branchSlug is not available
  const skipQueries = !shopSlug || !branchSlug;

  // API queries
  const {
    data: reviewsData,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReviewsQuery({
    shopSlug,
    branchSlug,
    filters
  }, {
    skip: skipQueries,
    // Add error handling options to prevent infinite loops
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  });

  const {
    data: reviewStats,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useGetReviewStatsQuery({
    shopSlug,
    branchSlug
  }, {
    skip: skipQueries
  });

  const {
    data: recentReviews,
    isLoading: isLoadingRecent,
    refetch: refetchRecent
  } = useGetRecentReviewsQuery({
    shopSlug,
    branchSlug,
    limit: 10
  }, {
    skip: skipQueries
  });

  const {
    data: pendingReviews,
    isLoading: isLoadingPending,
    refetch: refetchPending
  } = useGetPendingReviewsQuery({
    shopSlug,
    branchSlug
  }, {
    skip: skipQueries
  });

  const {
    data: reviewInsights,
    isLoading: isLoadingInsights,
    refetch: refetchInsights
  } = useGetReviewInsightsQuery({
    shopSlug,
    branchSlug
  }, {
    skip: skipQueries
  });

  // Mutations - use slug-based versions when available
  const [respondToReviewBySlug, { isLoading: isResponding }] = useRespondToReviewBySlugMutation();
  const [updateStatusBySlug, { isLoading: isUpdatingStatus }] = useUpdateReviewStatusBySlugMutation();
  const [flagReviewBySlug, { isLoading: isFlagging }] = useFlagReviewBySlugMutation();
  const [approveReviewBySlug, { isLoading: isApproving }] = useApproveReviewBySlugMutation();
  const [rejectReviewBySlug, { isLoading: isRejecting }] = useRejectReviewBySlugMutation();

  // Legacy mutations for ID-based operations
  const [respondToReview] = useRespondToReviewMutation();
  const [updateResponse, { isLoading: isUpdatingResponse }] = useUpdateReviewResponseMutation();
  const [deleteResponse, { isLoading: isDeletingResponse }] = useDeleteReviewResponseMutation();
  const [updateStatus] = useUpdateReviewStatusMutation();
  const [flagReview] = useFlagReviewMutation();
  const [hideReview, { isLoading: isHiding }] = useHideReviewMutation();
  const [showReview, { isLoading: isShowing }] = useShowReviewMutation();
  const [syncReviews, { isLoading: isSyncing }] = useSyncExternalReviewsMutation();
  const [exportReviews, { isLoading: isExporting }] = useExportReviewsMutation();

  // Filter management
  const updateFilters = useCallback((newFilters: Partial<ReviewFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({ page: 1, limit: 20 });
  }, []);

  // Review actions - use slug-based mutations
  const handleRespondToReview = useCallback(async (reviewId: string, message: string) => {
    try {
      await respondToReviewBySlug({
        shopSlug,
        branchSlug,
        reviewId,
        response: message,
        respondedBy: 'Restaurant Manager'
      }).unwrap();

      errorHandlers.showSuccessToast('Response posted successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [respondToReviewBySlug, shopSlug, branchSlug, refetch, refetchStats]);

  const handleUpdateResponse = useCallback(async (reviewId: string, message: string) => {
    try {
      await updateResponse({
        shopId,
        branchId,
        reviewId,
        message
      }).unwrap();

      errorHandlers.showSuccessToast('Response updated successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateResponse, shopId, branchId, refetch]);

  const handleDeleteResponse = useCallback(async (reviewId: string) => {
    try {
      await deleteResponse({
        shopId,
        branchId,
        reviewId
      }).unwrap();

      errorHandlers.showSuccessToast('Response deleted successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [deleteResponse, shopId, branchId, refetch, refetchStats]);

  const handleUpdateStatus = useCallback(async (reviewId: string, status: string) => {
    try {
      // Use specific slug-based mutations for common actions
      if (status === 'approved') {
        await approveReviewBySlug({
          shopSlug,
          branchSlug,
          reviewId
        }).unwrap();
      } else if (status === 'rejected') {
        await rejectReviewBySlug({
          shopSlug,
          branchSlug,
          reviewId
        }).unwrap();
      } else if (status === 'flagged') {
        await flagReviewBySlug({
          shopSlug,
          branchSlug,
          reviewId
        }).unwrap();
      } else {
        // Use generic status update for other statuses
        await updateStatusBySlug({
          shopSlug,
          branchSlug,
          reviewId,
          status
        }).unwrap();
      }

      errorHandlers.showSuccessToast(`Review ${status} successfully`);
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStatusBySlug, approveReviewBySlug, rejectReviewBySlug, flagReviewBySlug, shopSlug, branchSlug, refetch, refetchStats]);

  const handleFlagReview = useCallback(async (reviewId: string, reason: string) => {
    try {
      await flagReview({
        shopId,
        branchId,
        reviewId,
        reason
      }).unwrap();

      errorHandlers.showSuccessToast('Review flagged successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [flagReview, shopId, branchId, refetch]);

  const handleHideReview = useCallback(async (reviewId: string) => {
    try {
      await hideReview({
        shopId,
        branchId,
        reviewId
      }).unwrap();

      errorHandlers.showSuccessToast('Review hidden successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [hideReview, shopId, branchId, refetch, refetchStats]);

  const handleShowReview = useCallback(async (reviewId: string) => {
    try {
      await showReview({
        shopId,
        branchId,
        reviewId
      }).unwrap();

      errorHandlers.showSuccessToast('Review shown successfully');
      refetch();
      refetchStats();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [showReview, shopId, branchId, refetch, refetchStats]);

  const handleSyncReviews = useCallback(async (sources?: string[]) => {
    try {
      const result = await syncReviews({
        shopId,
        branchId,
        sources
      }).unwrap();

      errorHandlers.showSuccessToast(`Synced ${result.synced} reviews successfully`);
      refetch();
      refetchStats();
      refetchRecent();
      refetchPending();

      if (result.errors.length > 0) {
        console.warn('Sync errors:', result.errors);
      }

      return result;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [syncReviews, shopId, branchId, refetch, refetchStats, refetchRecent, refetchPending]);

  const handleExportReviews = useCallback(async (format: 'csv' | 'excel' | 'pdf') => {
    try {
      const result = await exportReviews({
        shopId,
        branchId,
        format,
        filters
      }).unwrap();

      errorHandlers.showSuccessToast('Export started successfully');
      return result.downloadUrl;
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [exportReviews, shopId, branchId, filters]);

  // Rating helpers
  const getRatingColor = useCallback((rating: number) => {
    if (rating >= 4) return 'text-green-600 dark:text-green-400';
    if (rating >= 3) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  }, []);

  const getSentimentColor = useCallback((sentiment: string) => {
    switch (sentiment.toLowerCase()) {
      case 'positive':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'neutral':
        return 'bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-950 dark:text-yellow-300';
      case 'negative':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  }, []);

  const getSourceColor = useCallback((source: string) => {
    switch (source.toLowerCase()) {
      case 'google':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'yelp':
        return 'bg-red-50 text-red-700 border-red-200 dark:bg-red-950 dark:text-red-300';
      case 'facebook':
        return 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300';
      case 'tripadvisor':
        return 'bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300';
      case 'internal':
        return 'bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-950 dark:text-purple-300';
      default:
        return 'bg-muted text-muted-foreground border-border';
    }
  }, []);

  // Format date
  const formatDate = useCallback((date: string) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }, []);

  // Data processing
  const reviews = reviewsData?.data || [];
  const pagination = reviewsData?.pagination;

  // Statistics
  const ratingCounts = useMemo(() => {
    return reviews.reduce((acc, review) => {
      const rating = review.rating;
      acc[rating] = (acc[rating] || 0) + 1;
      return acc;
    }, {} as Record<number, number>);
  }, [reviews]);

  const sourceCounts = useMemo(() => {
    return reviews.reduce((acc, review) => {
      const source = review.source;
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [reviews]);

  const sentimentCounts = useMemo(() => {
    return reviews.reduce((acc, review) => {
      const sentiment = review.sentiment;
      acc[sentiment] = (acc[sentiment] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [reviews]);

  return {
    // Data
    reviews,
    reviewStats,
    recentReviews: recentReviews || [],
    pendingReviews: pendingReviews || [],
    reviewInsights,
    pagination,
    ratingCounts,
    sourceCounts,
    sentimentCounts,

    // Loading states
    isLoading,
    isLoadingStats,
    isLoadingRecent,
    isLoadingPending,
    isLoadingInsights,
    isResponding,
    isUpdatingResponse,
    isDeletingResponse,
    isUpdatingStatus,
    isFlagging,
    isApproving,
    isRejecting,
    isHiding,
    isShowing,
    isSyncing,
    isExporting,
    isError,
    error,

    // Filters
    filters,
    updateFilters,
    clearFilters,

    // Actions
    respondToReview: handleRespondToReview,
    updateResponse: handleUpdateResponse,
    deleteResponse: handleDeleteResponse,
    updateStatus: handleUpdateStatus,
    flagReview: handleFlagReview,
    hideReview: handleHideReview,
    showReview: handleShowReview,
    syncReviews: handleSyncReviews,
    exportReviews: handleExportReviews,

    // Helpers
    getRatingColor,
    getSentimentColor,
    getSourceColor,
    formatDate,
    refetch,
    refetchStats,
    refetchRecent,
    refetchPending,
    refetchInsights,
  };
}

// Hook for single review
export function useReview(shopId: string, branchId: string, reviewId: string) {
  const skipQuery = !shopId || !branchId || !reviewId;

  const {
    data: review,
    isLoading,
    isError,
    error,
    refetch
  } = useGetReviewQuery({
    shopId,
    branchId,
    reviewId
  }, {
    skip: skipQuery
  });

  const [respondToReview, { isLoading: isResponding }] = useRespondToReviewMutation();
  const [updateResponse, { isLoading: isUpdatingResponse }] = useUpdateReviewResponseMutation();
  const [updateStatus, { isLoading: isUpdatingStatus }] = useUpdateReviewStatusMutation();

  const handleRespond = useCallback(async (message: string) => {
    try {
      await respondToReview({
        shopId,
        branchId,
        reviewId,
        message
      }).unwrap();

      errorHandlers.showSuccessToast('Response posted successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [respondToReview, shopId, branchId, reviewId, refetch]);

  const handleUpdateResponse = useCallback(async (message: string) => {
    try {
      await updateResponse({
        shopId,
        branchId,
        reviewId,
        message
      }).unwrap();

      errorHandlers.showSuccessToast('Response updated successfully');
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateResponse, shopId, branchId, reviewId, refetch]);

  const handleUpdateStatus = useCallback(async (status: string) => {
    try {
      await updateStatus({
        shopId,
        branchId,
        reviewId,
        status
      }).unwrap();

      errorHandlers.showSuccessToast(`Review ${status} successfully`);
      refetch();
    } catch (error) {
      errorHandlers.showErrorToast(error as Error);
      throw error;
    }
  }, [updateStatus, shopId, branchId, reviewId, refetch]);

  return {
    review,
    isLoading,
    isResponding,
    isUpdatingResponse,
    isUpdatingStatus,
    isError,
    error,
    respondToReview: handleRespond,
    updateResponse: handleUpdateResponse,
    updateStatus: handleUpdateStatus,
    refetch,
  };
}
