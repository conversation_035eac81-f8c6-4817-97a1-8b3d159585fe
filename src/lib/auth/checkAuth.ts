import { NextRequest } from 'next/server';
import { supabase } from '@/lib/supabase/client';

/**
 * Helper function to check authentication
 * @param request The Next.js request object
 * @returns Object containing authentication status and user if authenticated
 */
export async function checkAuth(request: NextRequest) {
  // For demo purposes, we'll skip actual authentication
  // In a real app, you would validate the token with your auth provider
  
  // Uncomment the following code to implement real authentication with Supabase
  /*
  const authHeader = request.headers.get('authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return { authenticated: false };
  }
  
  const token = authHeader.split(' ')[1];
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    return { authenticated: false };
  }
  
  return { authenticated: true, user };
  */
  
  // For demo purposes, always return authenticated
  return { authenticated: true, user: { id: 'demo-user-id' } };
}
