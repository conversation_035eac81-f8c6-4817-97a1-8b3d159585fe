import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import GoogleProvider from "next-auth/providers/google"
import GitH<PERSON><PERSON>rovider from "next-auth/providers/github"
import FacebookProvider from "next-auth/providers/facebook"
import Discord<PERSON><PERSON>ider from "next-auth/providers/discord"
import { supabase } from "@/lib/supabase/client"

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    user: {
      id: string
      email: string
      name?: string
      role: string
    }
  }

  interface User {
    id: string
    email: string
    name?: string
    role: string
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    role: string
    backendToken?: string
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    // OAuth Providers
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code"
        }
      }
    }),
    GitHubProvider({
      clientId: process.env.GITHUB_CLIENT_ID!,
      clientSecret: process.env.GITHUB_CLIENT_SECRET!,
    }),
    FacebookProvider({
      clientId: process.env.FACEBOOK_CLIENT_ID!,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET!,
    }),
    DiscordProvider({
      clientId: process.env.DISCORD_CLIENT_ID!,
      clientSecret: process.env.DISCORD_CLIENT_SECRET!,
    }),
    // Credentials Provider
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error("Email and password are required")
        }

        try {
          const { data, error } = await supabase.auth.signInWithPassword({
            email: credentials.email,
            password: credentials.password,
          })

          if (error) {
            throw new Error(error.message)
          }

          if (!data.user) {
            throw new Error("Invalid credentials")
          }

          return {
            id: data.user.id,
            email: data.user.email!,
            name: data.user.user_metadata?.name || data.user.email!,
            role: data.user.user_metadata?.role || 'user',
          }
        } catch (error) {
          console.error('Auth error:', error)
          throw error
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
    secret: process.env.NEXTAUTH_SECRET,
  },
  callbacks: {
    async signIn({ account }) {
      // Allow all OAuth sign-ins
      if (account?.provider !== "credentials") {
        return true
      }

      // For credentials, the authorize function handles validation
      return true
    },
    async jwt({ token, user, account, trigger, session }) {
      // Initial sign in
      if (user) {
        token.role = user.role || 'user'

        // For OAuth providers, create/update user in restaurant backend
        if (account?.provider !== "credentials") {
          try {
            // Parse name into first and last name
            const nameParts = user.name?.split(' ') || []
            const firstName = nameParts[0] || ''
            const lastName = nameParts.slice(1).join(' ') || ''

            // Call backend to create/update OAuth user
            const backendUrl = process.env.BACKEND_API_URL || 'http://localhost:8080/api/v1'
            const response = await fetch(`${backendUrl}/auth/oauth-user`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                oauth_user_id: user.id,
                email: user.email,
                name: user.name,
                first_name: firstName,
                last_name: lastName,
                avatar_url: user.image,
                provider: account?.provider || 'unknown',
                shop_slug: 'weerawat-poseeya', // Default shop for OAuth users
                branch_slug: 'the-green-terrace' // Default branch for OAuth users
              }),
            })

            if (response.ok) {
              const userData = await response.json()
              console.log('OAuth user created/updated successfully:', userData.email)
              token.role = userData.role || 'user'
            } else {
              console.error('Failed to create OAuth user:', response.status, response.statusText)
              token.role = 'user' // Default role
            }
          } catch (error) {
            console.error('OAuth user creation error:', error)
            token.role = 'user' // Default role
          }
        }
      }

      // Update session
      if (trigger === "update" && session) {
        token.role = session.role
      }

      return token
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub!
        session.user.role = token.role as string
      }
      return session
    },
    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`
      // Allows callback URLs on the same origin
      else if (new URL(url).origin === baseUrl) return url
      // Default redirect to /app after successful authentication
      return `${baseUrl}/app`
    },
  },
  pages: {
    signIn: '/login',
    signOut: '/logout',
    error: '/auth/error',
  },
  events: {
    async signIn({ user }) {
      console.log('User signed in:', user.email)
    },
    async signOut({ token }) {
      console.log('User signed out:', token?.email)
    },
  },
  debug: process.env.NODE_ENV === 'development',
}
