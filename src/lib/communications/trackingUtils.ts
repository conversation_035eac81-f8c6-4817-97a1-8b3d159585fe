/**
 * Utilities for tracking email and SMS events
 * 
 * This file contains utilities for generating tracking URLs and handling
 * tracking events for emails and SMS messages.
 */

/**
 * Generate a tracking URL for email opens
 * @param communicationId - The ID of the communication
 * @param merchantId - The ID of the merchant
 * @returns The tracking URL
 */
export function generateOpenTrackingUrl(communicationId: string, merchantId: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  return `${baseUrl}/api/merchants/${merchantId}/communications/tracking/open?id=${communicationId}`;
}

/**
 * Generate a tracking URL for email clicks
 * @param communicationId - The ID of the communication
 * @param merchantId - The ID of the merchant
 * @param url - The URL to redirect to
 * @returns The tracking URL
 */
export function generateClickTrackingUrl(communicationId: string, merchantId: string, url: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  const encodedUrl = encodeURIComponent(url);
  return `${baseUrl}/api/merchants/${merchantId}/communications/tracking/click?id=${communicationId}&url=${encodedUrl}`;
}

/**
 * Generate a tracking pixel for email opens
 * @param communicationId - The ID of the communication
 * @param merchantId - The ID of the merchant
 * @returns The tracking pixel HTML
 */
export function generateTrackingPixel(communicationId: string, merchantId: string): string {
  const trackingUrl = generateOpenTrackingUrl(communicationId, merchantId);
  return `<img src="${trackingUrl}" alt="" width="1" height="1" style="display:none;" />`;
}

/**
 * Add tracking to an HTML email
 * @param html - The HTML content of the email
 * @param communicationId - The ID of the communication
 * @param merchantId - The ID of the merchant
 * @returns The HTML with tracking added
 */
export function addTrackingToHtml(html: string, communicationId: string, merchantId: string): string {
  // Add tracking pixel for opens
  const trackingPixel = generateTrackingPixel(communicationId, merchantId);
  
  // Add tracking to links for clicks
  let trackedHtml = html.replace(/<a\s+(?:[^>]*?\s+)?href="([^"]*)"([^>]*)>/gi, (match, url, rest) => {
    // Skip tracking for anchor links, mailto links, and tel links
    if (url.startsWith('#') || url.startsWith('mailto:') || url.startsWith('tel:')) {
      return match;
    }
    
    const trackingUrl = generateClickTrackingUrl(communicationId, merchantId, url);
    return `<a href="${trackingUrl}"${rest}>`;
  });
  
  // Add tracking pixel before the closing body tag or at the end if no body tag
  if (trackedHtml.includes('</body>')) {
    trackedHtml = trackedHtml.replace('</body>', `${trackingPixel}</body>`);
  } else {
    trackedHtml += trackingPixel;
  }
  
  return trackedHtml;
}

/**
 * Generate an unsubscribe URL
 * @param communicationId - The ID of the communication
 * @param merchantId - The ID of the merchant
 * @param userId - The ID of the user
 * @returns The unsubscribe URL
 */
export function generateUnsubscribeUrl(communicationId: string, merchantId: string, userId?: string): string {
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
  let url = `${baseUrl}/api/merchants/${merchantId}/communications/unsubscribe?id=${communicationId}`;
  
  if (userId) {
    url += `&userId=${userId}`;
  }
  
  return url;
}

/**
 * Add an unsubscribe link to an HTML email
 * @param html - The HTML content of the email
 * @param communicationId - The ID of the communication
 * @param merchantId - The ID of the merchant
 * @param userId - The ID of the user
 * @returns The HTML with an unsubscribe link added
 */
export function addUnsubscribeLink(html: string, communicationId: string, merchantId: string, userId?: string): string {
  const unsubscribeUrl = generateUnsubscribeUrl(communicationId, merchantId, userId);
  const unsubscribeText = 'If you no longer wish to receive these emails, you can <a href="' + unsubscribeUrl + '">unsubscribe here</a>.';
  const unsubscribeHtml = `<div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #eee; font-size: 12px; color: #666;">${unsubscribeText}</div>`;
  
  // Add unsubscribe link before the closing body tag or at the end if no body tag
  if (html.includes('</body>')) {
    return html.replace('</body>', `${unsubscribeHtml}</body>`);
  } else {
    return html + unsubscribeHtml;
  }
}
