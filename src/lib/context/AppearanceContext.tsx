'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';

export interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system';
  accentColor: string;
  fontSize: string;
  reducedMotion: boolean;
  reducedTransparency: boolean;
  highContrast: boolean;
  compactMode: boolean;
  customFont: string;
}

interface AppearanceContextType {
  settings: AppearanceSettings;
  updateSettings: (newSettings: Partial<AppearanceSettings>) => void;
  applySettings: () => void;
}

const defaultSettings: AppearanceSettings = {
  theme: 'light',
  accentColor: 'solar-dusk',
  fontSize: 'medium',
  reducedMotion: false,
  reducedTransparency: false,
  highContrast: false,
  compactMode: false,
  customFont: 'oxanium',
};

// Color theme options with Solar Dusk theme variables
const colorThemes = {
  'solar-dusk': {
    light: {
      background: 'oklch(0.9885 0.0057 84.5659)',
      foreground: 'oklch(0.3660 0.0251 49.6085)',
      card: 'oklch(0.9686 0.0091 78.2818)',
      cardForeground: 'oklch(0.3660 0.0251 49.6085)',
      popover: 'oklch(0.9686 0.0091 78.2818)',
      popoverForeground: 'oklch(0.3660 0.0251 49.6085)',
      primary: 'oklch(0.5553 0.1455 48.9975)',
      primaryForeground: 'oklch(1.0000 0 0)',
      secondary: 'oklch(0.8276 0.0752 74.4400)',
      secondaryForeground: 'oklch(0.4444 0.0096 73.6390)',
      muted: 'oklch(0.9363 0.0218 83.2637)',
      mutedForeground: 'oklch(0.5534 0.0116 58.0708)',
      accent: 'oklch(0.9000 0.0500 74.9889)',
      accentForeground: 'oklch(0.4444 0.0096 73.6390)',
      destructive: 'oklch(0.4437 0.1613 26.8994)',
      destructiveForeground: 'oklch(1.0000 0 0)',
      border: 'oklch(0.8866 0.0404 89.6994)',
      input: 'oklch(0.8866 0.0404 89.6994)',
      ring: 'oklch(0.5553 0.1455 48.9975)',
    },
    dark: {
      background: 'oklch(0.2161 0.0061 56.0434)',
      foreground: 'oklch(0.9699 0.0013 106.4238)',
      card: 'oklch(0.2685 0.0063 34.2976)',
      cardForeground: 'oklch(0.9699 0.0013 106.4238)',
      popover: 'oklch(0.2685 0.0063 34.2976)',
      popoverForeground: 'oklch(0.9699 0.0013 106.4238)',
      primary: 'oklch(0.7049 0.1867 47.6044)',
      primaryForeground: 'oklch(1.0000 0 0)',
      secondary: 'oklch(0.4444 0.0096 73.6390)',
      secondaryForeground: 'oklch(0.9232 0.0026 48.7171)',
      muted: 'oklch(0.2685 0.0063 34.2976)',
      mutedForeground: 'oklch(0.7161 0.0091 56.2590)',
      accent: 'oklch(0.3598 0.0497 229.3202)',
      accentForeground: 'oklch(0.9232 0.0026 48.7171)',
      destructive: 'oklch(0.5771 0.2152 27.3250)',
      destructiveForeground: 'oklch(1.0000 0 0)',
      border: 'oklch(0.3741 0.0087 67.5582)',
      input: 'oklch(0.3741 0.0087 67.5582)',
      ring: 'oklch(0.7049 0.1867 47.6044)',
    }
  },
  earth: { primary: '#8a745c', secondary: '#e5ccb2', bg: '#ffffff', text: '#000000', border: '#e5e5e5' },
  ocean: { primary: '#3b82f6', secondary: '#93c5fd', bg: '#ffffff', text: '#000000', border: '#e5e5e5' },
  forest: { primary: '#059669', secondary: '#a7f3d0', bg: '#ffffff', text: '#000000', border: '#e5e5e5' },
  sunset: { primary: '#ea580c', secondary: '#fdba74', bg: '#ffffff', text: '#000000', border: '#e5e5e5' },
  berry: { primary: '#8b5cf6', secondary: '#c4b5fd', bg: '#ffffff', text: '#000000', border: '#e5e5e5' },
};

// Font size options
const fontSizeMap = {
  small: 0.875,
  medium: 1,
  large: 1.125,
  'x-large': 1.25,
};

// Font family options
const fontFamilyMap = {
  oxanium: 'Oxanium, sans-serif',
  'be-vietnam': 'Be Vietnam Pro, sans-serif',
  inter: 'Inter, sans-serif',
  roboto: 'Roboto, sans-serif',
  poppins: 'Poppins, sans-serif',
  montserrat: 'Montserrat, sans-serif',
};

const AppearanceContext = createContext<AppearanceContextType | undefined>(undefined);

export function AppearanceProvider({ children }: { children: React.ReactNode }) {
  const [settings, setSettings] = useState<AppearanceSettings>(defaultSettings);
  const [isClient, setIsClient] = useState(false);

  // Set client flag after hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load settings from localStorage on mount (client-side only)
  useEffect(() => {
    if (!isClient) return;

    try {
      const savedSettings = localStorage.getItem('appearance-settings');
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        setSettings({ ...defaultSettings, ...parsed });
      }
    } catch (error) {
      console.error('Failed to parse appearance settings:', error);
    }
  }, [isClient]);

  // Apply settings to the document (client-side only)
  const applySettings = useCallback(() => {
    if (!isClient || typeof document === 'undefined') return;

    const root = document.documentElement;
    const fontSizeScale = fontSizeMap[settings.fontSize as keyof typeof fontSizeMap] || fontSizeMap.medium;
    const fontFamily = fontFamilyMap[settings.customFont as keyof typeof fontFamilyMap] || fontFamilyMap.oxanium;

    // Apply theme
    if (settings.theme === 'dark') {
      root.classList.add('dark');
    } else {
      root.classList.remove('dark');
    }

    // Handle Solar Dusk theme with proper CSS variables
    if (settings.accentColor === 'solar-dusk') {
      const solarDuskTheme = colorThemes['solar-dusk'];
      const themeVariant = settings.theme === 'dark' ? solarDuskTheme.dark : solarDuskTheme.light;

      // Apply Solar Dusk CSS variables
      root.style.setProperty('--background', themeVariant.background);
      root.style.setProperty('--foreground', themeVariant.foreground);
      root.style.setProperty('--card', themeVariant.card);
      root.style.setProperty('--card-foreground', themeVariant.cardForeground);
      root.style.setProperty('--popover', themeVariant.popover);
      root.style.setProperty('--popover-foreground', themeVariant.popoverForeground);
      root.style.setProperty('--primary', themeVariant.primary);
      root.style.setProperty('--primary-foreground', themeVariant.primaryForeground);
      root.style.setProperty('--secondary', themeVariant.secondary);
      root.style.setProperty('--secondary-foreground', themeVariant.secondaryForeground);
      root.style.setProperty('--muted', themeVariant.muted);
      root.style.setProperty('--muted-foreground', themeVariant.mutedForeground);
      root.style.setProperty('--accent', themeVariant.accent);
      root.style.setProperty('--accent-foreground', themeVariant.accentForeground);
      root.style.setProperty('--destructive', themeVariant.destructive);
      root.style.setProperty('--destructive-foreground', themeVariant.destructiveForeground);
      root.style.setProperty('--border', themeVariant.border);
      root.style.setProperty('--input', themeVariant.input);
      root.style.setProperty('--ring', themeVariant.ring);

      // Set font family for Solar Dusk
      root.style.setProperty('--font-sans', 'Oxanium, sans-serif');
      root.style.setProperty('--font-serif', 'Merriweather, serif');
      root.style.setProperty('--font-mono', 'Fira Code, monospace');
    } else {
      // Handle legacy themes
      const colorTheme = colorThemes[settings.accentColor as keyof typeof colorThemes] || colorThemes.earth;

      // Apply legacy color theme using ShadCN CSS variables
      root.style.setProperty('--primary', colorTheme.primary);
      root.style.setProperty('--primary-foreground', colorTheme.bg);
      root.style.setProperty('--secondary', colorTheme.secondary);
      root.style.setProperty('--secondary-foreground', colorTheme.text);
      root.style.setProperty('--background', colorTheme.bg);
      root.style.setProperty('--foreground', colorTheme.text);
      root.style.setProperty('--card', colorTheme.bg);
      root.style.setProperty('--card-foreground', colorTheme.text);
      root.style.setProperty('--popover', colorTheme.bg);
      root.style.setProperty('--popover-foreground', colorTheme.text);
      root.style.setProperty('--muted', colorTheme.secondary);
      root.style.setProperty('--muted-foreground', colorTheme.primary);
      root.style.setProperty('--accent', colorTheme.secondary);
      root.style.setProperty('--accent-foreground', colorTheme.text);
      root.style.setProperty('--border', colorTheme.border);
      root.style.setProperty('--input', colorTheme.border);
      root.style.setProperty('--ring', colorTheme.primary);

      // Keep custom properties for backward compatibility
      root.style.setProperty('--color-primary', colorTheme.primary);
      root.style.setProperty('--color-secondary', colorTheme.secondary);
      root.style.setProperty('--color-background', colorTheme.bg);
      root.style.setProperty('--color-text', colorTheme.text);
      root.style.setProperty('--color-border', colorTheme.border);
    }

    // Apply font settings
    root.style.setProperty('--font-size-scale', fontSizeScale.toString());
    root.style.setProperty('--font-family', fontFamily);

    // Apply accessibility settings
    if (settings.reducedMotion) {
      root.style.setProperty('--animation-duration', '0s');
      root.style.setProperty('--transition-duration', '0s');
    } else {
      root.style.setProperty('--animation-duration', '0.3s');
      root.style.setProperty('--transition-duration', '0.3s');
    }

    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    if (settings.compactMode) {
      root.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
    }

    // Apply font size
    root.style.fontSize = `${fontSizeScale}rem`;
    root.style.fontFamily = fontFamily;
  }, [settings, isClient]);

  // Apply settings whenever they change (client-side only)
  useEffect(() => {
    if (isClient) {
      applySettings();
    }
  }, [settings, isClient]);

  const updateSettings = useCallback((newSettings: Partial<AppearanceSettings>) => {
    setSettings(prevSettings => {
      const updatedSettings = { ...prevSettings, ...newSettings };

      // Save to localStorage (client-side only)
      if (isClient && typeof localStorage !== 'undefined') {
        localStorage.setItem('appearance-settings', JSON.stringify(updatedSettings));
      }

      return updatedSettings;
    });
  }, [isClient]);

  return (
    <AppearanceContext.Provider value={{ settings, updateSettings, applySettings }}>
      {children}
    </AppearanceContext.Provider>
  );
}

export function useAppearance() {
  const context = useContext(AppearanceContext);
  if (context === undefined) {
    throw new Error('useAppearance must be used within an AppearanceProvider');
  }
  return context;
}
