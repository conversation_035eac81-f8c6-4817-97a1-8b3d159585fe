import { NextRequest, NextResponse } from 'next/server'
import { getToken } from 'next-auth/jwt'

export interface AuthUser {
  id: string
  email: string
  name?: string
  role: string
}

export interface AuthResult {
  authenticated: boolean
  user?: AuthUser
  error?: string
}

/**
 * Enhanced authentication middleware for NextAuth
 */
export async function checkAuth(request: NextRequest): Promise<AuthResult> {
  try {
    // Get token from NextAuth
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET,
    })

    if (!token) {
      return { authenticated: false, error: 'No authentication token' }
    }

    const user: AuthUser = {
      id: token.sub!,
      email: token.email!,
      name: token.name || undefined,
      role: (token.role as string) || 'user',
    }

    return { authenticated: true, user }
  } catch (error) {
    console.error('Auth check error:', error)
    return { authenticated: false, error: 'Authentication failed' }
  }
}

/**
 * Role-based authorization check
 */
export function checkRole(user: AuthUser, allowedRoles: string[]): boolean {
  return allowedRoles.includes(user.role)
}

/**
 * Middleware wrapper for API routes
 */
export function withAuth(
  handler: (request: NextRequest, context: { user: AuthUser }) => Promise<NextResponse>,
  options: {
    allowedRoles?: string[]
    requireAuth?: boolean
  } = {}
) {
  return async (request: NextRequest, params: any) => {
    const { allowedRoles, requireAuth = true } = options

    if (!requireAuth) {
      return handler(request, { user: null as any })
    }

    const authResult = await checkAuth(request)

    if (!authResult.authenticated || !authResult.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    if (allowedRoles && !checkRole(authResult.user, allowedRoles)) {
      return NextResponse.json(
        { error: 'Forbidden' },
        { status: 403 }
      )
    }

    return handler(request, { user: authResult.user })
  }
}

/**
 * Create auth headers for API requests
 */
export function createAuthHeaders(token?: string): HeadersInit {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  }

  if (token) {
    headers['Authorization'] = `Bearer ${token}`
  }

  return headers
}

/**
 * Extract auth token from request headers
 */
export function extractAuthToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization')

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  return authHeader.split(' ')[1]
}

/**
 * Validate merchant access
 */
export function validateMerchantAccess(
  user: AuthUser,
  merchantId: string
): boolean {
  // Admin can access all merchants
  if (user.role === 'admin') {
    return true
  }

  // For now, allow merchant owners to access their own merchant
  // In a real app, you'd check the user's merchant associations
  return user.role === 'merchant' || user.role === 'merchant_owner'
}

/**
 * Create unauthorized response
 */
export function createUnauthorizedResponse(message = 'Unauthorized'): NextResponse {
  return NextResponse.json(
    { error: message },
    { status: 401 }
  )
}

/**
 * Create forbidden response
 */
export function createForbiddenResponse(message = 'Forbidden'): NextResponse {
  return NextResponse.json(
    { error: message },
    { status: 403 }
  )
}
