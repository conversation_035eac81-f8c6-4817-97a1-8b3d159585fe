import { apiSlice } from '../apiSlice';
import { CommunicationCampaign } from '@/services/communicationService';
import { CampaignSegment, CampaignSegmentFilter } from '@/services/campaignService';

// Create the campaign API
export const campaignApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Campaigns
    getCampaigns: builder.query<CommunicationCampaign[], { 
      merchantId: string; 
      type?: 'email' | 'sms';
    }>({
      query: ({ merchantId, type }) => {
        let url = `/merchants/${merchantId}/campaigns`;
        const params = new URLSearchParams();
        if (type) params.append('type', type);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['Campaigns'],
    }),
    
    getCampaignById: builder.query<CommunicationCampaign, { merchantId: string; campaignId: string }>({
      query: ({ merchantId, campaignId }) => `/merchants/${merchantId}/campaigns/${campaignId}`,
      providesTags: ['Campaigns'],
    }),
    
    createCampaign: builder.mutation<CommunicationCampaign, Partial<CommunicationCampaign> & { merchantId: string }>({
      query: ({ merchantId, ...campaign }) => ({
        url: `/merchants/${merchantId}/campaigns`,
        method: 'POST',
        body: campaign,
      }),
      invalidatesTags: ['Campaigns'],
    }),
    
    updateCampaign: builder.mutation<CommunicationCampaign, Partial<CommunicationCampaign> & { merchantId: string; campaignId: string }>({
      query: ({ merchantId, campaignId, ...campaign }) => ({
        url: `/merchants/${merchantId}/campaigns/${campaignId}`,
        method: 'PUT',
        body: campaign,
      }),
      invalidatesTags: ['Campaigns'],
    }),
    
    deleteCampaign: builder.mutation<{ success: boolean }, { merchantId: string; campaignId: string }>({
      query: ({ merchantId, campaignId }) => ({
        url: `/merchants/${merchantId}/campaigns/${campaignId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Campaigns'],
    }),
    
    executeCampaign: builder.mutation<any, { merchantId: string; campaignId: string }>({
      query: ({ merchantId, campaignId }) => ({
        url: `/merchants/${merchantId}/campaigns/${campaignId}/execute`,
        method: 'POST',
      }),
      invalidatesTags: ['Campaigns'],
    }),
    
    // Campaign Segments
    getSegments: builder.query<CampaignSegment[], { merchantId: string }>({
      query: ({ merchantId }) => `/merchants/${merchantId}/campaign-segments`,
      providesTags: ['CampaignSegments'],
    }),
    
    getSegmentById: builder.query<CampaignSegment, { merchantId: string; segmentId: string }>({
      query: ({ merchantId, segmentId }) => `/merchants/${merchantId}/campaign-segments/${segmentId}`,
      providesTags: ['CampaignSegments'],
    }),
    
    createSegment: builder.mutation<CampaignSegment, {
      merchantId: string;
      name: string;
      description?: string;
      filters: CampaignSegmentFilter[];
    }>({
      query: ({ merchantId, ...segment }) => ({
        url: `/merchants/${merchantId}/campaign-segments`,
        method: 'POST',
        body: segment,
      }),
      invalidatesTags: ['CampaignSegments'],
    }),
    
    updateSegment: builder.mutation<CampaignSegment, {
      merchantId: string;
      segmentId: string;
      name?: string;
      description?: string;
      filters?: CampaignSegmentFilter[];
    }>({
      query: ({ merchantId, segmentId, ...segment }) => ({
        url: `/merchants/${merchantId}/campaign-segments/${segmentId}`,
        method: 'PUT',
        body: segment,
      }),
      invalidatesTags: ['CampaignSegments'],
    }),
    
    deleteSegment: builder.mutation<{ success: boolean }, { merchantId: string; segmentId: string }>({
      query: ({ merchantId, segmentId }) => ({
        url: `/merchants/${merchantId}/campaign-segments/${segmentId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['CampaignSegments'],
    }),
    
    getSegmentCustomers: builder.query<{
      customers: any[];
      total: number;
    }, {
      merchantId: string;
      segmentId: string;
      limit?: number;
      offset?: number;
    }>({
      query: ({ merchantId, segmentId, limit = 100, offset = 0 }) => {
        let url = `/merchants/${merchantId}/campaign-segments/${segmentId}/customers`;
        const params = new URLSearchParams();
        params.append('limit', limit.toString());
        params.append('offset', offset.toString());
        return `${url}?${params.toString()}`;
      },
      providesTags: ['CampaignSegments'],
    }),
    
    getSegmentEmails: builder.query<string[], { merchantId: string; segmentId: string }>({
      query: ({ merchantId, segmentId }) => `/merchants/${merchantId}/campaign-segments/${segmentId}/emails`,
      providesTags: ['CampaignSegments'],
    }),
    
    getSegmentPhones: builder.query<string[], { merchantId: string; segmentId: string }>({
      query: ({ merchantId, segmentId }) => `/merchants/${merchantId}/campaign-segments/${segmentId}/phones`,
      providesTags: ['CampaignSegments'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetCampaignsQuery,
  useGetCampaignByIdQuery,
  useCreateCampaignMutation,
  useUpdateCampaignMutation,
  useDeleteCampaignMutation,
  useExecuteCampaignMutation,
  useGetSegmentsQuery,
  useGetSegmentByIdQuery,
  useCreateSegmentMutation,
  useUpdateSegmentMutation,
  useDeleteSegmentMutation,
  useGetSegmentCustomersQuery,
  useGetSegmentEmailsQuery,
  useGetSegmentPhonesQuery,
} = campaignApi;
