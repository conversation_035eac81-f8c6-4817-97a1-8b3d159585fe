import { apiSlice } from '../apiSlice';
import { 
  CommunicationTemplate, 
  Communication,
  CommunicationCampaign
} from '@/services/communicationService';

// Create the communication API
export const communicationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Templates
    getTemplates: builder.query<CommunicationTemplate[], { 
      merchantId: string; 
      type?: 'email' | 'sms'; 
      category?: 'appointment_confirmation' | 'appointment_reminder' | 'appointment_cancellation' | 'marketing' | 'custom';
    }>({
      query: ({ merchantId, type, category }) => {
        let url = `/merchants/${merchantId}/communications/templates`;
        const params = new URLSearchParams();
        if (type) params.append('type', type);
        if (category) params.append('category', category);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['Communications'],
    }),
    
    getTemplateById: builder.query<CommunicationTemplate, { merchantId: string; templateId: string }>({
      query: ({ merchantId, templateId }) => `/merchants/${merchantId}/communications/templates/${templateId}`,
      providesTags: ['Communications'],
    }),
    
    createTemplate: builder.mutation<CommunicationTemplate, Partial<CommunicationTemplate> & { merchantId: string }>({
      query: ({ merchantId, ...template }) => ({
        url: `/merchants/${merchantId}/communications/templates`,
        method: 'POST',
        body: template,
      }),
      invalidatesTags: ['Communications'],
    }),
    
    updateTemplate: builder.mutation<CommunicationTemplate, Partial<CommunicationTemplate> & { merchantId: string; templateId: string }>({
      query: ({ merchantId, templateId, ...template }) => ({
        url: `/merchants/${merchantId}/communications/templates/${templateId}`,
        method: 'PUT',
        body: template,
      }),
      invalidatesTags: ['Communications'],
    }),
    
    deleteTemplate: builder.mutation<{ success: boolean }, { merchantId: string; templateId: string }>({
      query: ({ merchantId, templateId }) => ({
        url: `/merchants/${merchantId}/communications/templates/${templateId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Communications'],
    }),
    
    // Sending communications
    sendCommunication: builder.mutation<Communication, Partial<Communication> & { merchantId: string }>({
      query: ({ merchantId, ...communication }) => ({
        url: `/merchants/${merchantId}/communications/send`,
        method: 'POST',
        body: communication,
      }),
    }),
    
    sendWithTemplate: builder.mutation<Communication, {
      merchantId: string;
      templateId: string;
      userId?: string;
      recipient: string;
      variables: Record<string, string>;
      scheduledFor?: Date;
    }>({
      query: (data) => ({
        url: `/merchants/${data.merchantId}/communications/send`,
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetTemplatesQuery,
  useGetTemplateByIdQuery,
  useCreateTemplateMutation,
  useUpdateTemplateMutation,
  useDeleteTemplateMutation,
  useSendCommunicationMutation,
  useSendWithTemplateMutation,
} = communicationApi;
