import { apiSlice } from '../apiSlice';

// Convenience store types
export interface ConvenienceProduct {
  id: string;
  name: string;
  category: string;
  price: number;
  stock: number;
  status: 'In Stock' | 'Low Stock' | 'Out of Stock';
  image: string;
  barcode?: string;
  supplier?: string;
  merchantId: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ConvenienceOrder {
  id: string;
  items: number;
  date: string;
  time: string;
  status: 'Completed' | 'Processing' | 'Cancelled';
  total: string;
  customerName?: string;
  customerPhone?: string;
  merchantId: string;
}

export interface ConvenienceSupplier {
  id: string;
  name: string;
  category: string;
  lastDelivery: string;
  nextDelivery: string;
  contactEmail?: string;
  contactPhone?: string;
  merchantId: string;
}

export interface ConvenienceStats {
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  lowStockItems: number;
  topSellingProducts: ConvenienceProduct[];
  recentOrders: ConvenienceOrder[];
}

// Create the convenience store API
export const convenienceApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get convenience products
    getConvenienceProducts: builder.query<ConvenienceProduct[], { merchantId: string; category?: string }>({
      query: ({ merchantId, category }) => {
        const params = new URLSearchParams();
        params.append('merchantId', merchantId);
        if (category) params.append('category', category);
        return `/convenience-products?${params.toString()}`;
      },
      providesTags: ['ConvenienceProducts'],
    }),

    // Get convenience product by ID
    getConvenienceProduct: builder.query<ConvenienceProduct, { merchantId: string; productId: string }>({
      query: ({ merchantId, productId }) => 
        `/convenience-products/${productId}?merchantId=${merchantId}`,
      providesTags: (result, error, { productId }) => [{ type: 'ConvenienceProducts', id: productId }],
    }),

    // Get convenience orders
    getConvenienceOrders: builder.query<{
      data: ConvenienceOrder[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }, { 
      merchantId: string; 
      page?: number; 
      limit?: number; 
      status?: string;
      dateFrom?: string;
      dateTo?: string;
    }>({
      query: ({ merchantId, page = 1, limit = 20, status, dateFrom, dateTo }) => {
        const params = new URLSearchParams();
        params.append('merchantId', merchantId);
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        if (status) params.append('status', status);
        if (dateFrom) params.append('dateFrom', dateFrom);
        if (dateTo) params.append('dateTo', dateTo);
        return `/convenience-orders?${params.toString()}`;
      },
      providesTags: ['ConvenienceOrders'],
    }),

    // Get convenience suppliers
    getConvenienceSuppliers: builder.query<ConvenienceSupplier[], { merchantId: string }>({
      query: ({ merchantId }) => `/convenience-suppliers?merchantId=${merchantId}`,
      providesTags: ['ConvenienceSuppliers'],
    }),

    // Get convenience store statistics
    getConvenienceStats: builder.query<ConvenienceStats, { merchantId: string }>({
      query: ({ merchantId }) => `/convenience-stats?merchantId=${merchantId}`,
      providesTags: ['ConvenienceProducts', 'ConvenienceOrders'],
    }),

    // Create convenience product
    createConvenienceProduct: builder.mutation<ConvenienceProduct, Omit<ConvenienceProduct, 'id' | 'createdAt' | 'updatedAt'>>({
      query: (product) => ({
        url: '/convenience-products',
        method: 'POST',
        body: product,
      }),
      invalidatesTags: ['ConvenienceProducts'],
    }),

    // Update convenience product
    updateConvenienceProduct: builder.mutation<ConvenienceProduct, { 
      productId: string; 
      data: Partial<ConvenienceProduct> 
    }>({
      query: ({ productId, data }) => ({
        url: `/convenience-products/${productId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { productId }) => [
        { type: 'ConvenienceProducts', id: productId },
        'ConvenienceProducts'
      ],
    }),

    // Delete convenience product
    deleteConvenienceProduct: builder.mutation<void, { merchantId: string; productId: string }>({
      query: ({ merchantId, productId }) => ({
        url: `/convenience-products/${productId}?merchantId=${merchantId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { productId }) => [
        { type: 'ConvenienceProducts', id: productId },
        'ConvenienceProducts'
      ],
    }),

    // Create convenience order
    createConvenienceOrder: builder.mutation<ConvenienceOrder, Omit<ConvenienceOrder, 'id'>>({
      query: (order) => ({
        url: '/convenience-orders',
        method: 'POST',
        body: order,
      }),
      invalidatesTags: ['ConvenienceOrders'],
    }),

    // Update convenience order
    updateConvenienceOrder: builder.mutation<ConvenienceOrder, { 
      orderId: string; 
      data: Partial<ConvenienceOrder> 
    }>({
      query: ({ orderId, data }) => ({
        url: `/convenience-orders/${orderId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { orderId }) => [
        { type: 'ConvenienceOrders', id: orderId },
        'ConvenienceOrders'
      ],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetConvenienceProductsQuery,
  useGetConvenienceProductQuery,
  useGetConvenienceOrdersQuery,
  useGetConvenienceSuppliersQuery,
  useGetConvenienceStatsQuery,
  useCreateConvenienceProductMutation,
  useUpdateConvenienceProductMutation,
  useDeleteConvenienceProductMutation,
  useCreateConvenienceOrderMutation,
  useUpdateConvenienceOrderMutation,
} = convenienceApi;
