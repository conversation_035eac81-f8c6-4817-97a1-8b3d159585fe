import { apiSlice } from '../apiSlice';

// Create the customer communication API
export const customerCommunicationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Send a birthday greeting to a customer
    sendBirthdayGreeting: builder.mutation<any, { 
      merchantId: string; 
      customerId: string; 
      type?: 'email' | 'sms';
    }>({
      query: ({ merchantId, customerId, type = 'email' }) => ({
        url: `/merchants/${merchantId}/customers/${customerId}/communications/birthday`,
        method: 'POST',
        body: { type },
      }),
    }),
    
    // Send a loyalty offer to a customer
    sendLoyaltyOffer: builder.mutation<any, { 
      merchantId: string; 
      customerId: string; 
      type?: 'email' | 'sms';
      offerType?: 'discount' | 'free_service' | 'gift';
    }>({
      query: ({ merchantId, customerId, type = 'email', offerType = 'discount' }) => ({
        url: `/merchants/${merchantId}/customers/${customerId}/communications/loyalty-offer`,
        method: 'POST',
        body: { type, offerType },
      }),
    }),
    
    // Send a reengagement campaign to inactive customers
    sendReengagementCampaign: builder.mutation<any, { 
      merchantId: string; 
      type?: 'email' | 'sms';
      daysSinceLastAppointment?: number;
    }>({
      query: ({ merchantId, type = 'email', daysSinceLastAppointment = 90 }) => ({
        url: `/merchants/${merchantId}/communications/reengagement`,
        method: 'POST',
        body: { type, daysSinceLastAppointment },
      }),
    }),
  }),
});

// Export hooks for usage in components
export const {
  useSendBirthdayGreetingMutation,
  useSendLoyaltyOfferMutation,
  useSendReengagementCampaignMutation,
} = customerCommunicationApi;
