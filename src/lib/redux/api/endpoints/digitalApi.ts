import { apiSlice } from '../apiSlice';

// Digital-specific types
export interface DigitalSettings {
  automaticDelivery: boolean;
  downloadLimitPerPurchase: number;
  downloadExpiryDays: number;
  watermarkEnabled: boolean;
  licenseTypes: string[];
}

export interface DigitalProduct {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  salePrice: number | null;
  category: string;
  images: string[];
  fileUrl: string;
  fileSize: number;
  fileType: string;
  previewUrl: string | null;
  licenseType: string;
  version: string;
  requirements: string;
  features: string[];
  available: boolean;
}

export interface License {
  id: string;
  merchantId: string;
  productId: string;
  userId: string;
  orderId: string;
  licenseKey: string;
  activationDate: string;
  expiryDate: string | null;
  maxActivations: number;
  currentActivations: number;
  status: 'active' | 'expired' | 'revoked';
}

export interface Download {
  id: string;
  merchantId: string;
  productId: string;
  userId: string;
  licenseId: string;
  downloadDate: string;
  ipAddress: string;
  userAgent: string;
  successful: boolean;
}

// Create the digital API
export const digitalApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Digital Products
    getDigitalProducts: builder.query<DigitalProduct[], string>({
      query: (merchantId) => `/digital-products?merchantId=${merchantId}`,
      providesTags: ['Items'],
    }),

    createDigitalProduct: builder.mutation<DigitalProduct, Partial<DigitalProduct> & { merchantId: string }>({
      query: ({ merchantId, ...product }) => ({
        url: `/merchants/${merchantId}/digital-products`,
        method: 'POST',
        body: product,
      }),
      invalidatesTags: ['Items'],
    }),

    updateDigitalProduct: builder.mutation<DigitalProduct, { merchantId: string; productId: string; data: Partial<DigitalProduct> }>({
      query: ({ merchantId, productId, data }) => ({
        url: `/merchants/${merchantId}/digital-products/${productId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Items'],
    }),

    // Licenses
    getLicenses: builder.query<License[], { merchantId: string; productId?: string; userId?: string }>({
      query: ({ merchantId, productId, userId }) => {
        let url = `/merchants/${merchantId}/licenses`;
        const params = new URLSearchParams();
        if (productId) params.append('productId', productId);
        if (userId) params.append('userId', userId);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['Licenses'],
    }),

    createLicense: builder.mutation<License, Partial<License> & { merchantId: string }>({
      query: ({ merchantId, ...license }) => ({
        url: `/merchants/${merchantId}/licenses`,
        method: 'POST',
        body: license,
      }),
      invalidatesTags: ['Licenses'],
    }),

    updateLicense: builder.mutation<License, { merchantId: string; licenseId: string; data: Partial<License> }>({
      query: ({ merchantId, licenseId, data }) => ({
        url: `/merchants/${merchantId}/licenses/${licenseId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Licenses'],
    }),

    // Revoke license
    revokeLicense: builder.mutation<void, { merchantId: string; licenseId: string }>({
      query: ({ merchantId, licenseId }) => ({
        url: `/merchants/${merchantId}/licenses/${licenseId}/revoke`,
        method: 'POST',
      }),
      invalidatesTags: ['Licenses'],
    }),

    // Downloads
    getDownloads: builder.query<Download[], { merchantId: string; productId?: string; userId?: string }>({
      query: ({ merchantId, productId, userId }) => {
        let url = `/merchants/${merchantId}/downloads`;
        const params = new URLSearchParams();
        if (productId) params.append('productId', productId);
        if (userId) params.append('userId', userId);
        const queryString = params.toString();
        if (queryString) url += `?${queryString}`;
        return url;
      },
      providesTags: ['Downloads'],
    }),

    // Download statistics
    getDownloadStats: builder.query<{ total: number; today: number; thisWeek: number; thisMonth: number }, string>({
      query: (merchantId) => `/merchants/${merchantId}/download-stats`,
      providesTags: ['Downloads'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetDigitalProductsQuery,
  useCreateDigitalProductMutation,
  useUpdateDigitalProductMutation,
  useGetLicensesQuery,
  useCreateLicenseMutation,
  useUpdateLicenseMutation,
  useRevokeLicenseMutation,
  useGetDownloadsQuery,
  useGetDownloadStatsQuery,
} = digitalApi;
