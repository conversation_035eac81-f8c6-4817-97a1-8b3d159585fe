import { apiSlice } from '../apiSlice';

// Notification types
export interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  isRead: boolean;
  type: 'order' | 'reservation' | 'review' | 'staff' | 'system' | 'promotion';
  link?: string;
  data?: Record<string, any>;
  merchantId: string;
  userId?: string;
}

export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<string, number>;
}

export interface CreateNotificationRequest {
  title: string;
  message: string;
  type: 'order' | 'reservation' | 'review' | 'staff' | 'system' | 'promotion';
  link?: string;
  data?: Record<string, any>;
  userId?: string;
}

export interface UpdateNotificationRequest {
  isRead?: boolean;
}

// Create the notification API
export const notificationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get notifications
    getNotifications: builder.query<{
      data: Notification[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }, { 
      merchantId: string; 
      page?: number; 
      limit?: number; 
      type?: string;
      isRead?: boolean;
    }>({
      query: ({ merchantId, page = 1, limit = 20, type, isRead }) => {
        const params = new URLSearchParams();
        params.append('page', page.toString());
        params.append('limit', limit.toString());
        if (type) params.append('type', type);
        if (isRead !== undefined) params.append('isRead', isRead.toString());
        
        return `/merchants/${merchantId}/notifications?${params.toString()}`;
      },
      providesTags: ['Communications'],
    }),

    // Get notification by ID
    getNotification: builder.query<Notification, { merchantId: string; notificationId: string }>({
      query: ({ merchantId, notificationId }) => 
        `/merchants/${merchantId}/notifications/${notificationId}`,
      providesTags: (result, error, { notificationId }) => [{ type: 'Communications', id: notificationId }],
    }),

    // Get notification stats
    getNotificationStats: builder.query<NotificationStats, { merchantId: string }>({
      query: ({ merchantId }) => `/merchants/${merchantId}/notifications/stats`,
      providesTags: ['Communications'],
    }),

    // Create notification
    createNotification: builder.mutation<Notification, CreateNotificationRequest & { merchantId: string }>({
      query: ({ merchantId, ...notification }) => ({
        url: `/merchants/${merchantId}/notifications`,
        method: 'POST',
        body: notification,
      }),
      invalidatesTags: ['Communications'],
    }),

    // Update notification (mark as read/unread)
    updateNotification: builder.mutation<Notification, { 
      merchantId: string; 
      notificationId: string; 
      data: UpdateNotificationRequest 
    }>({
      query: ({ merchantId, notificationId, data }) => ({
        url: `/merchants/${merchantId}/notifications/${notificationId}`,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: (result, error, { notificationId }) => [
        { type: 'Communications', id: notificationId },
        'Communications'
      ],
    }),

    // Mark all notifications as read
    markAllNotificationsAsRead: builder.mutation<void, { merchantId: string }>({
      query: ({ merchantId }) => ({
        url: `/merchants/${merchantId}/notifications/mark-all-read`,
        method: 'POST',
      }),
      invalidatesTags: ['Communications'],
    }),

    // Delete notification
    deleteNotification: builder.mutation<void, { merchantId: string; notificationId: string }>({
      query: ({ merchantId, notificationId }) => ({
        url: `/merchants/${merchantId}/notifications/${notificationId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { notificationId }) => [
        { type: 'Communications', id: notificationId },
        'Communications'
      ],
    }),

    // Delete all notifications
    deleteAllNotifications: builder.mutation<void, { merchantId: string }>({
      query: ({ merchantId }) => ({
        url: `/merchants/${merchantId}/notifications`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Communications'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetNotificationsQuery,
  useGetNotificationQuery,
  useGetNotificationStatsQuery,
  useCreateNotificationMutation,
  useUpdateNotificationMutation,
  useMarkAllNotificationsAsReadMutation,
  useDeleteNotificationMutation,
  useDeleteAllNotificationsMutation,
} = notificationApi;
