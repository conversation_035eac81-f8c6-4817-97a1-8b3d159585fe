import { apiSlice } from '../../apiSlice';

// Types for AI Generation
export interface AIGenerationInputData {
  menu_image_url?: string;
  menu_text?: string;
  food_image_urls?: string[];
  cuisine_type?: string;
  price_range?: string;
  restaurant_name?: string;
}

export interface AIGeneratedMenuItem {
  name: string;
  description: string;
  price: number;
  category: string;
  image_url?: string;
  ingredients?: string[];
  allergens?: string[];
  is_vegetarian: boolean;
  is_vegan: boolean;
  is_gluten_free: boolean;
  is_spicy: boolean;
  spice_level: number;
  preparation_time?: number;
  tags?: string[];
}

export interface AIGeneratedMenuInfo {
  name: string;
  description: string;
  image_url?: string;
}

export interface AIGenerationOutputData {
  menu_items?: AIGeneratedMenuItem[];
  menu_info?: AIGeneratedMenuInfo;
}

export interface AIGenerationJob {
  id: string;
  branch_id: string;
  user_id: string;
  type: 'menu_image' | 'text' | 'food_images';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  input_data: AIGenerationInputData;
  output_data: AIGenerationOutputData;
  error_message?: string;
  started_at?: string;
  completed_at?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateAIGenerationJobRequest {
  type: 'menu_image' | 'text' | 'food_images';
  input_data: AIGenerationInputData;
}

export interface AIGenerationJobsResponse {
  jobs: AIGenerationJob[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface PublishMenuItemRequest {
  name: string;
  description: string;
  price: number;
  category_name?: string;
  image_url?: string;
  ingredients?: string[];
  allergens?: string[];
  is_vegetarian: boolean;
  is_vegan: boolean;
  is_gluten_free: boolean;
  is_spicy: boolean;
  spice_level: number;
  preparation_time?: number;
  tags?: string[];
}

export interface PublishMenuInfoRequest {
  name: string;
  description: string;
  image_url?: string;
}

export interface PublishAIGeneratedMenuRequest {
  job_id: string;
  menu_items: PublishMenuItemRequest[];
  menu_info?: PublishMenuInfoRequest;
}

export interface UploadAIFileResponse {
  file_url: string;
  message: string;
}

export interface AIGenerationJobFilters {
  type?: string;
  status?: string;
  page?: number;
  limit?: number;
}

export const aiGenerationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Create AI generation job
    createAIGenerationJob: builder.mutation<AIGenerationJob, {
      shopSlug: string;
      branchSlug: string;
      data: CreateAIGenerationJobRequest;
    }>({
      query: ({ shopSlug, branchSlug, data }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/ai-generation/jobs`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['AIGenerationJob'],
    }),

    // Get AI generation job by ID
    getAIGenerationJob: builder.query<AIGenerationJob, {
      shopSlug: string;
      branchSlug: string;
      jobId: string;
    }>({
      query: ({ shopSlug, branchSlug, jobId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/ai-generation/jobs/${jobId}`,
      }),
      providesTags: (result, error, { jobId }) => [
        { type: 'AIGenerationJob', id: jobId },
      ],
    }),

    // Get AI generation jobs for a branch
    getAIGenerationJobs: builder.query<AIGenerationJobsResponse, {
      shopSlug: string;
      branchSlug: string;
      filters?: AIGenerationJobFilters;
    }>({
      query: ({ shopSlug, branchSlug, filters = {} }) => {
        const params = new URLSearchParams();
        if (filters.type) params.append('type', filters.type);
        if (filters.status) params.append('status', filters.status);
        if (filters.page) params.append('page', filters.page.toString());
        if (filters.limit) params.append('limit', filters.limit.toString());

        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/ai-generation/jobs?${params.toString()}`,
        };
      },
      providesTags: ['AIGenerationJob'],
    }),

    // Publish AI generated menu
    publishAIGeneratedMenu: builder.mutation<{ message: string }, {
      shopSlug: string;
      branchSlug: string;
      data: PublishAIGeneratedMenuRequest;
    }>({
      query: ({ shopSlug, branchSlug, data }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/ai-generation/publish`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['MenuItem', 'MenuCategory', 'AIGenerationJob'],
    }),

    // Upload file for AI generation
    uploadAIFile: builder.mutation<UploadAIFileResponse, {
      shopSlug: string;
      branchSlug: string;
      file: File;
      type: 'menu_image' | 'food_image';
    }>({
      query: ({ shopSlug, branchSlug, file, type }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type);

        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/ai-generation/upload`,
          method: 'POST',
          body: formData,
        };
      },
    }),
  }),
});

export const {
  useCreateAIGenerationJobMutation,
  useGetAIGenerationJobQuery,
  useGetAIGenerationJobsQuery,
  usePublishAIGeneratedMenuMutation,
  useUploadAIFileMutation,
} = aiGenerationApi;
