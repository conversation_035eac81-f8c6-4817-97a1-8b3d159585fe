/**
 * RTK Query API endpoints for floors management
 * Handles all floor-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface Floor {
  id: string;
  branch_id: string;
  name: string;
  description: string;
  order: number;
  layout: {
    width: number;
    height: number;
    background_image?: string;
    grid_size: number;
    show_grid: boolean;
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
  areas?: Array<{
    id: string;
    name: string;
    description?: string;
    color: string;
    is_active: boolean;
  }>;
}

export interface CreateFloorRequest {
  name: string;
  description?: string;
  order: number;
  layout?: {
    width?: number;
    height?: number;
    background_image?: string;
    grid_size?: number;
    show_grid?: boolean;
  };
}

export interface UpdateFloorRequest {
  name?: string;
  description?: string;
  order?: number;
  layout?: {
    width?: number;
    height?: number;
    background_image?: string;
    grid_size?: number;
    show_grid?: boolean;
  };
}

export interface FloorFilters {
  name?: string;
  include_areas?: boolean;
  page?: number;
  limit?: number;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

export const floorsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all floors for a branch
    getFloors: builder.query<
      { data: Floor[]; total: number; page: number; limit: number; total_pages: number },
      { shopId: string; branchId: string; filters?: FloorFilters }
    >({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        // Add default pagination parameters to avoid backend validation errors
        params.append('page', (filters.page || 1).toString());
        params.append('limit', (filters.limit || 50).toString());

        // Add default sorting parameters to avoid backend validation errors
        params.append('sort_by', filters.sort_by || 'order');
        params.append('sort_order', filters.sort_order || 'asc');

        if (filters.name) params.append('name', filters.name);
        if (filters.include_areas) params.append('include_areas', 'true');

        return {
          url: `/shops/${shopId}/branches/${branchId}/floors${params.toString() ? `?${params.toString()}` : ''}`,
          method: 'GET',
        };
      },
      providesTags: ['Floor'],
    }),

    // Get a specific floor
    getFloor: builder.query<
      Floor,
      { shopId: string; branchId: string; floorId: string }
    >({
      query: ({ shopId, branchId, floorId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/floors/${floorId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { floorId }) => [
        { type: 'Floor', id: floorId },
      ],
    }),

    // Create a new floor
    createFloor: builder.mutation<
      Floor,
      { shopId: string; branchId: string; floorData: CreateFloorRequest }
    >({
      query: ({ shopId, branchId, floorData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/floors`,
        method: 'POST',
        body: floorData,
      }),
      invalidatesTags: ['Floor'],
    }),

    // Update an existing floor
    updateFloor: builder.mutation<
      Floor,
      { shopId: string; branchId: string; floorId: string; floorData: UpdateFloorRequest }
    >({
      query: ({ shopId, branchId, floorId, floorData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/floors/${floorId}`,
        method: 'PUT',
        body: floorData,
      }),
      invalidatesTags: (result, error, { floorId }) => [
        { type: 'Floor', id: floorId },
        'Floor',
      ],
    }),

    // Delete a floor
    deleteFloor: builder.mutation<
      void,
      { shopId: string; branchId: string; floorId: string }
    >({
      query: ({ shopId, branchId, floorId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/floors/${floorId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { floorId }) => [
        { type: 'Floor', id: floorId },
        'Floor',
      ],
    }),
  }),
});

export const {
  useGetFloorsQuery,
  useGetFloorQuery,
  useCreateFloorMutation,
  useUpdateFloorMutation,
  useDeleteFloorMutation,
} = floorsApi;
