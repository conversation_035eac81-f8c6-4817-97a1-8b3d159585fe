/**
 * Enhanced Inventory API with Backend-Driven Filtering, Sorting, and Pagination
 * Following the Purchase Orders pattern for consistent implementation
 */

import { apiSlice } from '../../apiSlice';

// Enhanced Inventory interfaces
export interface InventoryItem {
  id: string;
  name: string;
  category: string;
  sku: string;
  description?: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  totalValue: number;
  supplier: string;
  supplierContact?: string;
  lastRestocked?: string;
  expiryDate?: string;
  location?: string;
  notes?: string;
  status: 'in-stock' | 'low-stock' | 'out-of-stock' | 'expired';
  shopId: string;
  branchId: string;
  createdAt: string;
  updatedAt: string;
}

// Enhanced filtering interface
export interface InventoryFilters {
  // Pagination
  page: number;
  limit: number;
  
  // Sorting
  sort_by: 'name' | 'category' | 'currentStock' | 'status' | 'totalValue' | 'supplier' | 'expiryDate' | 'lastRestocked' | 'createdAt';
  sort_order: 'asc' | 'desc';
  
  // Filtering
  category?: string;
  status?: 'in-stock' | 'low-stock' | 'out-of-stock' | 'expired';
  supplier?: string;
  search?: string;
  
  // Stock level filtering
  lowStock?: boolean;
  outOfStock?: boolean;
  expiringSoon?: boolean;
  
  // Date filtering
  startDate?: string;
  endDate?: string;
  dateRange?: 'today' | 'week' | 'month' | 'quarter' | 'year';
}

// Response interfaces
export interface InventoryListResponse {
  data: InventoryItem[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  summary: {
    totalItems: number;
    lowStockItems: number;
    outOfStockItems: number;
    expiringSoonItems: number;
    totalValue: number;
    byCategory: Record<string, number>;
    byStatus: Record<string, number>;
    bySupplier: Record<string, number>;
  };
}

// Request interfaces
export interface CreateInventoryItemRequest {
  name: string;
  category: string;
  sku: string;
  description?: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  unit: string;
  costPerUnit: number;
  supplier: string;
  supplierContact?: string;
  expiryDate?: string;
  location?: string;
  notes?: string;
}

export interface UpdateInventoryItemRequest {
  name?: string;
  category?: string;
  sku?: string;
  description?: string;
  currentStock?: number;
  minStock?: number;
  maxStock?: number;
  unit?: string;
  costPerUnit?: number;
  supplier?: string;
  supplierContact?: string;
  expiryDate?: string;
  location?: string;
  notes?: string;
}

export interface StockAdjustmentRequest {
  itemId: string;
  adjustmentType: 'increase' | 'decrease' | 'set';
  quantity: number;
  reason: string;
  notes?: string;
}

export interface BulkStockUpdateRequest {
  updates: Array<{
    itemId: string;
    currentStock: number;
    reason: string;
  }>;
}

// Query parameters interface
export interface GetInventoryParams {
  shopId: string;
  branchId: string;
  filters: InventoryFilters;
}

// Enhanced Inventory API
export const inventoryApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get inventory items with backend-driven filtering, sorting, and pagination
    getInventoryItems: builder.query<InventoryListResponse, GetInventoryParams>({
      query: ({ shopId, branchId, filters }) => {
        const params = new URLSearchParams();
        
        // Pagination
        params.append('page', filters.page.toString());
        params.append('limit', filters.limit.toString());
        
        // Sorting
        params.append('sort_by', filters.sort_by);
        params.append('sort_order', filters.sort_order);
        
        // Filtering
        if (filters.category) params.append('category', filters.category);
        if (filters.status) params.append('status', filters.status);
        if (filters.supplier) params.append('supplier', filters.supplier);
        if (filters.search) params.append('search', filters.search);
        
        // Stock level filtering
        if (filters.lowStock) params.append('lowStock', 'true');
        if (filters.outOfStock) params.append('outOfStock', 'true');
        if (filters.expiringSoon) params.append('expiringSoon', 'true');
        
        // Date filtering
        if (filters.startDate) params.append('startDate', filters.startDate);
        if (filters.endDate) params.append('endDate', filters.endDate);
        if (filters.dateRange) params.append('dateRange', filters.dateRange);
        
        return {
          url: `inventory?shopId=${shopId}&branchId=${branchId}&${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        { type: 'Inventory', id: 'LIST' },
        ...(result?.data || []).map(({ id }) => ({ type: 'Inventory' as const, id })),
      ],
    }),

    // Get inventory item by ID
    getInventoryItemById: builder.query<InventoryItem, { shopId: string; branchId: string; itemId: string }>({
      query: ({ shopId, branchId, itemId }) => ({
        url: `inventory/${itemId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { itemId }) => [
        { type: 'Inventory', id: itemId },
      ],
    }),

    // Create inventory item
    createInventoryItem: builder.mutation<InventoryItem, { shopId: string; branchId: string; item: CreateInventoryItemRequest }>({
      query: ({ shopId, branchId, item }) => ({
        url: `inventory?shopId=${shopId}&branchId=${branchId}`,
        method: 'POST',
        body: item,
      }),
      invalidatesTags: [{ type: 'Inventory', id: 'LIST' }],
    }),

    // Update inventory item
    updateInventoryItem: builder.mutation<InventoryItem, { shopId: string; branchId: string; itemId: string; updates: UpdateInventoryItemRequest }>({
      query: ({ shopId, branchId, itemId, updates }) => ({
        url: `inventory/${itemId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'PATCH',
        body: updates,
      }),
      invalidatesTags: (result, error, { itemId }) => [
        { type: 'Inventory', id: itemId },
        { type: 'Inventory', id: 'LIST' },
      ],
    }),

    // Delete inventory item
    deleteInventoryItem: builder.mutation<{ success: boolean }, { shopId: string; branchId: string; itemId: string }>({
      query: ({ shopId, branchId, itemId }) => ({
        url: `inventory/${itemId}?shopId=${shopId}&branchId=${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { itemId }) => [
        { type: 'Inventory', id: itemId },
        { type: 'Inventory', id: 'LIST' },
      ],
    }),

    // Adjust stock levels
    adjustStock: builder.mutation<InventoryItem, { shopId: string; branchId: string; adjustment: StockAdjustmentRequest }>({
      query: ({ shopId, branchId, adjustment }) => ({
        url: `inventory/adjust-stock?shopId=${shopId}&branchId=${branchId}`,
        method: 'POST',
        body: adjustment,
      }),
      invalidatesTags: (result, error, { adjustment }) => [
        { type: 'Inventory', id: adjustment.itemId },
        { type: 'Inventory', id: 'LIST' },
      ],
    }),

    // Bulk update stock levels
    bulkUpdateStock: builder.mutation<{ updated: number }, { shopId: string; branchId: string; request: BulkStockUpdateRequest }>({
      query: ({ shopId, branchId, request }) => ({
        url: `inventory/bulk-update?shopId=${shopId}&branchId=${branchId}`,
        method: 'POST',
        body: request,
      }),
      invalidatesTags: [{ type: 'Inventory', id: 'LIST' }],
    }),

    // Get inventory statistics
    getInventoryStats: builder.query<InventoryListResponse['summary'], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `inventory/stats?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: [{ type: 'Inventory', id: 'STATS' }],
    }),

    // Get low stock alerts
    getLowStockAlerts: builder.query<InventoryItem[], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `inventory/low-stock?shopId=${shopId}&branchId=${branchId}`,
        method: 'GET',
      }),
      providesTags: [{ type: 'Inventory', id: 'LOW_STOCK' }],
    }),

    // Get expiring items
    getExpiringItems: builder.query<InventoryItem[], { shopId: string; branchId: string; days?: number }>({
      query: ({ shopId, branchId, days = 7 }) => ({
        url: `inventory/expiring?shopId=${shopId}&branchId=${branchId}&days=${days}`,
        method: 'GET',
      }),
      providesTags: [{ type: 'Inventory', id: 'EXPIRING' }],
    }),
  }),
});

// Export hooks
export const {
  useGetInventoryItemsQuery,
  useGetInventoryItemByIdQuery,
  useCreateInventoryItemMutation,
  useUpdateInventoryItemMutation,
  useDeleteInventoryItemMutation,
  useAdjustStockMutation,
  useBulkUpdateStockMutation,
  useGetInventoryStatsQuery,
  useGetLowStockAlertsQuery,
  useGetExpiringItemsQuery,
} = inventoryApi;

// Export types
export type {
  InventoryItem,
  InventoryFilters,
  InventoryListResponse,
  CreateInventoryItemRequest,
  UpdateInventoryItemRequest,
  StockAdjustmentRequest,
  BulkStockUpdateRequest,
  GetInventoryParams,
};
