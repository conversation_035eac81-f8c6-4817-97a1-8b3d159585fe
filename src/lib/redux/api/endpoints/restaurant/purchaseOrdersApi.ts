/**
 * Purchase Orders API endpoints with consistent filtering, sorting, and pagination
 */

import { apiSlice } from '../../apiSlice';

// Purchase Order Types
export type PurchaseOrderStatus = 'pending' | 'approved' | 'ordered' | 'received' | 'partial' | 'cancelled' | 'completed';

export interface PurchaseOrder {
  id: string;
  order_number: string;
  shop_id: string;
  branch_id: string;
  supplier_id: string;
  supplier_name: string;
  status: PurchaseOrderStatus;
  total_amount: number;
  currency: string;
  expected_delivery?: string;
  actual_delivery?: string;
  notes: string;
  created_by: string;
  created_at: string;
  updated_at: string;
  items?: PurchaseOrderItem[];
}

export interface PurchaseOrderItem {
  id: string;
  purchase_order_id: string;
  ingredient_id: string;
  ingredient_name: string;
  quantity: number;
  unit: string;
  unit_price: number;
  total_price: number;
  received_quantity: number;
  created_at: string;
  updated_at: string;
}

export interface PurchaseOrderFilters {
  // Filtering
  supplier_id?: string;
  status?: PurchaseOrderStatus;
  created_by?: string;
  date_from?: string;
  date_to?: string;
  expected_from?: string;
  expected_to?: string;
  min_amount?: number;
  max_amount?: number;
  search?: string;
  
  // Sorting
  sort_by?: 'order_number' | 'supplier_name' | 'status' | 'total_amount' | 'expected_delivery' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
  
  // Pagination
  page?: number;
  limit?: number;
}

export interface PurchaseOrdersResponse {
  data: PurchaseOrder[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface CreatePurchaseOrderRequest {
  supplier_id: string;
  expected_delivery?: string;
  notes?: string;
  items: CreatePurchaseOrderItemRequest[];
}

export interface CreatePurchaseOrderItemRequest {
  ingredient_id: string;
  quantity: number;
  unit: string;
  unit_price: number;
}

export interface UpdatePurchaseOrderRequest {
  status?: PurchaseOrderStatus;
  expected_delivery?: string;
  actual_delivery?: string;
  notes?: string;
}

// Supplier Types
export interface Supplier {
  id: string;
  name: string;
  contact_person?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country: string;
  payment_terms?: string;
  delivery_terms?: string;
  notes?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Create the purchase orders API
export const purchaseOrdersApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get purchase orders with filters, sorting, and pagination
    getPurchaseOrders: builder.query<PurchaseOrdersResponse, {
      shopId: string;
      branchId: string;
      filters?: PurchaseOrderFilters;
    }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();
        params.append('shopId', shopId);
        params.append('branchId', branchId);

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/purchase-orders?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'PurchaseOrders',
        ...(result?.data || []).map(({ id }) => ({ type: 'PurchaseOrders' as const, id })),
      ],
    }),

    // Get single purchase order
    getPurchaseOrder: builder.query<PurchaseOrder, {
      shopId: string;
      branchId: string;
      orderId: string;
    }>({
      query: ({ shopId, branchId, orderId }) => {
        const params = new URLSearchParams();
        params.append('shopId', shopId);
        params.append('branchId', branchId);
        params.append('orderId', orderId);

        return {
          url: `/purchase-orders/${orderId}?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result, error, { orderId }) => [
        { type: 'PurchaseOrders', id: orderId },
      ],
    }),

    // Create purchase order
    createPurchaseOrder: builder.mutation<PurchaseOrder, {
      shopId: string;
      branchId: string;
      data: CreatePurchaseOrderRequest;
    }>({
      query: ({ shopId, branchId, data }) => ({
        url: '/purchase-orders',
        method: 'POST',
        body: {
          shopId,
          branchId,
          ...data,
        },
      }),
      invalidatesTags: ['PurchaseOrders'],
    }),

    // Update purchase order
    updatePurchaseOrder: builder.mutation<PurchaseOrder, {
      shopId: string;
      branchId: string;
      orderId: string;
      data: UpdatePurchaseOrderRequest;
    }>({
      query: ({ shopId, branchId, orderId, data }) => {
        const params = new URLSearchParams();
        params.append('shopId', shopId);
        params.append('branchId', branchId);

        return {
          url: `/purchase-orders/${orderId}?${params.toString()}`,
          method: 'PUT',
          body: data,
        };
      },
      invalidatesTags: (result, error, { orderId }) => [
        'PurchaseOrders',
        { type: 'PurchaseOrders', id: orderId },
      ],
    }),

    // Delete purchase order
    deletePurchaseOrder: builder.mutation<void, {
      shopId: string;
      branchId: string;
      orderId: string;
    }>({
      query: ({ shopId, branchId, orderId }) => {
        const params = new URLSearchParams();
        params.append('shopId', shopId);
        params.append('branchId', branchId);

        return {
          url: `/purchase-orders/${orderId}?${params.toString()}`,
          method: 'DELETE',
        };
      },
      invalidatesTags: (result, error, { orderId }) => [
        'PurchaseOrders',
        { type: 'PurchaseOrders', id: orderId },
      ],
    }),

    // Get suppliers
    getSuppliers: builder.query<Supplier[], void>({
      query: () => ({
        url: '/suppliers',
        method: 'GET',
      }),
      providesTags: ['Suppliers'],
    }),
  }),
});

export const {
  useGetPurchaseOrdersQuery,
  useGetPurchaseOrderQuery,
  useCreatePurchaseOrderMutation,
  useUpdatePurchaseOrderMutation,
  useDeletePurchaseOrderMutation,
  useGetSuppliersQuery,
} = purchaseOrdersApi;
