/**
 * RTK Query API endpoints for reviews management
 * Handles all review-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface Review {
  id: string;
  created_at: string;
  updated_at: string;
  branch_id: string;
  customer_id?: string;
  order_id?: string;
  customer_name: string;
  customer_email?: string;
  customer_avatar?: string;
  rating: number;
  title?: string;
  comment: string;
  photos?: string[];
  source: 'google' | 'yelp' | 'facebook' | 'tripadvisor' | 'internal' | 'other';
  status: 'pending' | 'approved' | 'rejected' | 'flagged';
  is_verified: boolean;
  is_public: boolean;
  response?: {
    message: string;
    responded_by: string;
    responded_at: string;
  };
  tags?: string[];
  sentiment: 'positive' | 'neutral' | 'negative';
  branch?: any; // Optional branch data
}

export interface ReviewFilters {
  rating?: number;
  source?: string;
  status?: string;
  sentiment?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  hasResponse?: boolean;
  page?: number;
  limit?: number;
}

export interface ReviewResponse {
  reviewId: string;
  message: string;
}

export interface ReviewStats {
  totalReviews: number;
  averageRating: number;
  ratingDistribution: {
    1: number;
    2: number;
    3: number;
    4: number;
    5: number;
  };
  responseRate: number;
  sentimentBreakdown: {
    positive: number;
    neutral: number;
    negative: number;
  };
  sourceBreakdown: Record<string, number>;
  recentTrend: {
    period: string;
    averageRating: number;
    totalReviews: number;
  }[];
}

export interface ReviewsListResponse {
  data: Review[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
}

export const reviewsApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get reviews with filters and pagination
    getReviews: builder.query<ReviewsListResponse, {
      shopSlug: string;
      branchSlug: string;
      filters?: ReviewFilters;
    }>({
      query: ({ shopSlug, branchSlug, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews?${params.toString()}`,
          method: 'GET',
        };
      },
      providesTags: (result) => [
        'Reviews',
        ...(result?.data || []).map(({ id }) => ({ type: 'Reviews' as const, id })),
      ],
      // Add error handling to prevent infinite loops
      transformErrorResponse: (response: any) => {
        console.error('Reviews API Error:', response);
        return {
          status: response.status || 500,
          data: response.data || 'Failed to fetch reviews'
        };
      },
      // Disable automatic refetching on error to prevent infinite loops
      refetchOnMountOrArgChange: false,
      refetchOnFocus: false,
      refetchOnReconnect: false,
    }),

    // Get single review
    getReview: builder.query<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ shopId, branchId, reviewId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
      ],
    }),

    // Get review statistics
    getReviewStats: builder.query<ReviewStats, {
      shopSlug: string;
      branchSlug: string;
      period?: string;
    }>({
      query: ({ shopSlug, branchSlug, period = '30d' }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/stats?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['ReviewStats'],
    }),

    // Get recent reviews
    getRecentReviews: builder.query<Review[], {
      shopSlug: string;
      branchSlug: string;
      limit?: number;
    }>({
      query: ({ shopSlug, branchSlug, limit = 10 }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/recent?limit=${limit}`,
        method: 'GET',
      }),
      providesTags: ['Reviews'],
    }),

    // Get pending reviews
    getPendingReviews: builder.query<Review[], {
      shopSlug: string;
      branchSlug: string;
    }>({
      query: ({ shopSlug, branchSlug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/pending`,
        method: 'GET',
      }),
      providesTags: ['Reviews'],
    }),

    // Respond to review
    respondToReview: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
      message: string;
    }>({
      query: ({ shopId, branchId, reviewId, message }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/respond`,
        method: 'POST',
        body: { message },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Update review response
    updateReviewResponse: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
      message: string;
    }>({
      query: ({ shopId, branchId, reviewId, message }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/respond`,
        method: 'PUT',
        body: { message },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
      ],
    }),

    // Delete review response
    deleteReviewResponse: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ shopId, branchId, reviewId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/respond`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Update review status
    updateReviewStatus: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
      status: string;
    }>({
      query: ({ shopId, branchId, reviewId, status }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Flag review
    flagReview: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
      reason: string;
    }>({
      query: ({ shopId, branchId, reviewId, reason }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/flag`,
        method: 'POST',
        body: { reason },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
      ],
    }),

    // Hide review
    hideReview: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ shopId, branchId, reviewId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/hide`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Show review
    showReview: builder.mutation<Review, {
      shopId: string;
      branchId: string;
      reviewId: string;
    }>({
      query: ({ shopId, branchId, reviewId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/${reviewId}/show`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    // Sync external reviews
    syncExternalReviews: builder.mutation<{ synced: number; errors: string[] }, {
      shopId: string;
      branchId: string;
      sources?: string[];
    }>({
      query: ({ shopId, branchId, sources = [] }) => ({
        url: `/shops/${shopId}/branches/${branchId}/reviews/sync`,
        method: 'POST',
        body: { sources },
      }),
      invalidatesTags: ['Reviews', 'ReviewStats'],
    }),

    // Export reviews
    exportReviews: builder.mutation<{ downloadUrl: string }, {
      shopId: string;
      branchId: string;
      format: 'csv' | 'excel' | 'pdf';
      filters?: ReviewFilters;
    }>({
      query: ({ shopId, branchId, format, filters = {} }) => {
        const params = new URLSearchParams();
        params.append('format', format);

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/shops/${shopId}/branches/${branchId}/reviews/export?${params.toString()}`,
          method: 'POST',
        };
      },
    }),

    // Get review insights
    getReviewInsights: builder.query<{
      commonKeywords: Array<{ word: string; count: number; sentiment: string }>;
      improvementAreas: string[];
      strengths: string[];
      competitorComparison?: {
        averageRating: number;
        reviewCount: number;
        responseRate: number;
      };
    }, {
      shopSlug: string;
      branchSlug: string;
      period?: string;
    }>({
      query: ({ shopSlug, branchSlug, period = '30d' }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/insights?period=${period}`,
        method: 'GET',
      }),
      providesTags: ['ReviewStats'],
    }),

    // Slug-based mutations for review management
    respondToReviewBySlug: builder.mutation<Review, {
      shopSlug: string;
      branchSlug: string;
      reviewId: string;
      response: string;
      respondedBy?: string;
    }>({
      query: ({ shopSlug, branchSlug, reviewId, response, respondedBy }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/${reviewId}/respond`,
        method: 'POST',
        body: { response, responded_by: respondedBy },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    updateReviewStatusBySlug: builder.mutation<Review, {
      shopSlug: string;
      branchSlug: string;
      reviewId: string;
      status: string;
    }>({
      query: ({ shopSlug, branchSlug, reviewId, status }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/${reviewId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    flagReviewBySlug: builder.mutation<Review, {
      shopSlug: string;
      branchSlug: string;
      reviewId: string;
    }>({
      query: ({ shopSlug, branchSlug, reviewId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/${reviewId}/flag`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    approveReviewBySlug: builder.mutation<Review, {
      shopSlug: string;
      branchSlug: string;
      reviewId: string;
    }>({
      query: ({ shopSlug, branchSlug, reviewId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/${reviewId}/approve`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),

    rejectReviewBySlug: builder.mutation<Review, {
      shopSlug: string;
      branchSlug: string;
      reviewId: string;
    }>({
      query: ({ shopSlug, branchSlug, reviewId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/reviews/${reviewId}/reject`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { reviewId }) => [
        { type: 'Reviews', id: reviewId },
        'Reviews',
        'ReviewStats',
      ],
    }),
  }),
});

export const {
  useGetReviewsQuery,
  useGetReviewQuery,
  useGetReviewStatsQuery,
  useGetRecentReviewsQuery,
  useGetPendingReviewsQuery,
  useRespondToReviewMutation,
  useUpdateReviewResponseMutation,
  useDeleteReviewResponseMutation,
  useUpdateReviewStatusMutation,
  useFlagReviewMutation,
  useHideReviewMutation,
  useShowReviewMutation,
  useSyncExternalReviewsMutation,
  useExportReviewsMutation,
  useGetReviewInsightsQuery,
  // Slug-based hooks
  useRespondToReviewBySlugMutation,
  useUpdateReviewStatusBySlugMutation,
  useFlagReviewBySlugMutation,
  useApproveReviewBySlugMutation,
  useRejectReviewBySlugMutation,
} = reviewsApi;
