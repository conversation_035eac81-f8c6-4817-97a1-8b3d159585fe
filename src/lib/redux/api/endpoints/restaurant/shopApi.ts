/**
 * RTK Query API endpoints for shop management
 * Handles all shop-related API calls (replaces merchantApi)
 */

import { apiSlice } from '../../apiSlice';
import { Shop as ShopType, Branch, ShopWithBranches, BranchWithShop } from '@/lib/types/shop';

export interface ShopBranch {
  id: string;
  name: string;
  slug: string;
  address?: string;
  phone?: string;
  email?: string;
  status: string;
}

export interface Shop {
  id: string;
  name: string;
  slug: string;
  description?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  logo?: string;
  coverImage?: string;
  cuisine: string;
  priceRange: string;
  rating: number;
  reviewCount: number;
  isActive: boolean;
  openingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  location: {
    latitude: number;
    longitude: number;
  };
  features: string[];
  socialMedia: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
  branches?: ShopBranch[];
  createdAt: string;
  updatedAt: string;
}

export interface ShopsResponse {
  data: Shop[];
  total: number;
  page: number;
  limit: number;
}

export interface CreateShopRequest {
  name: string;
  slug: string;
  description?: string;
  address: string;
  phone: string;
  email: string;
  website?: string;
  cuisine: string;
  priceRange: string;
  openingHours: {
    [key: string]: {
      open: string;
      close: string;
      isOpen: boolean;
    };
  };
  location: {
    latitude: number;
    longitude: number;
  };
  features?: string[];
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
  };
}

export interface UpdateShopRequest extends Partial<CreateShopRequest> {
  id: string;
}

// Define types for branch endpoints (matching backend CreateBranchRequest)
interface CreateBranchRequest {
  name: string;
  slug: string;
  email?: string;
  phone?: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
  };
  business_hours: Record<string, string>;
  timezone: string;
}

// Define paginated response type
interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export const shopApi = apiSlice.injectEndpoints({
  overrideExisting: true, // Add this to suppress warnings during development
  endpoints: (builder) => ({
    // Get all shops
    getShops: builder.query<ShopsResponse, {
      page?: number;
      limit?: number;
      search?: string;
      cuisine?: string;
      priceRange?: string;
      isActive?: boolean;
      sort_by?: string;
      sort_order?: string;
    }>({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();

        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            searchParams.append(key, String(value));
          }
        });

        return {
          url: `/shops?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: ['Shop'],
    }),

    // Get shop by ID
    getShop: builder.query<Shop, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Shop', id }],
    }),

    // Get shop by ID (alternative name for compatibility)
    getShopById: builder.query<Shop, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'GET',
      }),
      providesTags: (result, error, id) => [{ type: 'Shop', id }],
    }),

    // Get shop by slug
    getShopBySlug: builder.query<ShopType, string>({
      query: (slug) => ({
        url: `/shops/slug/${slug}`,
        method: 'GET',
      }),
      providesTags: (result, error, slug) => [{ type: 'Shop', id: slug }],
    }),

    // Create new shop
    createShop: builder.mutation<Shop, CreateShopRequest>({
      query: (shopData) => ({
        url: '/shops',
        method: 'POST',
        body: shopData,
      }),
      invalidatesTags: ['Shop'],
    }),

    // Update shop
    updateShop: builder.mutation<Shop, UpdateShopRequest>({
      query: ({ id, ...shopData }) => ({
        url: `/shops/${id}`,
        method: 'PUT',
        body: shopData,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Shop', id },
        'Shop',
      ],
    }),

    // Delete shop
    deleteShop: builder.mutation<void, string>({
      query: (id) => ({
        url: `/shops/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Shop'],
    }),

    // Get shops by owner
    getShopsByOwner: builder.query<ShopsResponse, string>({
      query: (ownerId) => ({
        url: `/shops/owner/${ownerId}`,
        method: 'GET',
      }),
      providesTags: ['Shop'],
    }),

    // Get shops by type/cuisine
    getShopsByType: builder.query<ShopsResponse, string>({
      query: (type) => ({
        url: `/shops/type/${type}`,
        method: 'GET',
      }),
      providesTags: ['Shop'],
    }),

    // Branch-related endpoints
    // Get all branches for a shop
    getBranches: builder.query<Branch[], string>({
      query: (shopId) => `/shops/${shopId}/branches`,
      providesTags: ['Branches'],
    }),

    // Get branch by ID
    getBranchById: builder.query<Branch, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/shops/${shopId}/branches/${branchId}`,
      providesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),

    // Get branch by slug
    getBranchBySlug: builder.query<Branch, { shopSlug: string; branchSlug: string }>({
      query: ({ shopSlug, branchSlug }) => `/shops/slug/${shopSlug}/branches/slug/${branchSlug}`,
      providesTags: (result, error, { branchSlug }) => [{ type: 'Branches', id: result?.id }],
    }),

    // Create branch
    createBranch: builder.mutation<Branch, CreateBranchRequest & { shopId: string }>({
      query: ({ shopId, ...branch }) => ({
        url: `/shops/${shopId}/branches`,
        method: 'POST',
        body: branch,
      }),
      invalidatesTags: ['Branches', 'Shop'],
    }),

    // Update branch
    updateBranch: builder.mutation<Branch, { shopId: string; branchId: string; data: Partial<Branch> }>({
      query: ({ shopId, branchId, data }) => ({
        url: `/shops/${shopId}/branches/${branchId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),

    // Delete branch
    deleteBranch: builder.mutation<void, { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => ({
        url: `/shops/${shopId}/branches/${branchId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Branches'],
    }),

    // Get shops with branches
    getShopsWithBranches: builder.query<ShopWithBranches[], void>({
      query: () => '/shops/with-branches',
      providesTags: ['Shop', 'Branches'],
    }),

    // Get branch with shop
    getBranchWithShop: builder.query<BranchWithShop, { shopSlug: string; branchSlug: string }>({
      query: ({ shopSlug, branchSlug }) => `/shops/slug/${shopSlug}/branches/slug/${branchSlug}`,
      providesTags: (result, error, { branchSlug }) => [
        { type: 'Branches', id: result?.id },
        { type: 'Shop', id: result?.shop?.id },
      ],
    }),

    // Get branch settings
    getBranchSettings: builder.query<Branch['settings'], { shopId: string; branchId: string }>({
      query: ({ shopId, branchId }) => `/shops/${shopId}/branches/${branchId}/settings`,
      providesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),

    // Update branch settings
    updateBranchSettings: builder.mutation<Branch, {
      shopId: string;
      branchId: string;
      settings: Branch['settings']
    }>({
      query: ({ shopId, branchId, settings }) => ({
        url: `/shops/${shopId}/branches/${branchId}/settings`,
        method: 'PUT',
        body: settings,
      }),
      invalidatesTags: (result, error, { branchId }) => [{ type: 'Branches', id: branchId }],
    }),
  }),
});

export const {
  useGetShopsQuery,
  useGetShopQuery,
  useGetShopByIdQuery,
  useGetShopBySlugQuery,
  useCreateShopMutation,
  useUpdateShopMutation,
  useDeleteShopMutation,
  useGetShopsByOwnerQuery,
  useGetShopsByTypeQuery,
  // Branch-related hooks
  useGetBranchesQuery,
  useGetBranchByIdQuery,
  useGetBranchBySlugQuery,
  useCreateBranchMutation,
  useUpdateBranchMutation,
  useDeleteBranchMutation,
  useGetShopsWithBranchesQuery,
  useGetBranchWithShopQuery,
  useGetBranchSettingsQuery,
  useUpdateBranchSettingsMutation,
} = shopApi;

// Legacy exports for backward compatibility
export const merchantApi = shopApi;
export const {
  useGetShopsQuery: useGetMerchantsQuery,
  useGetShopQuery: useGetMerchantQuery,
  useGetShopBySlugQuery: useGetMerchantBySlugQuery,
  useCreateShopMutation: useCreateMerchantMutation,
  useUpdateShopMutation: useUpdateMerchantMutation,
  useDeleteShopMutation: useDeleteMerchantMutation,
} = shopApi;

// Legacy type exports
export type Merchant = Shop;
export type MerchantFilters = { page?: number; limit?: number; search?: string; cuisine?: string; priceRange?: string; isActive?: boolean; sort_by?: string; sort_order?: string; };
export type CreateMerchantRequest = CreateShopRequest;
export type UpdateMerchantRequest = UpdateShopRequest;
export type MerchantsResponse = ShopsResponse;
