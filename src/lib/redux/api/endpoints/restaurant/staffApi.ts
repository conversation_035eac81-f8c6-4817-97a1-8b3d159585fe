/**
 * RTK Query API endpoints for staff management
 * Handles all staff-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface StaffMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  slug: string;
  avatar?: string;
  position: string;
  department: string;
  roleId: string;
  roleName: string;
  permissions: string[];
  status: 'active' | 'inactive' | 'suspended';
  employeeId: string;
  hireDate: string;
  salary?: number;
  hourlyRate?: number;
  address?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  };
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
  schedule?: WorkSchedule[];
  performance?: PerformanceMetrics;
  createdAt: string;
  updatedAt: string;
}

export interface WorkSchedule {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string;
  endTime: string;
  isWorkingDay: boolean;
}

export interface PerformanceMetrics {
  ordersServed: number;
  customerRating: number;
  punctualityScore: number;
  salesGenerated: number;
  lastReviewDate?: string;
}

export interface StaffRole {
  id: string;
  name: string;
  description: string;
  permissions: string[]; // Array of permission names/IDs
  isActive: boolean;
  createdAt: string;
}

export interface Permission {
  id?: string;
  name: string;
  description: string;
  category?: string;
  isActive?: boolean;
}

export interface StaffFilters {
  // Filtering
  status?: string;
  roleId?: string;
  department?: string;
  position?: string;
  search?: string;

  // Sorting
  sort_by?: 'first_name' | 'last_name' | 'position' | 'department' | 'status' | 'hire_date' | 'created_at';
  sort_order?: 'asc' | 'desc';

  // Pagination
  page?: number;
  limit?: number;
}

export interface CreateStaffRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  department: string;
  roleId: string;
  employeeId: string;
  hireDate: string;
  salary?: number;
  hourlyRate?: number;
  address?: StaffMember['address'];
  emergencyContact?: StaffMember['emergencyContact'];
  schedule?: WorkSchedule[];
}

// Backend request format (snake_case) - matches types.CreateUserRequest
export interface BackendCreateStaffRequest {
  first_name: string;
  last_name: string;
  email: string;
  password: string;
  phone?: string;
  position?: string;
  department?: string;
  role_id: string; // UUID string
  employee_id?: string;
  hire_date?: string; // ISO date string
  salary?: number;
  hourly_rate?: number;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
  };
  emergency_contact: {
    name: string;
    relationship: string;
    phone: string;
    email: string;
  };
}

export interface UpdateStaffRequest {
  id: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  position?: string;
  department?: string;
  roleId?: string;
  status?: string;
  salary?: number;
  hourlyRate?: number;
  address?: StaffMember['address'];
  emergencyContact?: StaffMember['emergencyContact'];
  schedule?: WorkSchedule[];
}

export interface StaffResponse {
  data: StaffMember[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export const staffApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get staff members with filters, sorting, and pagination
    getStaff: builder.query<StaffResponse, {
      shopSlug: string;
      branchSlug: string;
      filters?: StaffFilters;
    }>({
      query: ({ shopSlug, branchSlug, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        const queryString = params.toString();
        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff${queryString ? `?${queryString}` : ''}`,
          method: 'GET',
        };
      },
      transformResponse: (response: any) => {
        // Transform backend snake_case to frontend camelCase
        if (response?.data) {
          const transformedData = response.data.map((staff: any) => ({
            id: staff.id,
            firstName: staff.first_name,
            lastName: staff.last_name,
            email: staff.email,
            phone: staff.phone,
            slug: staff.slug,
            avatar: staff.avatar_url,
            position: staff.position,
            department: staff.department,
            roleId: staff.role_id,
            roleName: staff.role?.name || '',
            permissions: staff.role?.permissions || [],
            status: staff.status,
            employeeId: staff.employee_id,
            hireDate: staff.hire_date,
            salary: staff.salary,
            hourlyRate: staff.hourly_rate,
            address: staff.address ? {
              street: staff.address.street,
              city: staff.address.city,
              state: staff.address.state,
              zipCode: staff.address.zip_code,
              country: staff.address.country,
            } : undefined,
            emergencyContact: staff.emergency_contact ? {
              name: staff.emergency_contact.name,
              relationship: staff.emergency_contact.relationship,
              phone: staff.emergency_contact.phone,
            } : undefined,
            schedule: staff.schedule,
            performance: staff.performance,
            createdAt: staff.created_at,
            updatedAt: staff.updated_at,
          }));

          return {
            ...response,
            data: transformedData,
          };
        }
        return response;
      },
      providesTags: (result) => [
        'Staff',
        ...(result?.data || []).map(({ id }) => ({ type: 'Staff' as const, id })),
      ],
    }),

    // Get single staff member
    getStaffMember: builder.query<StaffMember, {
      shopSlug: string;
      branchSlug: string;
      staffId: string;
    }>({
      query: ({ shopSlug, branchSlug, staffId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/${staffId}`,
        method: 'GET',
      }),
      transformResponse: (staff: any) => {
        // Transform backend snake_case to frontend camelCase
        if (staff) {
          return {
            id: staff.id,
            firstName: staff.first_name,
            lastName: staff.last_name,
            email: staff.email,
            phone: staff.phone,
            slug: staff.slug,
            avatar: staff.avatar_url,
            position: staff.position,
            department: staff.department,
            roleId: staff.role_id,
            roleName: staff.role?.name || '',
            permissions: staff.role?.permissions || [],
            status: staff.status,
            employeeId: staff.employee_id,
            hireDate: staff.hire_date,
            salary: staff.salary,
            hourlyRate: staff.hourly_rate,
            address: staff.address ? {
              street: staff.address.street,
              city: staff.address.city,
              state: staff.address.state,
              zipCode: staff.address.zip_code,
              country: staff.address.country,
            } : undefined,
            emergencyContact: staff.emergency_contact ? {
              name: staff.emergency_contact.name,
              relationship: staff.emergency_contact.relationship,
              phone: staff.emergency_contact.phone,
            } : undefined,
            schedule: staff.schedule,
            performance: staff.performance,
            createdAt: staff.created_at,
            updatedAt: staff.updated_at,
          };
        }
        return staff;
      },
      providesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
      ],
    }),

    // Get staff member by slug
    getStaffMemberBySlug: builder.query<StaffMember, {
      shopSlug: string;
      branchSlug: string;
      slug: string;
    }>({
      query: ({ shopSlug, branchSlug, slug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/slug/${slug}`,
        method: 'GET',
      }),
      transformResponse: (staff: any) => {
        // Transform backend snake_case to frontend camelCase
        if (staff) {
          return {
            id: staff.id,
            firstName: staff.first_name,
            lastName: staff.last_name,
            email: staff.email,
            phone: staff.phone,
            slug: staff.slug,
            avatar: staff.avatar_url,
            position: staff.position,
            department: staff.department,
            roleId: staff.role_id,
            roleName: staff.role?.name || '',
            permissions: staff.role?.permissions || [],
            status: staff.status,
            employeeId: staff.employee_id,
            hireDate: staff.hire_date,
            salary: staff.salary,
            hourlyRate: staff.hourly_rate,
            address: staff.address ? {
              street: staff.address.street,
              city: staff.address.city,
              state: staff.address.state,
              zipCode: staff.address.zip_code,
              country: staff.address.country,
            } : undefined,
            emergencyContact: staff.emergency_contact ? {
              name: staff.emergency_contact.name,
              relationship: staff.emergency_contact.relationship,
              phone: staff.emergency_contact.phone,
            } : undefined,
            schedule: staff.schedule,
            performance: staff.performance,
            createdAt: staff.created_at,
            updatedAt: staff.updated_at,
          };
        }
        return staff;
      },
      providesTags: (result, error, { slug }) => [
        { type: 'Staff', id: slug },
      ],
    }),

    // Get staff roles
    getStaffRoles: builder.query<StaffRole[], {
      shopSlug: string;
      branchSlug: string;
    }>({
      query: ({ shopSlug, branchSlug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/roles`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        // Backend returns { data: StaffRole[], total, page, limit, total_pages }
        // We need to extract just the data array
        return response?.data || [];
      },
      providesTags: ['StaffRoles'],
    }),

    // Get permissions
    getPermissions: builder.query<Permission[], {
      shopSlug: string;
      branchSlug: string;
    }>({
      query: ({ shopSlug, branchSlug }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/permissions`,
        method: 'GET',
      }),
      transformResponse: (response: any) => {
        // Backend returns { data: Permission[], total, page, limit, total_pages }
        // We need to extract just the data array
        return response?.data || [];
      },
      providesTags: ['Permissions'],
    }),

    // Create new staff member
    createStaffMember: builder.mutation<StaffMember, {
      shopSlug: string;
      branchSlug: string;
      staffData: CreateStaffRequest;
    }>({
      query: ({ shopSlug, branchSlug, staffData }) => {
        // Transform frontend camelCase to backend snake_case
        const backendData: BackendCreateStaffRequest = {
          first_name: staffData.firstName,
          last_name: staffData.lastName,
          email: staffData.email,
          password: 'temp123456', // Temporary password - should be changed on first login
          phone: staffData.phone || '',
          position: staffData.position || '',
          department: staffData.department || '',
          role_id: staffData.roleId,
          employee_id: staffData.employeeId || '',
          hire_date: staffData.hireDate ? new Date(staffData.hireDate).toISOString() : new Date().toISOString(), // ISO datetime format
          salary: staffData.salary,
          hourly_rate: staffData.hourlyRate,
          // Provide required nested fields with proper structure
          address: staffData.address ? {
            street: staffData.address.street || '',
            city: staffData.address.city || '',
            state: staffData.address.state || '',
            zip_code: staffData.address.zipCode || '',
            country: staffData.address.country || '',
          } : {
            street: '',
            city: '',
            state: '',
            zip_code: '',
            country: '',
          },
          emergency_contact: staffData.emergencyContact ? {
            name: staffData.emergencyContact.name || '',
            relationship: staffData.emergencyContact.relationship || '',
            phone: staffData.emergencyContact.phone || '',
            email: staffData.emergencyContact.phone || '', // Use phone as fallback for email
          } : {
            name: '',
            relationship: '',
            phone: '',
            email: '',
          },
        };



        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff`,
          method: 'POST',
          body: backendData,
        };
      },
      invalidatesTags: ['Staff'],
    }),

    // Update staff member
    updateStaffMember: builder.mutation<StaffMember, {
      shopSlug: string;
      branchSlug: string;
      staffData: UpdateStaffRequest;
    }>({
      query: ({ shopSlug, branchSlug, staffData }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/${staffData.id}`,
        method: 'PUT',
        body: staffData,
      }),
      invalidatesTags: (result, error, { staffData }) => [
        { type: 'Staff', id: staffData.id },
        'Staff',
      ],
    }),

    // Delete staff member
    deleteStaffMember: builder.mutation<void, {
      shopSlug: string;
      branchSlug: string;
      staffId: string;
    }>({
      query: ({ shopSlug, branchSlug, staffId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/${staffId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
        'Staff',
      ],
    }),

    // Update staff status
    updateStaffStatus: builder.mutation<StaffMember, {
      shopSlug: string;
      branchSlug: string;
      staffId: string;
      status: string;
    }>({
      query: ({ shopSlug, branchSlug, staffId, status }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/${staffId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
        'Staff',
      ],
    }),

    // Update staff schedule
    updateStaffSchedule: builder.mutation<StaffMember, {
      shopSlug: string;
      branchSlug: string;
      staffId: string;
      schedule: WorkSchedule[];
    }>({
      query: ({ shopSlug, branchSlug, staffId, schedule }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/${staffId}/schedule`,
        method: 'PUT',
        body: { schedule },
      }),
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
      ],
    }),

    // Upload staff avatar
    uploadStaffAvatar: builder.mutation<{ avatarUrl: string }, {
      shopSlug: string;
      branchSlug: string;
      staffId: string;
      file: File;
    }>({
      query: ({ shopSlug, branchSlug, staffId, file }) => {
        const formData = new FormData();
        formData.append('avatar', file);

        return {
          url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/${staffId}/avatar`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { staffId }) => [
        { type: 'Staff', id: staffId },
      ],
    }),

    // Create staff role
    createStaffRole: builder.mutation<StaffRole, {
      shopSlug: string;
      branchSlug: string;
      roleData: {
        name: string;
        description: string;
        permissions: string[];
      };
    }>({
      query: ({ shopSlug, branchSlug, roleData }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/roles`,
        method: 'POST',
        body: roleData,
      }),
      invalidatesTags: ['StaffRoles'],
    }),

    // Update staff role
    updateStaffRole: builder.mutation<StaffRole, {
      shopSlug: string;
      branchSlug: string;
      roleId: string;
      roleData: {
        name?: string;
        description?: string;
        permissions?: string[];
        isActive?: boolean;
      };
    }>({
      query: ({ shopSlug, branchSlug, roleId, roleData }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/roles/${roleId}`,
        method: 'PUT',
        body: roleData,
      }),
      invalidatesTags: ['StaffRoles', 'Staff'],
    }),

    // Delete staff role
    deleteStaffRole: builder.mutation<void, {
      shopSlug: string;
      branchSlug: string;
      roleId: string;
    }>({
      query: ({ shopSlug, branchSlug, roleId }) => ({
        url: `/shops/slug/${shopSlug}/branches/slug/${branchSlug}/staff/roles/${roleId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['StaffRoles', 'Staff'],
    }),
  }),
});

export const {
  useGetStaffQuery,
  useGetStaffMemberQuery,
  useGetStaffMemberBySlugQuery,
  useGetStaffRolesQuery,
  useGetPermissionsQuery,
  useCreateStaffMemberMutation,
  useUpdateStaffMemberMutation,
  useDeleteStaffMemberMutation,
  useUpdateStaffStatusMutation,
  useUpdateStaffScheduleMutation,
  useUploadStaffAvatarMutation,
  useCreateStaffRoleMutation,
  useUpdateStaffRoleMutation,
  useDeleteStaffRoleMutation,
} = staffApi;
