/**
 * RTK Query API endpoints for tables management
 * Handles all table-related API calls
 */

import { apiSlice } from '../../apiSlice';

export interface Table {
  id: string;
  branch_id: string;
  area_id?: string;
  area?: {
    id: string;
    name: string;
    description?: string;
    color: string;
    is_active: boolean;
    table_count: number;
  };
  name: string;
  number: string;
  capacity: number;
  min_capacity: number;
  max_capacity: number;
  status: 'available' | 'occupied' | 'reserved' | 'cleaning' | 'maintenance';
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
    angle: number;
  };
  shape: 'square' | 'round' | 'rectangle';
  image_url?: string;
  qr_code?: string;
  qr_code_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface TableArea {
  id: string;
  floor_id?: string;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  tableCount: number;
}

export interface TableLayout {
  id: string;
  name: string;
  areas: TableArea[];
  tables: Table[];
  floorPlan?: {
    width: number;
    height: number;
    backgroundImage?: string;
  };
}

export interface TableFilters {
  area?: string;
  status?: string;
  capacity?: number;
  search?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

export interface CreateTableRequest {
  name: string;
  number: string;
  capacity: number;
  min_capacity?: number;
  max_capacity?: number;
  area_id?: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
    angle?: number;
  };
  shape: 'square' | 'round' | 'rectangle';
  image_url?: string;
  is_active?: boolean;
  qr_code?: string;
}

export interface UpdateTableRequest {
  id: string;
  name?: string;
  number?: string;
  capacity?: number;
  area_id?: string;
  position?: {
    x: number;
    y: number;
    width: number;
    height: number;
    angle?: number;
  };
  shape?: 'square' | 'round' | 'rectangle';
  image_url?: string;
  is_active?: boolean;
}

export interface QRCodeData {
  tableId: string;
  qrCodeUrl: string;
  menuUrl: string;
}

export const tablesApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tables (now uses shops API)
    getTables: builder.query<Table[], {
      shopId: string;
      branchId: string;
      filters?: TableFilters;
    }>({
      query: ({ shopId, branchId, filters = {} }) => {
        const params = new URLSearchParams();

        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            params.append(key, String(value));
          }
        });

        return {
          url: `/shops/${shopId}/branches/${branchId}/tables?${params.toString()}`,
          method: 'GET',
        };
      },
      transformResponse: (response: PaginatedResponse<Table>) => response.data,
      providesTags: (result) => [
        'Tables',
        ...(result || []).map(({ id }) => ({ type: 'Tables' as const, id })),
      ],
    }),

    // Get single table (now uses shops API)
    getTable: builder.query<Table, {
      shopId: string;
      branchId: string;
      tableId: string;
    }>({
      query: ({ shopId, branchId, tableId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/${tableId}`,
        method: 'GET',
      }),
      providesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
      ],
    }),

    // Get table layout (now uses shops API)
    getTableLayout: builder.query<TableLayout, {
      shopId: string;
      branchId: string;
    }>({
      query: ({ shopId, branchId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/layout`,
        method: 'GET',
      }),
      providesTags: ['TableLayout'],
    }),

    // Get table areas (now uses shops API)
    getTableAreas: builder.query<TableArea[], {
      shopId: string;
      branchId: string;
    }>({
      query: ({ shopId, branchId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/areas`,
        method: 'GET',
      }),
      transformResponse: (response: PaginatedResponse<TableArea>) => response.data,
      providesTags: ['TableAreas'],
    }),

    // Create new table (now uses shops API)
    createTable: builder.mutation<Table, {
      shopId: string;
      branchId: string;
      tableData: CreateTableRequest;
    }>({
      query: ({ shopId, branchId, tableData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables`,
        method: 'POST',
        body: tableData,
      }),
      invalidatesTags: ['Tables', 'TableLayout'],
    }),

    // Update table (now uses shops API)
    updateTable: builder.mutation<Table, {
      shopId: string;
      branchId: string;
      tableData: UpdateTableRequest;
    }>({
      query: ({ shopId, branchId, tableData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/${tableData.id}`,
        method: 'PUT',
        body: tableData,
      }),
      invalidatesTags: (result, error, { tableData }) => [
        { type: 'Tables', id: tableData.id },
        'Tables',
        'TableLayout',
      ],
    }),

    // Delete table (now uses shops API)
    deleteTable: builder.mutation<void, {
      shopId: string;
      branchId: string;
      tableId: string;
    }>({
      query: ({ shopId, branchId, tableId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/${tableId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
        'Tables',
        'TableLayout',
      ],
    }),

    // Update table status (now uses shops API)
    updateTableStatus: builder.mutation<Table, {
      shopId: string;
      branchId: string;
      tableId: string;
      status: string;
    }>({
      query: ({ shopId, branchId, tableId, status }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/${tableId}/status`,
        method: 'PATCH',
        body: { status },
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
        'Tables',
        'TableLayout',
      ],
    }),

    // Generate QR code for table (now uses shops API)
    generateTableQRCode: builder.mutation<QRCodeData, {
      shopId: string;
      branchId: string;
      tableId: string;
    }>({
      query: ({ shopId, branchId, tableId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/${tableId}/qr-code`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
      ],
    }),

    // Get all QR codes (now uses shops API)
    getTableQRCodes: builder.query<QRCodeData[], {
      shopId: string;
      branchId: string;
    }>({
      query: ({ shopId, branchId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/qr-codes`,
        method: 'GET',
      }),
      providesTags: ['Tables'],
    }),

    // Create table area (now uses shops API)
    createTableArea: builder.mutation<TableArea, {
      shopId: string;
      branchId: string;
      areaData: {
        name: string;
        description?: string;
        color: string;
      };
    }>({
      query: ({ shopId, branchId, areaData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/areas`,
        method: 'POST',
        body: areaData,
      }),
      invalidatesTags: ['TableAreas', 'TableLayout'],
    }),

    // Update table area (now uses shops API)
    updateTableArea: builder.mutation<TableArea, {
      shopId: string;
      branchId: string;
      areaId: string;
      areaData: {
        name?: string;
        description?: string;
        color?: string;
        isActive?: boolean;
      };
    }>({
      query: ({ shopId, branchId, areaId, areaData }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/areas/${areaId}`,
        method: 'PUT',
        body: areaData,
      }),
      invalidatesTags: ['TableAreas', 'TableLayout'],
    }),

    // Delete table area (now uses shops API)
    deleteTableArea: builder.mutation<void, {
      shopId: string;
      branchId: string;
      areaId: string;
    }>({
      query: ({ shopId, branchId, areaId }) => ({
        url: `/shops/${shopId}/branches/${branchId}/tables/areas/${areaId}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['TableAreas', 'TableLayout', 'Tables'],
    }),

    // Update table layout
    updateTableLayout: builder.mutation<TableLayout, {
      merchantId: string;
      branchId: string;
      layoutData: {
        name?: string;
        floorPlan?: {
          width: number;
          height: number;
          backgroundImage?: string;
        };
        tables?: Partial<Table>[];
      };
    }>({
      query: ({ merchantId, branchId, layoutData }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/layout`,
        method: 'PUT',
        body: layoutData,
      }),
      invalidatesTags: ['TableLayout', 'Tables'],
    }),

    // Get table statistics
    getTableStats: builder.query<{
      totalTables: number;
      availableTables: number;
      occupiedTables: number;
      reservedTables: number;
      occupancyRate: number;
      averageTurnover: number;
    }, {
      merchantId: string;
      branchId: string;
    }>({
      query: ({ merchantId, branchId }) => ({
        url: `/merchants/${merchantId}/branches/${branchId}/tables/stats`,
        method: 'GET',
      }),
      providesTags: ['Tables'],
    }),

    // Upload table image
    uploadTableImage: builder.mutation<{ imageUrl: string }, {
      shopId: string;
      branchId: string;
      tableId: string;
      file: File;
    }>({
      query: ({ shopId, branchId, tableId, file }) => {
        const formData = new FormData();
        formData.append('image', file);

        return {
          url: `/shops/${shopId}/branches/${branchId}/tables/${tableId}/image`,
          method: 'POST',
          body: formData,
        };
      },
      invalidatesTags: (result, error, { tableId }) => [
        { type: 'Tables', id: tableId },
        'Tables',
        'TableLayout',
      ],
    }),
  }),
});

export const {
  useGetTablesQuery,
  useGetTableQuery,
  useGetTableLayoutQuery,
  useGetTableAreasQuery,
  useCreateTableMutation,
  useUpdateTableMutation,
  useDeleteTableMutation,
  useUpdateTableStatusMutation,
  useGenerateTableQRCodeMutation,
  useGetTableQRCodesQuery,
  useCreateTableAreaMutation,
  useUpdateTableAreaMutation,
  useDeleteTableAreaMutation,
  useUpdateTableLayoutMutation,
  useGetTableStatsQuery,
  useUploadTableImageMutation,
} = tablesApi;
