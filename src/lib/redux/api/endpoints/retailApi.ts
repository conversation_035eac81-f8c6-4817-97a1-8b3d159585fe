import { apiSlice } from '../apiSlice';

// Retail-specific types
export interface RetailSettings {
  inventoryManagement: boolean;
  allowBackorders: boolean;
  shippingMethods: string[];
  returnPolicy: string;
  openingHours: Record<string, { open: string; close: string }>;
}

export interface Product {
  id: string;
  merchantId: string;
  name: string;
  description: string;
  price: number;
  salePrice: number | null;
  category: string;
  images: string[];
  sku: string;
  barcode: string;
  weight: number;
  dimensions: { length: number; width: number; height: number };
  inventoryCount: number;
  inventoryThreshold: number;
  available: boolean;
  attributes: Record<string, any>;
  variants: ProductVariant[];
}

export interface ProductVariant {
  id: string;
  productId: string;
  name: string;
  sku: string;
  price: number;
  inventoryCount: number;
  attributes: Record<string, any>;
}

// Create the retail API
export const retailApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Products
    getProducts: builder.query<Product[], string>({
      query: (merchantId) => `/merchants/${merchantId}/products`,
      providesTags: ['Items'],
    }),

    createProduct: builder.mutation<Product, Partial<Product> & { merchantId: string }>({
      query: ({ merchantId, ...product }) => ({
        url: `/merchants/${merchantId}/products`,
        method: 'POST',
        body: product,
      }),
      invalidatesTags: ['Items'],
    }),

    updateProduct: builder.mutation<Product, { merchantId: string; productId: string; data: Partial<Product> }>({
      query: ({ merchantId, productId, data }) => ({
        url: `/merchants/${merchantId}/products/${productId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Items'],
    }),

    // Inventory
    updateInventory: builder.mutation<void, { merchantId: string; productId: string; count: number }>({
      query: ({ merchantId, productId, count }) => ({
        url: `/merchants/${merchantId}/products/${productId}/inventory`,
        method: 'PUT',
        body: { count },
      }),
      invalidatesTags: ['Items'],
    }),

    // Product Variants
    getProductVariants: builder.query<ProductVariant[], { merchantId: string; productId: string }>({
      query: ({ merchantId, productId }) => `/merchants/${merchantId}/products/${productId}/variants`,
      providesTags: ['Items'],
    }),
  }),
});

// Export hooks for usage in components
export const {
  useGetProductsQuery,
  useCreateProductMutation,
  useUpdateProductMutation,
  useUpdateInventoryMutation,
  useGetProductVariantsQuery,
} = retailApi;
