/**
 * Navigation type options for the application
 */
export enum NavigationType {
  HEADER = 'header',
  SIDEBAR_LEFT = 'sidebar-left',
  SIDEBAR_RIGHT = 'sidebar-right',
  DRAWER = 'drawer',
  BOTTOM = 'bottom',
}

/**
 * Navigation item interface
 */
export interface NavigationItem {
  name: string;
  href: string;
  icon?: React.ReactNode;
}

/**
 * Navigation settings interface
 */
export interface NavigationSettings {
  type: NavigationType;
  isCollapsed?: boolean; // For sidebar navigation
  showLabels?: boolean; // For bottom navigation
  showIcons?: boolean; // For all navigation types
  autoHide?: boolean; // For header and bottom navigation
}

/**
 * Default navigation settings
 */
export const defaultNavigationSettings: NavigationSettings = {
  type: NavigationType.HEADER,
  isCollapsed: false,
  showLabels: true,
  showIcons: true,
  autoHide: false,
};
