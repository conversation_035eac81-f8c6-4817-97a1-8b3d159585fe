/**
 * Shop and Branch types for the application
 */

// Address type to match backend
export interface Address {
  street: string;
  city: string;
  state: string;
  zip_code: string;
  country: string;
}

// Social media links type
export interface SocialMediaLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
  youtube?: string;
  tiktok?: string;
}

// Business hours type
export interface BusinessHours {
  [day: string]: string; // e.g., "monday": "09:00-22:00"
}

// Shop settings type
export interface ShopSettings {
  currency?: string;
  tax_rate?: number;
  service_charge_rate?: number;
  default_tip_percentage?: number;
  payment_methods?: string[];
  features?: Record<string, boolean>;
  notifications?: Record<string, boolean>;
  theme?: Record<string, string>;
  online_ordering?: boolean;
  table_reservations?: boolean;
  delivery_enabled?: boolean;
  pickup_enabled?: boolean;
}

// Shop type to match backend API response
export interface Shop {
  id: string;
  slug: string;
  name: string;
  description: string;
  shop_type: string;
  owner_id: string;
  email?: string;
  phone?: string;
  website?: string;
  logo?: string;
  cover_image?: string;
  address: Address;
  cuisine_type?: string;
  price_range?: string;
  rating: number;
  review_count: number;
  social_media: SocialMediaLinks;
  business_hours: BusinessHours;
  status: 'active' | 'inactive' | 'suspended';
  is_verified: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  branches?: Branch[];
}

// Branch settings type
export interface BranchSettings {
  currency?: string;
  tax_rate?: number;
  service_charge_rate?: number;
  default_tip_percentage?: number;
  payment_methods?: string[];
  features?: Record<string, boolean>;
  notifications?: Record<string, boolean>;
  theme?: Record<string, string>;
  online_ordering?: boolean;
  table_reservations?: boolean;
  delivery_enabled?: boolean;
  pickup_enabled?: boolean;
  max_table_capacity?: number;
  reservation_window?: number;
  min_reservation_time?: number;
}

// Branch type to match backend API response
export interface Branch {
  id: string;
  slug: string;
  shop_id: string;
  name: string;
  email?: string;
  phone?: string;
  address: Address;
  business_hours: BusinessHours;
  timezone: string;
  status: 'active' | 'inactive';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Shop with branches type
export interface ShopWithBranches extends Shop {
  branches: Branch[];
}

// Branch with shop type
export interface BranchWithShop extends Branch {
  shop: Shop;
}
