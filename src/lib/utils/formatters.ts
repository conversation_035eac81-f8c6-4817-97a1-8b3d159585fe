import { format, formatDistanceToNow, isToday, isYesterday, parseISO } from 'date-fns';

/**
 * Date formatting utilities
 */
export const dateFormatters = {
  /**
   * Format date for display (e.g., "Jan 15, 2024")
   */
  display: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, 'MMM dd, yyyy');
  },

  /**
   * Format date with time (e.g., "Jan 15, 2024 at 2:30 PM")
   */
  displayWithTime: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, 'MMM dd, yyyy \'at\' h:mm a');
  },

  /**
   * Format date for forms (e.g., "2024-01-15")
   */
  form: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, 'yyyy-MM-dd');
  },

  /**
   * Format time only (e.g., "2:30 PM")
   */
  time: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, 'h:mm a');
  },

  /**
   * Format relative time (e.g., "2 hours ago", "in 3 days")
   */
  relative: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return formatDistanceToNow(dateObj, { addSuffix: true });
  },

  /**
   * Smart date formatting (Today, Yesterday, or date)
   */
  smart: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    
    if (isToday(dateObj)) {
      return 'Today';
    }
    
    if (isYesterday(dateObj)) {
      return 'Yesterday';
    }
    
    return format(dateObj, 'MMM dd, yyyy');
  },

  /**
   * Format for table headers and sorting
   */
  table: (date: Date | string) => {
    const dateObj = typeof date === 'string' ? parseISO(date) : date;
    return format(dateObj, 'MM/dd/yyyy');
  }
};

/**
 * Number formatting utilities
 */
export const numberFormatters = {
  /**
   * Format currency (e.g., "$1,234.56")
   */
  currency: (amount: number, currency = 'USD', locale = 'en-US') => {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  },

  /**
   * Format percentage (e.g., "12.34%")
   */
  percentage: (value: number, decimals = 2, locale = 'en-US') => {
    return new Intl.NumberFormat(locale, {
      style: 'percent',
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value / 100);
  },

  /**
   * Format number with commas (e.g., "1,234,567")
   */
  withCommas: (value: number, locale = 'en-US') => {
    return new Intl.NumberFormat(locale).format(value);
  },

  /**
   * Format decimal number (e.g., "1,234.56")
   */
  decimal: (value: number, decimals = 2, locale = 'en-US') => {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(value);
  },

  /**
   * Format compact number (e.g., "1.2K", "1.5M")
   */
  compact: (value: number, locale = 'en-US') => {
    return new Intl.NumberFormat(locale, {
      notation: 'compact',
      compactDisplay: 'short',
    }).format(value);
  },

  /**
   * Format file size (e.g., "1.5 MB", "2.3 GB")
   */
  fileSize: (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    const size = bytes / Math.pow(1024, i);
    
    return `${size.toFixed(2)} ${sizes[i]}`;
  },

  /**
   * Format phone number (basic US format)
   */
  phone: (phoneNumber: string) => {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    
    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }
    
    return phoneNumber;
  }
};

/**
 * Text formatting utilities
 */
export const textFormatters = {
  /**
   * Truncate text with ellipsis
   */
  truncate: (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + '...';
  },

  /**
   * Capitalize first letter
   */
  capitalize: (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  },

  /**
   * Convert to title case
   */
  titleCase: (text: string) => {
    return text.replace(/\w\S*/g, (txt) => 
      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
    );
  },

  /**
   * Convert to slug format
   */
  slug: (text: string) => {
    return text
      .toLowerCase()
      .replace(/[^\w ]+/g, '')
      .replace(/ +/g, '-');
  }
};
