/**
 * Pagination utility functions
 */

export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startIndex: number;
  endIndex: number;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  total: number;
}

/**
 * Calculate pagination information
 */
export function calculatePagination({
  page = 1,
  limit = 10,
  total
}: PaginationParams): PaginationInfo {
  const currentPage = Math.max(1, page);
  const itemsPerPage = Math.max(1, limit);
  const totalPages = Math.ceil(total / itemsPerPage);
  const validCurrentPage = Math.min(currentPage, totalPages || 1);
  
  const startIndex = (validCurrentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage - 1, total - 1);
  
  return {
    currentPage: validCurrentPage,
    totalPages,
    totalItems: total,
    itemsPerPage,
    hasNextPage: validCurrentPage < totalPages,
    hasPreviousPage: validCurrentPage > 1,
    startIndex,
    endIndex
  };
}

/**
 * Generate page numbers for pagination component
 */
export function generatePageNumbers(
  currentPage: number,
  totalPages: number,
  maxVisible: number = 5
): (number | 'ellipsis')[] {
  if (totalPages <= maxVisible) {
    return Array.from({ length: totalPages }, (_, i) => i + 1);
  }

  const pages: (number | 'ellipsis')[] = [];
  const halfVisible = Math.floor(maxVisible / 2);

  // Always show first page
  pages.push(1);

  let startPage = Math.max(2, currentPage - halfVisible);
  let endPage = Math.min(totalPages - 1, currentPage + halfVisible);

  // Adjust if we're near the beginning
  if (currentPage <= halfVisible + 1) {
    endPage = Math.min(totalPages - 1, maxVisible - 1);
  }

  // Adjust if we're near the end
  if (currentPage >= totalPages - halfVisible) {
    startPage = Math.max(2, totalPages - maxVisible + 2);
  }

  // Add ellipsis after first page if needed
  if (startPage > 2) {
    pages.push('ellipsis');
  }

  // Add middle pages
  for (let i = startPage; i <= endPage; i++) {
    pages.push(i);
  }

  // Add ellipsis before last page if needed
  if (endPage < totalPages - 1) {
    pages.push('ellipsis');
  }

  // Always show last page (if more than 1 page)
  if (totalPages > 1) {
    pages.push(totalPages);
  }

  return pages;
}

/**
 * Get pagination slice of array
 */
export function paginateArray<T>(
  array: T[],
  page: number = 1,
  limit: number = 10
): { data: T[]; pagination: PaginationInfo } {
  const pagination = calculatePagination({
    page,
    limit,
    total: array.length
  });

  const data = array.slice(pagination.startIndex, pagination.endIndex + 1);

  return { data, pagination };
}

/**
 * Create pagination URL parameters
 */
export function createPaginationParams(
  currentParams: URLSearchParams,
  page: number,
  limit?: number
): URLSearchParams {
  const newParams = new URLSearchParams(currentParams);
  
  newParams.set('page', page.toString());
  
  if (limit) {
    newParams.set('limit', limit.toString());
  }
  
  return newParams;
}

/**
 * Parse pagination from URL search params
 */
export function parsePaginationFromUrl(
  searchParams: URLSearchParams,
  defaultLimit: number = 10
): { page: number; limit: number } {
  const page = Math.max(1, parseInt(searchParams.get('page') || '1', 10));
  const limit = Math.max(1, parseInt(searchParams.get('limit') || defaultLimit.toString(), 10));
  
  return { page, limit };
}

/**
 * Format pagination info text (e.g., "Showing 1-10 of 100 results")
 */
export function formatPaginationInfo(pagination: PaginationInfo): string {
  const { startIndex, endIndex, totalItems } = pagination;
  
  if (totalItems === 0) {
    return 'No results found';
  }
  
  if (totalItems === 1) {
    return '1 result';
  }
  
  const start = startIndex + 1;
  const end = endIndex + 1;
  
  return `Showing ${start}-${end} of ${totalItems} results`;
}
