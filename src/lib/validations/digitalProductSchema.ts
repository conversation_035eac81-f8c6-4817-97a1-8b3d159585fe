import { z } from 'zod';

// Digital product schema
export const digitalProductSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be a positive number'),
  salePrice: z.number().min(0, 'Sale price must be a positive number').nullable().optional(),
  category: z.string().min(1, 'Category is required'),
  images: z.array(z.string().url('Invalid image URL')).optional(),
  fileUrl: z.string().url('Invalid file URL'),
  fileSize: z.number().min(0, 'File size must be a positive number'),
  fileType: z.string().min(1, 'File type is required'),
  previewUrl: z.string().url('Invalid preview URL').nullable().optional(),
  licenseType: z.string().min(1, 'License type is required'),
  version: z.string().optional(),
  requirements: z.string().optional(),
  features: z.array(z.string()).optional(),
  available: z.boolean().default(true),
});

// Type for digital product
export type DigitalProduct = z.infer<typeof digitalProductSchema>;

// Schema for creating a digital product (includes merchantId)
export const createDigitalProductSchema = digitalProductSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a digital product
export type CreateDigitalProduct = z.infer<typeof createDigitalProductSchema>;

// Schema for updating a digital product
export const updateDigitalProductSchema = digitalProductSchema.partial().extend({
  id: z.string().min(1, 'Product ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a digital product
export type UpdateDigitalProduct = z.infer<typeof updateDigitalProductSchema>;

// License schema
export const licenseSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  userId: z.string().min(1, 'User ID is required'),
  orderId: z.string().min(1, 'Order ID is required'),
  licenseKey: z.string().min(1, 'License key is required'),
  activationDate: z.string().min(1, 'Activation date is required'),
  expiryDate: z.string().nullable().optional(),
  maxActivations: z.number().int().min(1, 'Maximum activations must be at least 1'),
  currentActivations: z.number().int().min(0, 'Current activations must be a non-negative integer'),
  status: z.enum(['active', 'expired', 'revoked']),
});

// Type for license
export type License = z.infer<typeof licenseSchema>;

// Schema for creating a license (includes merchantId)
export const createLicenseSchema = licenseSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a license
export type CreateLicense = z.infer<typeof createLicenseSchema>;

// Schema for updating a license
export const updateLicenseSchema = licenseSchema.partial().extend({
  id: z.string().min(1, 'License ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a license
export type UpdateLicense = z.infer<typeof updateLicenseSchema>;
