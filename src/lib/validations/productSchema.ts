import { z } from 'zod';

// Product variant schema
export const productVariantSchema = z.object({
  id: z.string().optional(), // Optional for new variants
  productId: z.string().optional(), // Optional for new variants
  name: z.string().min(1, 'Variant name is required'),
  sku: z.string().min(1, 'SKU is required'),
  price: z.number().min(0, 'Price must be a positive number'),
  inventoryCount: z.number().int().min(0, 'Inventory count must be a non-negative integer'),
  attributes: z.record(z.any()).optional(),
});

// Product schema
export const productSchema = z.object({
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  price: z.number().min(0, 'Price must be a positive number'),
  salePrice: z.number().min(0, 'Sale price must be a positive number').nullable().optional(),
  category: z.string().min(1, 'Category is required'),
  images: z.array(z.string().url('Invalid image URL')).optional(),
  sku: z.string().min(1, 'SKU is required'),
  barcode: z.string().optional(),
  weight: z.number().min(0, 'Weight must be a positive number').optional(),
  dimensions: z.object({
    length: z.number().min(0, 'Length must be a positive number'),
    width: z.number().min(0, 'Width must be a positive number'),
    height: z.number().min(0, 'Height must be a positive number'),
  }).optional(),
  inventoryCount: z.number().int().min(0, 'Inventory count must be a non-negative integer'),
  inventoryThreshold: z.number().int().min(0, 'Inventory threshold must be a non-negative integer').optional(),
  available: z.boolean().default(true),
  attributes: z.record(z.any()).optional(),
  variants: z.array(productVariantSchema).optional(),
});

// Type for product
export type Product = z.infer<typeof productSchema>;

// Type for product variant
export type ProductVariant = z.infer<typeof productVariantSchema>;

// Schema for creating a product (includes merchantId)
export const createProductSchema = productSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a product
export type CreateProduct = z.infer<typeof createProductSchema>;

// Schema for updating a product
export const updateProductSchema = productSchema.partial().extend({
  id: z.string().min(1, 'Product ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a product
export type UpdateProduct = z.infer<typeof updateProductSchema>;
