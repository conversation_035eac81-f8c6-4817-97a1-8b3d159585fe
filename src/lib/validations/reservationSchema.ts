import { z } from 'zod';

// Reservation schema
export const reservationSchema = z.object({
  tableId: z.string().min(1, 'Table is required'),
  userId: z.string().min(1, 'User ID is required'),
  date: z.string().min(1, 'Date is required'),
  time: z.string().min(1, 'Time is required'),
  duration: z.number().int().min(1, 'Duration must be at least 1 minute'),
  partySize: z.number().int().min(1, 'Party size must be at least 1'),
  status: z.enum(['pending', 'confirmed', 'cancelled', 'completed', 'no-show', 'seated']),
  specialRequests: z.string().optional(),
  customerName: z.string().min(1, 'Customer name is required'),
  customerEmail: z.string().email('Invalid email address'),
  customerPhone: z.string().optional(),
});

// Type for reservation
export type Reservation = z.infer<typeof reservationSchema>;

// Schema for creating a reservation (includes merchantId)
export const createReservationSchema = reservationSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a reservation
export type CreateReservation = z.infer<typeof createReservationSchema>;

// Schema for updating a reservation
export const updateReservationSchema = reservationSchema.partial().extend({
  id: z.string().min(1, 'Reservation ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a reservation
export type UpdateReservation = z.infer<typeof updateReservationSchema>;

// Table schema
export const tableSchema = z.object({
  number: z.number().int().min(1, 'Table number must be at least 1'),
  capacity: z.number().int().min(1, 'Capacity must be at least 1'),
  status: z.enum(['available', 'occupied', 'reserved', 'unavailable']),
  location: z.string().optional(),
});

// Type for table
export type Table = z.infer<typeof tableSchema>;

// Schema for creating a table (includes merchantId)
export const createTableSchema = tableSchema.extend({
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for creating a table
export type CreateTable = z.infer<typeof createTableSchema>;

// Schema for updating a table
export const updateTableSchema = tableSchema.partial().extend({
  id: z.string().min(1, 'Table ID is required'),
  merchantId: z.string().min(1, 'Merchant ID is required'),
});

// Type for updating a table
export type UpdateTable = z.infer<typeof updateTableSchema>;

// Time slot schema for table availability
export const tableTimeSlotSchema = z.object({
  date: z.string(),
  time: z.string(),
  available: z.boolean(),
  tableId: z.string(),
  capacity: z.number().int(),
});

// Type for table time slot
export type TableTimeSlot = z.infer<typeof tableTimeSlotSchema>;
