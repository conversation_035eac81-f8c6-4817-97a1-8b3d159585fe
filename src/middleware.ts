import { withAuth } from "next-auth/middleware"
import createIntlMiddleware from 'next-intl/middleware';
import { routing } from './i18n/routing';
import { NextRequest } from 'next/server';

const intlMiddleware = createIntlMiddleware(routing);

export default withAuth(
  function middleware(req: NextRequest) {
    // Handle locale redirects first
    const response = intlMiddleware(req);
    return response;
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Always allow access to API routes
        if (pathname.startsWith('/api')) {
          return true;
        }

        // Public routes that don't require authentication
        const publicRoutes = ['/login', '/register', '/forgot-password', '/auth', '/oauth-test'];

        // Check if it's a public route (including localized versions)
        const isPublicRoute = publicRoutes.some(route => {
          return pathname === route ||
                 pathname.startsWith(`/en${route}`) ||
                 pathname.startsWith(`/th${route}`) ||
                 pathname === '/' ||
                 pathname === '/en' ||
                 pathname === '/th';
        });

        if (isPublicRoute) {
          return true;
        }

        // Protected routes require authentication
        const isProtectedRoute = pathname.includes('/admin') ||
                               pathname.includes('/app') ||
                               pathname.startsWith('/en/admin') ||
                               pathname.startsWith('/th/admin') ||
                               pathname.startsWith('/en/app') ||
                               pathname.startsWith('/th/app');

        if (isProtectedRoute) {
          return !!token;
        }

        return true;
      },
    },
  }
);

export const config = {
  matcher: [
    // Enable a redirect to a matching locale at the root
    '/',

    // Set a cookie to remember the previous locale for
    // all requests that have a locale prefix
    '/(th|en)/:path*',

    // Enable redirects that add missing locales
    // (e.g. `/pathnames` -> `/en/pathnames`)
    '/((?!api|trpc|_next|_vercel|.*\\..*).*)'
  ],
};