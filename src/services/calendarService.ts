import { prisma } from '@/lib/prisma';

/**
 * Interface for calendar integration
 */
export interface CalendarIntegration {
  id?: string;
  merchantId: string;
  userId?: string;
  provider: 'google' | 'outlook' | 'apple' | 'custom';
  name: string;
  calendarId: string;
  accessToken: string;
  refreshToken?: string;
  tokenExpiry?: Date;
  settings: {
    syncEnabled: boolean;
    syncDirection: 'import' | 'export' | 'both';
    colorId?: string;
    defaultReminders?: { method: string; minutes: number }[];
  };
  lastSyncedAt?: Date;
}

/**
 * Interface for calendar event
 */
export interface CalendarEvent {
  id?: string;
  calendarIntegrationId: string;
  appointmentId?: string;
  externalEventId: string;
  summary: string;
  description?: string;
  location?: string;
  startTime: Date;
  endTime: Date;
  attendees?: { email: string; name?: string }[];
  status: 'confirmed' | 'tentative' | 'cancelled';
  lastSyncedAt: Date;
}

/**
 * Service for handling calendar integrations
 */
export const calendarService = {
  /**
   * Get calendar integrations for a merchant
   * @param merchantId - The ID of the merchant
   * @returns The calendar integrations
   */
  async getCalendarIntegrations(merchantId: string) {
    try {
      const integrations = await prisma.calendarIntegration.findMany({
        where: {
          merchantId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return integrations;
    } catch (error) {
      console.error('Error fetching calendar integrations:', error);
      throw new Error('Failed to fetch calendar integrations');
    }
  },
  
  /**
   * Get a calendar integration by ID
   * @param merchantId - The ID of the merchant
   * @param integrationId - The ID of the integration
   * @returns The calendar integration
   */
  async getCalendarIntegrationById(merchantId: string, integrationId: string) {
    try {
      const integration = await prisma.calendarIntegration.findUnique({
        where: {
          id: integrationId,
          merchantId,
        },
      });
      
      if (!integration) {
        throw new Error('Calendar integration not found');
      }
      
      return integration;
    } catch (error) {
      console.error('Error fetching calendar integration:', error);
      throw new Error('Failed to fetch calendar integration');
    }
  },
  
  /**
   * Create a calendar integration
   * @param integration - The integration data
   * @returns The created integration
   */
  async createCalendarIntegration(integration: Omit<CalendarIntegration, 'id' | 'lastSyncedAt'>) {
    try {
      const newIntegration = await prisma.calendarIntegration.create({
        data: {
          ...integration,
          settings: integration.settings || {
            syncEnabled: true,
            syncDirection: 'both',
          },
        },
      });
      
      return newIntegration;
    } catch (error) {
      console.error('Error creating calendar integration:', error);
      throw new Error('Failed to create calendar integration');
    }
  },
  
  /**
   * Update a calendar integration
   * @param merchantId - The ID of the merchant
   * @param integrationId - The ID of the integration
   * @param data - The updated integration data
   * @returns The updated integration
   */
  async updateCalendarIntegration(
    merchantId: string,
    integrationId: string,
    data: Partial<CalendarIntegration>
  ) {
    try {
      // Check if the integration exists
      const existingIntegration = await prisma.calendarIntegration.findUnique({
        where: {
          id: integrationId,
          merchantId,
        },
      });
      
      if (!existingIntegration) {
        throw new Error('Calendar integration not found');
      }
      
      // Update the integration
      const updatedIntegration = await prisma.calendarIntegration.update({
        where: {
          id: integrationId,
        },
        data,
      });
      
      return updatedIntegration;
    } catch (error) {
      console.error('Error updating calendar integration:', error);
      throw new Error('Failed to update calendar integration');
    }
  },
  
  /**
   * Delete a calendar integration
   * @param merchantId - The ID of the merchant
   * @param integrationId - The ID of the integration
   * @returns True if the integration was deleted
   */
  async deleteCalendarIntegration(merchantId: string, integrationId: string) {
    try {
      // Check if the integration exists
      const integration = await prisma.calendarIntegration.findUnique({
        where: {
          id: integrationId,
          merchantId,
        },
      });
      
      if (!integration) {
        throw new Error('Calendar integration not found');
      }
      
      // Delete all events associated with this integration
      await prisma.calendarEvent.deleteMany({
        where: {
          calendarIntegrationId: integrationId,
        },
      });
      
      // Delete the integration
      await prisma.calendarIntegration.delete({
        where: {
          id: integrationId,
        },
      });
      
      return true;
    } catch (error) {
      console.error('Error deleting calendar integration:', error);
      throw new Error('Failed to delete calendar integration');
    }
  },
  
  /**
   * Sync an appointment with external calendars
   * @param merchantId - The ID of the merchant
   * @param appointmentId - The ID of the appointment
   * @returns The synced calendar events
   */
  async syncAppointmentWithCalendars(merchantId: string, appointmentId: string) {
    try {
      // Get the appointment
      const appointment = await prisma.appointment.findUnique({
        where: {
          id: appointmentId,
          merchantId,
        },
        include: {
          service: true,
          staff: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });
      
      if (!appointment) {
        throw new Error('Appointment not found');
      }
      
      // Get all active calendar integrations for the merchant
      const integrations = await prisma.calendarIntegration.findMany({
        where: {
          merchantId,
          settings: {
            path: ['syncEnabled'],
            equals: true,
          },
        },
      });
      
      if (integrations.length === 0) {
        return { message: 'No active calendar integrations found' };
      }
      
      // For each integration, create or update a calendar event
      const syncResults = [];
      
      for (const integration of integrations) {
        // Check if an event already exists for this appointment and integration
        const existingEvent = await prisma.calendarEvent.findFirst({
          where: {
            calendarIntegrationId: integration.id,
            appointmentId,
          },
        });
        
        // Prepare event data
        const eventData = {
          calendarIntegrationId: integration.id,
          appointmentId,
          summary: `${appointment.service.name} - ${appointment.customerName}`,
          description: appointment.notes || `Appointment for ${appointment.service.name}`,
          location: appointment.location || '',
          startTime: new Date(`${appointment.date}T${appointment.startTime}`),
          endTime: new Date(`${appointment.date}T${appointment.endTime}`),
          attendees: [
            { email: appointment.customerEmail, name: appointment.customerName },
            ...(appointment.staff ? [{ email: appointment.staff.email, name: appointment.staff.name }] : []),
          ],
          status: appointment.status === 'cancelled' ? 'cancelled' : 'confirmed',
          lastSyncedAt: new Date(),
        };
        
        // In a real implementation, this would call the external calendar API
        // For now, we'll just simulate the external event ID
        const externalEventId = existingEvent?.externalEventId || `event_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        
        let event;
        
        if (existingEvent) {
          // Update existing event
          event = await prisma.calendarEvent.update({
            where: {
              id: existingEvent.id,
            },
            data: {
              ...eventData,
              externalEventId,
            },
          });
        } else {
          // Create new event
          event = await prisma.calendarEvent.create({
            data: {
              ...eventData,
              externalEventId,
            },
          });
        }
        
        syncResults.push({
          integration: integration.name,
          event,
          status: existingEvent ? 'updated' : 'created',
        });
      }
      
      return {
        appointment,
        syncResults,
      };
    } catch (error) {
      console.error('Error syncing appointment with calendars:', error);
      throw new Error('Failed to sync appointment with calendars');
    }
  },
  
  /**
   * Get calendar events for a merchant
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date for the events
   * @param endDate - The end date for the events
   * @returns The calendar events
   */
  async getCalendarEvents(merchantId: string, startDate?: string, endDate?: string) {
    try {
      // Get all calendar integrations for the merchant
      const integrations = await prisma.calendarIntegration.findMany({
        where: {
          merchantId,
        },
        select: {
          id: true,
        },
      });
      
      if (integrations.length === 0) {
        return [];
      }
      
      // Build the date filter
      const dateFilter: any = {};
      if (startDate) {
        dateFilter.gte = new Date(startDate);
      }
      if (endDate) {
        dateFilter.lte = new Date(endDate);
      }
      
      // Get all events for these integrations
      const events = await prisma.calendarEvent.findMany({
        where: {
          calendarIntegrationId: {
            in: integrations.map(i => i.id),
          },
          ...(startDate || endDate ? { startTime: dateFilter } : {}),
        },
        include: {
          calendarIntegration: {
            select: {
              name: true,
              provider: true,
            },
          },
          appointment: {
            include: {
              service: {
                select: {
                  name: true,
                },
              },
              staff: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          startTime: 'asc',
        },
      });
      
      return events;
    } catch (error) {
      console.error('Error fetching calendar events:', error);
      throw new Error('Failed to fetch calendar events');
    }
  },
};
