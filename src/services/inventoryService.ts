import { prisma } from '@/lib/prisma';

/**
 * Interface for product
 */
export interface Product {
  id?: string;
  merchantId: string;
  name: string;
  description?: string;
  sku?: string;
  barcode?: string;
  price: number;
  salePrice?: number;
  cost?: number;
  categoryId?: string;
  brandId?: string;
  supplierId?: string;
  taxRate?: number;
  images?: string[];
  attributes?: Record<string, any>;
  isActive: boolean;
  isService: boolean;
  inventoryTracking: boolean;
  stockQuantity?: number;
  lowStockThreshold?: number;
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Interface for product category
 */
export interface ProductCategory {
  id?: string;
  merchantId: string;
  name: string;
  description?: string;
  parentId?: string;
  image?: string;
  order: number;
}

/**
 * Interface for inventory transaction
 */
export interface InventoryTransaction {
  id?: string;
  merchantId: string;
  productId: string;
  type: 'purchase' | 'sale' | 'adjustment' | 'return' | 'transfer';
  quantity: number;
  reference?: string;
  notes?: string;
  createdAt?: Date;
}

/**
 * Service for handling inventory and products
 */
export const inventoryService = {
  /**
   * Get products for a merchant
   * @param merchantId - The ID of the merchant
   * @param categoryId - The ID of the category (optional)
   * @param includeInactive - Whether to include inactive products
   * @returns The products
   */
  async getProducts(merchantId: string, categoryId?: string, includeInactive = false) {
    try {
      const products = await prisma.product.findMany({
        where: {
          merchantId,
          ...(categoryId ? { categoryId } : {}),
          ...(includeInactive ? {} : { isActive: true }),
        },
        include: {
          category: true,
          brand: true,
          supplier: true,
          inventoryTransactions: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 5,
          },
        },
        orderBy: {
          name: 'asc',
        },
      });
      
      return products;
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  },
  
  /**
   * Get a product by ID
   * @param merchantId - The ID of the merchant
   * @param productId - The ID of the product
   * @returns The product
   */
  async getProductById(merchantId: string, productId: string) {
    try {
      const product = await prisma.product.findUnique({
        where: {
          id: productId,
          merchantId,
        },
        include: {
          category: true,
          brand: true,
          supplier: true,
          inventoryTransactions: {
            orderBy: {
              createdAt: 'desc',
            },
            take: 20,
          },
        },
      });
      
      if (!product) {
        throw new Error('Product not found');
      }
      
      return product;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  },
  
  /**
   * Create a product
   * @param product - The product data
   * @returns The created product
   */
  async createProduct(product: Omit<Product, 'id' | 'createdAt' | 'updatedAt'>) {
    try {
      // Check if a product with this SKU already exists
      if (product.sku) {
        const existingProduct = await prisma.product.findFirst({
          where: {
            merchantId: product.merchantId,
            sku: product.sku,
          },
        });
        
        if (existingProduct) {
          throw new Error('A product with this SKU already exists');
        }
      }
      
      // Create the product
      const newProduct = await prisma.product.create({
        data: {
          ...product,
          stockQuantity: product.inventoryTracking ? (product.stockQuantity || 0) : null,
          lowStockThreshold: product.inventoryTracking ? (product.lowStockThreshold || 5) : null,
        },
        include: {
          category: true,
          brand: true,
          supplier: true,
        },
      });
      
      // If inventory tracking is enabled and initial stock is provided, create an inventory transaction
      if (product.inventoryTracking && product.stockQuantity && product.stockQuantity > 0) {
        await prisma.inventoryTransaction.create({
          data: {
            merchantId: product.merchantId,
            productId: newProduct.id,
            type: 'purchase',
            quantity: product.stockQuantity,
            notes: 'Initial stock',
          },
        });
      }
      
      return newProduct;
    } catch (error) {
      console.error('Error creating product:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create product');
    }
  },
  
  /**
   * Update a product
   * @param merchantId - The ID of the merchant
   * @param productId - The ID of the product
   * @param data - The updated product data
   * @returns The updated product
   */
  async updateProduct(merchantId: string, productId: string, data: Partial<Product>) {
    try {
      // Check if the product exists
      const existingProduct = await prisma.product.findUnique({
        where: {
          id: productId,
          merchantId,
        },
      });
      
      if (!existingProduct) {
        throw new Error('Product not found');
      }
      
      // If SKU is being updated, check if it's already in use
      if (data.sku && data.sku !== existingProduct.sku) {
        const skuInUse = await prisma.product.findFirst({
          where: {
            merchantId,
            sku: data.sku,
            id: {
              not: productId,
            },
          },
        });
        
        if (skuInUse) {
          throw new Error('A product with this SKU already exists');
        }
      }
      
      // If stock quantity is being updated, create an inventory transaction
      if (data.stockQuantity !== undefined && data.stockQuantity !== existingProduct.stockQuantity) {
        const quantityDifference = (data.stockQuantity || 0) - (existingProduct.stockQuantity || 0);
        
        if (quantityDifference !== 0) {
          await prisma.inventoryTransaction.create({
            data: {
              merchantId,
              productId,
              type: 'adjustment',
              quantity: quantityDifference,
              notes: 'Manual stock adjustment',
            },
          });
        }
      }
      
      // Update the product
      const product = await prisma.product.update({
        where: {
          id: productId,
        },
        data: {
          ...data,
          // If inventory tracking is being disabled, set stock-related fields to null
          stockQuantity: data.inventoryTracking === false ? null : data.stockQuantity,
          lowStockThreshold: data.inventoryTracking === false ? null : data.lowStockThreshold,
        },
        include: {
          category: true,
          brand: true,
          supplier: true,
        },
      });
      
      return product;
    } catch (error) {
      console.error('Error updating product:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update product');
    }
  },
  
  /**
   * Delete a product
   * @param merchantId - The ID of the merchant
   * @param productId - The ID of the product
   * @returns True if the product was deleted
   */
  async deleteProduct(merchantId: string, productId: string) {
    try {
      // Check if the product exists
      const product = await prisma.product.findUnique({
        where: {
          id: productId,
          merchantId,
        },
        include: {
          inventoryTransactions: true,
        },
      });
      
      if (!product) {
        throw new Error('Product not found');
      }
      
      // If the product has inventory transactions, don't delete it, just mark it as inactive
      if (product.inventoryTransactions.length > 0) {
        await prisma.product.update({
          where: {
            id: productId,
          },
          data: {
            isActive: false,
          },
        });
        
        return { success: true, message: 'Product has inventory transactions and cannot be deleted. It has been marked as inactive instead.' };
      }
      
      // Delete the product
      await prisma.product.delete({
        where: {
          id: productId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting product:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete product');
    }
  },
  
  /**
   * Get product categories for a merchant
   * @param merchantId - The ID of the merchant
   * @returns The product categories
   */
  async getProductCategories(merchantId: string) {
    try {
      const categories = await prisma.productCategory.findMany({
        where: {
          merchantId,
        },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
            },
          },
          children: {
            select: {
              id: true,
              name: true,
            },
          },
          products: {
            select: {
              id: true,
            },
          },
        },
        orderBy: {
          order: 'asc',
        },
      });
      
      return categories;
    } catch (error) {
      console.error('Error fetching product categories:', error);
      throw new Error('Failed to fetch product categories');
    }
  },
  
  /**
   * Create a product category
   * @param category - The category data
   * @returns The created category
   */
  async createProductCategory(category: Omit<ProductCategory, 'id'>) {
    try {
      // Check if a category with this name already exists
      const existingCategory = await prisma.productCategory.findFirst({
        where: {
          merchantId: category.merchantId,
          name: category.name,
        },
      });
      
      if (existingCategory) {
        throw new Error('A category with this name already exists');
      }
      
      // If this is a subcategory, check if the parent exists
      if (category.parentId) {
        const parent = await prisma.productCategory.findUnique({
          where: {
            id: category.parentId,
            merchantId: category.merchantId,
          },
        });
        
        if (!parent) {
          throw new Error('Parent category not found');
        }
      }
      
      // Get the highest order value to place the new category at the end
      const highestOrder = await prisma.productCategory.findFirst({
        where: {
          merchantId: category.merchantId,
          parentId: category.parentId,
        },
        orderBy: {
          order: 'desc',
        },
        select: {
          order: true,
        },
      });
      
      const order = highestOrder ? highestOrder.order + 1 : 0;
      
      // Create the category
      const newCategory = await prisma.productCategory.create({
        data: {
          ...category,
          order,
        },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      
      return newCategory;
    } catch (error) {
      console.error('Error creating product category:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create product category');
    }
  },
  
  /**
   * Update a product category
   * @param merchantId - The ID of the merchant
   * @param categoryId - The ID of the category
   * @param data - The updated category data
   * @returns The updated category
   */
  async updateProductCategory(merchantId: string, categoryId: string, data: Partial<ProductCategory>) {
    try {
      // Check if the category exists
      const existingCategory = await prisma.productCategory.findUnique({
        where: {
          id: categoryId,
          merchantId,
        },
      });
      
      if (!existingCategory) {
        throw new Error('Category not found');
      }
      
      // If name is being updated, check if it's already in use
      if (data.name && data.name !== existingCategory.name) {
        const nameInUse = await prisma.productCategory.findFirst({
          where: {
            merchantId,
            name: data.name,
            id: {
              not: categoryId,
            },
          },
        });
        
        if (nameInUse) {
          throw new Error('A category with this name already exists');
        }
      }
      
      // If parent is being updated, check if it exists and is not the category itself
      if (data.parentId && data.parentId !== existingCategory.parentId) {
        if (data.parentId === categoryId) {
          throw new Error('A category cannot be its own parent');
        }
        
        const parent = await prisma.productCategory.findUnique({
          where: {
            id: data.parentId,
            merchantId,
          },
        });
        
        if (!parent) {
          throw new Error('Parent category not found');
        }
        
        // Check for circular references
        let currentParent = parent;
        while (currentParent.parentId) {
          if (currentParent.parentId === categoryId) {
            throw new Error('Circular reference detected');
          }
          
          currentParent = await prisma.productCategory.findUnique({
            where: {
              id: currentParent.parentId,
            },
          });
        }
      }
      
      // Update the category
      const category = await prisma.productCategory.update({
        where: {
          id: categoryId,
        },
        data,
        include: {
          parent: {
            select: {
              id: true,
              name: true,
            },
          },
          children: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });
      
      return category;
    } catch (error) {
      console.error('Error updating product category:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update product category');
    }
  },
  
  /**
   * Delete a product category
   * @param merchantId - The ID of the merchant
   * @param categoryId - The ID of the category
   * @returns True if the category was deleted
   */
  async deleteProductCategory(merchantId: string, categoryId: string) {
    try {
      // Check if the category exists
      const category = await prisma.productCategory.findUnique({
        where: {
          id: categoryId,
          merchantId,
        },
        include: {
          children: true,
          products: true,
        },
      });
      
      if (!category) {
        throw new Error('Category not found');
      }
      
      // Check if the category has children
      if (category.children.length > 0) {
        throw new Error('Cannot delete a category with subcategories');
      }
      
      // Check if the category has products
      if (category.products.length > 0) {
        throw new Error('Cannot delete a category with products');
      }
      
      // Delete the category
      await prisma.productCategory.delete({
        where: {
          id: categoryId,
        },
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error deleting product category:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete product category');
    }
  },
  
  /**
   * Get inventory transactions for a product
   * @param merchantId - The ID of the merchant
   * @param productId - The ID of the product
   * @returns The inventory transactions
   */
  async getInventoryTransactions(merchantId: string, productId: string) {
    try {
      // Check if the product exists
      const product = await prisma.product.findUnique({
        where: {
          id: productId,
          merchantId,
        },
      });
      
      if (!product) {
        throw new Error('Product not found');
      }
      
      // Get the inventory transactions
      const transactions = await prisma.inventoryTransaction.findMany({
        where: {
          merchantId,
          productId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
      
      return transactions;
    } catch (error) {
      console.error('Error fetching inventory transactions:', error);
      throw new Error('Failed to fetch inventory transactions');
    }
  },
  
  /**
   * Create an inventory transaction
   * @param transaction - The transaction data
   * @returns The created transaction
   */
  async createInventoryTransaction(transaction: Omit<InventoryTransaction, 'id' | 'createdAt'>) {
    try {
      // Check if the product exists and has inventory tracking enabled
      const product = await prisma.product.findUnique({
        where: {
          id: transaction.productId,
          merchantId: transaction.merchantId,
        },
      });
      
      if (!product) {
        throw new Error('Product not found');
      }
      
      if (!product.inventoryTracking) {
        throw new Error('Inventory tracking is not enabled for this product');
      }
      
      // Calculate the new stock quantity
      let newStockQuantity = product.stockQuantity || 0;
      
      if (transaction.type === 'purchase' || transaction.type === 'return') {
        newStockQuantity += transaction.quantity;
      } else if (transaction.type === 'sale' || transaction.type === 'transfer') {
        newStockQuantity -= transaction.quantity;
        
        // Check if there's enough stock
        if (newStockQuantity < 0) {
          throw new Error('Not enough stock');
        }
      } else if (transaction.type === 'adjustment') {
        newStockQuantity += transaction.quantity; // Can be positive or negative
      }
      
      // Create the transaction
      const newTransaction = await prisma.inventoryTransaction.create({
        data: transaction,
      });
      
      // Update the product's stock quantity
      await prisma.product.update({
        where: {
          id: transaction.productId,
        },
        data: {
          stockQuantity: newStockQuantity,
        },
      });
      
      return {
        transaction: newTransaction,
        newStockQuantity,
      };
    } catch (error) {
      console.error('Error creating inventory transaction:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to create inventory transaction');
    }
  },
  
  /**
   * Get low stock products
   * @param merchantId - The ID of the merchant
   * @returns Products with stock below the threshold
   */
  async getLowStockProducts(merchantId: string) {
    try {
      const products = await prisma.product.findMany({
        where: {
          merchantId,
          isActive: true,
          inventoryTracking: true,
          stockQuantity: {
            not: null,
          },
          lowStockThreshold: {
            not: null,
          },
          OR: [
            {
              stockQuantity: {
                lte: prisma.product.fields.lowStockThreshold,
              },
            },
            {
              stockQuantity: 0,
            },
          ],
        },
        include: {
          category: true,
        },
        orderBy: {
          stockQuantity: 'asc',
        },
      });
      
      return products;
    } catch (error) {
      console.error('Error fetching low stock products:', error);
      throw new Error('Failed to fetch low stock products');
    }
  },
};
