import { prisma } from '@/lib/prisma';

/**
 * Interface for appointment analytics
 */
export interface AppointmentAnalytics {
  total: number;
  completed: number;
  cancelled: number;
  noShow: number;
  revenue: number;
  averageDuration: number;
}

/**
 * Interface for service analytics
 */
export interface ServiceAnalytics {
  id: string;
  name: string;
  appointmentsCount: number;
  revenue: number;
  cancellationRate: number;
}

/**
 * Interface for staff analytics
 */
export interface StaffAnalytics {
  id: string;
  name: string;
  appointmentsCount: number;
  revenue: number;
  utilization: number; // percentage of available time used
}

/**
 * Interface for time slot analytics
 */
export interface TimeSlotAnalytics {
  hour: number;
  appointmentsCount: number;
  utilization: number; // percentage of available slots used
}

/**
 * Service for generating reports and analytics
 */
export const reportingService = {
  /**
   * Get appointment analytics for a merchant
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date for the report (optional)
   * @param endDate - The end date for the report (optional)
   * @returns Appointment analytics
   */
  async getAppointmentAnalytics(merchantId: string, startDate?: string, endDate?: string) {
    try {
      // Build the date filter
      const dateFilter: any = {};
      if (startDate) {
        dateFilter.gte = startDate;
      }
      if (endDate) {
        dateFilter.lte = endDate;
      }

      // Get all appointments for the merchant within the date range
      const appointments = await prisma.appointment.findMany({
        where: {
          merchantId,
          ...(startDate || endDate ? { date: dateFilter } : {}),
        },
        include: {
          service: true,
        },
      });

      // Calculate analytics
      const total = appointments.length;
      const completed = appointments.filter(a => a.status === 'completed').length;
      const cancelled = appointments.filter(a => a.status === 'cancelled').length;
      const noShow = appointments.filter(a => a.status === 'no-show').length;
      
      // Calculate revenue (only from completed appointments)
      const revenue = appointments
        .filter(a => a.status === 'completed')
        .reduce((sum, appointment) => sum + appointment.price, 0);
      
      // Calculate average duration
      const totalDuration = appointments.reduce((sum, appointment) => sum + appointment.duration, 0);
      const averageDuration = total > 0 ? totalDuration / total : 0;

      return {
        total,
        completed,
        cancelled,
        noShow,
        revenue,
        averageDuration,
      };
    } catch (error) {
      console.error('Error generating appointment analytics:', error);
      throw new Error('Failed to generate appointment analytics');
    }
  },

  /**
   * Get service analytics for a merchant
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date for the report (optional)
   * @param endDate - The end date for the report (optional)
   * @returns Service analytics
   */
  async getServiceAnalytics(merchantId: string, startDate?: string, endDate?: string) {
    try {
      // Build the date filter
      const dateFilter: any = {};
      if (startDate) {
        dateFilter.gte = startDate;
      }
      if (endDate) {
        dateFilter.lte = endDate;
      }

      // Get all services for the merchant
      const services = await prisma.service.findMany({
        where: {
          merchantId,
        },
        include: {
          appointments: {
            where: {
              ...(startDate || endDate ? { date: dateFilter } : {}),
            },
          },
        },
      });

      // Calculate analytics for each service
      const serviceAnalytics = services.map(service => {
        const appointmentsCount = service.appointments.length;
        const completedAppointments = service.appointments.filter(a => a.status === 'completed');
        const cancelledAppointments = service.appointments.filter(a => a.status === 'cancelled');
        
        // Calculate revenue (only from completed appointments)
        const revenue = completedAppointments.reduce((sum, appointment) => sum + appointment.price, 0);
        
        // Calculate cancellation rate
        const cancellationRate = appointmentsCount > 0 
          ? cancelledAppointments.length / appointmentsCount 
          : 0;

        return {
          id: service.id,
          name: service.name,
          appointmentsCount,
          revenue,
          cancellationRate,
        };
      });

      return serviceAnalytics;
    } catch (error) {
      console.error('Error generating service analytics:', error);
      throw new Error('Failed to generate service analytics');
    }
  },

  /**
   * Get staff analytics for a merchant
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date for the report (optional)
   * @param endDate - The end date for the report (optional)
   * @returns Staff analytics
   */
  async getStaffAnalytics(merchantId: string, startDate?: string, endDate?: string) {
    try {
      // Build the date filter
      const dateFilter: any = {};
      if (startDate) {
        dateFilter.gte = startDate;
      }
      if (endDate) {
        dateFilter.lte = endDate;
      }

      // Get all staff for the merchant
      const staffMembers = await prisma.staff.findMany({
        where: {
          merchantId,
        },
        include: {
          appointments: {
            where: {
              ...(startDate || endDate ? { date: dateFilter } : {}),
            },
            include: {
              service: true,
            },
          },
        },
      });

      // Calculate analytics for each staff member
      const staffAnalytics = staffMembers.map(staff => {
        const appointmentsCount = staff.appointments.length;
        const completedAppointments = staff.appointments.filter(a => a.status === 'completed');
        
        // Calculate revenue (only from completed appointments)
        const revenue = completedAppointments.reduce((sum, appointment) => sum + appointment.price, 0);
        
        // Calculate utilization (simplified - in a real app, you would need more complex logic)
        // This assumes 8 hours per day, 5 days per week
        const totalMinutesWorked = staff.appointments.reduce((sum, appointment) => sum + appointment.duration, 0);
        const workingDays = getWorkingDaysCount(startDate, endDate);
        const totalAvailableMinutes = workingDays * 8 * 60; // 8 hours per day in minutes
        const utilization = totalAvailableMinutes > 0 
          ? totalMinutesWorked / totalAvailableMinutes 
          : 0;

        return {
          id: staff.id,
          name: staff.name,
          appointmentsCount,
          revenue,
          utilization,
        };
      });

      return staffAnalytics;
    } catch (error) {
      console.error('Error generating staff analytics:', error);
      throw new Error('Failed to generate staff analytics');
    }
  },

  /**
   * Get time slot analytics for a merchant
   * @param merchantId - The ID of the merchant
   * @param startDate - The start date for the report (optional)
   * @param endDate - The end date for the report (optional)
   * @returns Time slot analytics
   */
  async getTimeSlotAnalytics(merchantId: string, startDate?: string, endDate?: string) {
    try {
      // Build the date filter
      const dateFilter: any = {};
      if (startDate) {
        dateFilter.gte = startDate;
      }
      if (endDate) {
        dateFilter.lte = endDate;
      }

      // Get all appointments for the merchant within the date range
      const appointments = await prisma.appointment.findMany({
        where: {
          merchantId,
          ...(startDate || endDate ? { date: dateFilter } : {}),
        },
        select: {
          startTime: true,
        },
      });

      // Initialize time slot analytics (for each hour from 0 to 23)
      const timeSlotAnalytics = Array.from({ length: 24 }, (_, hour) => ({
        hour,
        appointmentsCount: 0,
        utilization: 0,
      }));

      // Count appointments for each hour
      appointments.forEach(appointment => {
        const hour = parseInt(appointment.startTime.split(':')[0], 10);
        if (hour >= 0 && hour < 24) {
          timeSlotAnalytics[hour].appointmentsCount++;
        }
      });

      // Get the merchant's service settings to determine business hours
      const settings = await prisma.serviceSettings.findUnique({
        where: {
          merchantId,
        },
        select: {
          openingHours: true,
        },
      });

      // Calculate utilization based on business hours
      if (settings && settings.openingHours) {
        const workingDays = getWorkingDaysCount(startDate, endDate);
        
        // For each hour, calculate the utilization
        timeSlotAnalytics.forEach((slot, hour) => {
          // Check if this hour is within business hours
          // This is a simplified approach - in a real app, you would need to check each day's hours
          const isBusinessHour = Object.values(settings.openingHours).some(dayHours => {
            if (!dayHours.open || !dayHours.close) return false;
            const openHour = parseInt(dayHours.open.split(':')[0], 10);
            const closeHour = parseInt(dayHours.close.split(':')[0], 10);
            return hour >= openHour && hour < closeHour;
          });

          if (isBusinessHour) {
            // Calculate how many slots could have been booked in this hour
            // Assuming 4 slots per hour (15-minute intervals) and one appointment per slot
            const maxPossibleAppointments = workingDays * 4;
            slot.utilization = maxPossibleAppointments > 0 
              ? slot.appointmentsCount / maxPossibleAppointments 
              : 0;
          }
        });
      }

      return timeSlotAnalytics;
    } catch (error) {
      console.error('Error generating time slot analytics:', error);
      throw new Error('Failed to generate time slot analytics');
    }
  },
};

/**
 * Helper function to calculate the number of working days between two dates
 * @param startDate - The start date
 * @param endDate - The end date
 * @returns The number of working days
 */
function getWorkingDaysCount(startDate?: string, endDate?: string): number {
  if (!startDate || !endDate) {
    // Default to 30 days if no dates provided
    return 30 * 5 / 7; // Approximately 21 working days in 30 days
  }

  const start = new Date(startDate);
  const end = new Date(endDate);
  
  // Calculate the number of days between the dates
  const totalDays = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  
  // Estimate working days (Monday to Friday)
  // This is a simplified approach - in a real app, you would need to count actual working days
  return Math.round(totalDays * 5 / 7); // 5 out of 7 days are working days
}
