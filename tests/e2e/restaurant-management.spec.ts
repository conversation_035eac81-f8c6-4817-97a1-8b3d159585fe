import { test, expect } from '@playwright/test'

test.describe('Restaurant Management E2E Tests', () => {
  const baseURL = 'http://localhost:4000'
  const testRestaurantSlug = 'test-restaurant'
  const testBranchSlug = 'main-branch'

  test.beforeEach(async ({ page }) => {
    // Mock API responses for consistent testing
    await page.route('**/api/merchants', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 'merchant-1',
              slug: testRestaurantSlug,
              name: 'Test Restaurant',
              branches: [
                {
                  id: 'branch-1',
                  slug: testBranchSlug,
                  name: 'Main Branch',
                  address: '123 Test St',
                },
              ],
            },
          ],
        }),
      })
    })

    // Mock dashboard stats
    await page.route('**/api/dashboard/stats', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          todayOrders: 45,
          todayRevenue: 2850.75,
          todayReservations: 12,
          activeStaff: 8,
          ordersGrowth: 15.2,
          revenueGrowth: 8.7,
          reservationsGrowth: -2.1,
        }),
      })
    })

    // Mock menu items
    await page.route('**/api/menu/items', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'item-1',
            slug: 'burger-deluxe',
            name: 'Burger Deluxe',
            description: 'A delicious burger',
            category: 'Main Course',
            price: 15.99,
            available: true,
            image: 'https://example.com/burger.jpg',
          },
        ]),
      })
    })

    // Mock orders
    await page.route('**/api/orders', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: '#ORD-001',
            status: 'preparing',
            customer: { name: 'John Doe', phone: '+1234567890' },
            total: 25.99,
            createdAt: new Date().toISOString(),
            orderType: 'dine-in',
            items: [{ name: 'Burger Deluxe', quantity: 1, price: 15.99 }],
          },
        ]),
      })
    })

    // Mock staff
    await page.route('**/api/staff', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'staff-1',
            slug: 'john-doe',
            firstName: 'John',
            lastName: 'Doe',
            position: 'Manager',
            status: 'active',
            schedule: [],
          },
        ]),
      })
    })
  })

  test('should navigate through restaurant management workflow', async ({ page }) => {
    // Start at restaurant list page
    await page.goto(`${baseURL}/app/restaurant`)
    await page.waitForLoadState('networkidle')

    // Check restaurant list page
    await expect(page.getByText('Test Restaurant')).toBeVisible()

    // Navigate to restaurant dashboard
    await page.getByText('Test Restaurant').click()
    await page.waitForLoadState('networkidle')

    // Should redirect to dashboard
    await expect(page.getByText('Restaurant Dashboard')).toBeVisible()
    await expect(page.getByText('Real-time insights and management tools')).toBeVisible()

    // Check dashboard stats
    await expect(page.getByText('45')).toBeVisible() // Today's Orders
    await expect(page.getByText('$2,850.75')).toBeVisible() // Today's Revenue
    await expect(page.getByText('12')).toBeVisible() // Reservations
    await expect(page.getByText('8')).toBeVisible() // Active Staff

    // Navigate to menu management
    await page.getByText('Menu').click()
    await page.waitForLoadState('networkidle')

    await expect(page.getByText('Menu Management')).toBeVisible()
    await expect(page.getByText('Burger Deluxe')).toBeVisible()

    // Test search functionality
    await page.getByPlaceholderText('Search menu items').fill('burger')
    await expect(page.getByText('Burger Deluxe')).toBeVisible()

    // Switch to table view
    await page.getByText('Table View').click()
    await expect(page.getByText('Item Name')).toBeVisible()
    await expect(page.getByText('Category')).toBeVisible()

    // Navigate to orders management
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/orders`)
    await page.waitForLoadState('networkidle')

    await expect(page.getByText('Orders Management')).toBeVisible()
    await expect(page.getByText('#ORD-001')).toBeVisible()
    await expect(page.getByText('John Doe')).toBeVisible()

    // Test order search
    await page.getByPlaceholderText('Search orders...').fill('ORD-001')
    await expect(page.getByText('#ORD-001')).toBeVisible()

    // Navigate to staff management
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/staff`)
    await page.waitForLoadState('networkidle')

    await expect(page.getByText('Staff Management')).toBeVisible()
    await expect(page.getByText('John Doe')).toBeVisible()

    // Test staff tabs
    await page.getByText('Roles').click()
    await page.waitForTimeout(500)

    await page.getByText('Permissions').click()
    await page.waitForTimeout(500)

    // Navigate back to dashboard
    await page.getByText('Back to Dashboard').click()
    await page.waitForLoadState('networkidle')

    await expect(page.getByText('Restaurant Dashboard')).toBeVisible()
  })

  test('should handle responsive design on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/dashboard`)
    await page.waitForLoadState('networkidle')

    // Check that dashboard is responsive
    await expect(page.getByText('Restaurant Dashboard')).toBeVisible()
    await expect(page.getByText('Tables')).toBeVisible()
    await expect(page.getByText('Menu')).toBeVisible()

    // Navigate to menu on mobile
    await page.getByText('Menu').click()
    await page.waitForLoadState('networkidle')

    await expect(page.getByText('Menu Management')).toBeVisible()
    await expect(page.getByText('Burger Deluxe')).toBeVisible()

    // Test mobile search
    await page.getByPlaceholderText('Search menu items').fill('burger')
    await expect(page.getByText('Burger Deluxe')).toBeVisible()
  })

  test('should handle error states gracefully', async ({ page }) => {
    // Mock API error for merchants
    await page.route('**/api/merchants', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' }),
      })
    })

    await page.goto(`${baseURL}/app/restaurant`)
    await page.waitForLoadState('networkidle')

    // Should show error state (implementation depends on error handling)
    // This test verifies the app doesn't crash on API errors
    await expect(page.locator('body')).toBeVisible()
  })

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/dashboard`)
    await page.waitForLoadState('networkidle')

    // Test keyboard navigation through dashboard cards
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    await page.keyboard.press('Enter')

    // Should navigate to a management page
    await page.waitForLoadState('networkidle')
    await expect(page.locator('body')).toBeVisible()
  })

  test('should maintain state during navigation', async ({ page }) => {
    // Navigate to menu page
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/menu`)
    await page.waitForLoadState('networkidle')

    // Set search term
    await page.getByPlaceholderText('Search menu items').fill('burger')
    await expect(page.getByText('Burger Deluxe')).toBeVisible()

    // Switch to table view
    await page.getByText('Table View').click()
    await expect(page.getByText('Item Name')).toBeVisible()

    // Navigate away and back
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/dashboard`)
    await page.waitForLoadState('networkidle')

    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/menu`)
    await page.waitForLoadState('networkidle')

    // State should be reset (this is expected behavior)
    await expect(page.getByPlaceholderText('Search menu items')).toHaveValue('')
  })

  test('should handle concurrent API calls', async ({ page }) => {
    let apiCallCount = 0

    // Count API calls
    await page.route('**/api/**', async (route) => {
      apiCallCount++
      await route.continue()
    })

    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/dashboard`)
    await page.waitForLoadState('networkidle')

    // Should have made multiple API calls
    expect(apiCallCount).toBeGreaterThan(0)

    // Navigate to different pages quickly
    await page.getByText('Menu').click()
    await page.waitForTimeout(100)
    
    await page.getByText('Back to Dashboard').click()
    await page.waitForTimeout(100)

    await page.getByText('Orders').click()
    await page.waitForLoadState('networkidle')

    // Should handle concurrent navigation without errors
    await expect(page.getByText('Orders Management')).toBeVisible()
  })

  test('should validate data consistency across pages', async ({ page }) => {
    // Start at dashboard
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/dashboard`)
    await page.waitForLoadState('networkidle')

    // Check restaurant name in dashboard
    await expect(page.getByText('Restaurant Dashboard')).toBeVisible()

    // Navigate to menu
    await page.getByText('Menu').click()
    await page.waitForLoadState('networkidle')

    // Check that menu items are consistent
    await expect(page.getByText('Burger Deluxe')).toBeVisible()
    await expect(page.getByText('$15.99')).toBeVisible()

    // Navigate to orders
    await page.goto(`${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/orders`)
    await page.waitForLoadState('networkidle')

    // Check that order data is consistent
    await expect(page.getByText('#ORD-001')).toBeVisible()
    await expect(page.getByText('$25.99')).toBeVisible()
  })
})
