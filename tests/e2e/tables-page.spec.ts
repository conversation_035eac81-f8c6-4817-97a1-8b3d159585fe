import { test, expect } from '@playwright/test'

test.describe('Tables Page E2E Tests', () => {
  const baseURL = 'http://localhost:4000'
  const testRestaurantSlug = 'test-restaurant'
  const testBranchSlug = 'main-branch'
  const tablesPageURL = `${baseURL}/app/restaurant/${testRestaurantSlug}/${testBranchSlug}/tables`

  test.beforeEach(async ({ page }) => {
    // Mock API responses for consistent testing
    await page.route('**/api/merchants', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 'merchant-1',
              slug: testRestaurantSlug,
              name: 'Test Restaurant',
              branches: [
                {
                  id: 'branch-1',
                  slug: testBranchSlug,
                  name: 'Main Branch',
                  address: '123 Test St',
                },
              ],
            },
          ],
        }),
      })
    })

    await page.route('**/api/merchants/*/branches/*/tables', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'table-1',
            number: 1,
            name: 'Table 1',
            capacity: 4,
            status: 'available',
            area: 'dining',
          },
          {
            id: 'table-2',
            number: 2,
            name: 'Table 2',
            capacity: 2,
            status: 'occupied',
            area: 'dining',
          },
          {
            id: 'table-3',
            number: 3,
            name: 'Table 3',
            capacity: 6,
            status: 'reserved',
            area: 'outdoor',
          },
        ]),
      })
    })

    await page.route('**/api/merchants/*/branches/*/tables/areas', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([
          {
            id: 'area-1',
            name: 'Dining Area',
            description: 'Main dining area',
          },
          {
            id: 'area-2',
            name: 'Outdoor Patio',
            description: 'Outdoor seating area',
          },
        ]),
      })
    })

    await page.route('**/api/merchants/*/branches/*/reservations', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          data: [
            {
              id: 'reservation-1',
              time: '7:00 PM',
              customerName: 'John Doe',
              partySize: 4,
              tableId: 'table-1',
              tableName: 'Table 1',
              status: 'confirmed',
              date: '2024-01-15',
            },
            {
              id: 'reservation-2',
              time: '8:00 PM',
              customerName: 'Jane Smith',
              partySize: 2,
              tableId: 'table-2',
              tableName: 'Table 2',
              status: 'pending',
              date: '2024-01-15',
            },
          ],
        }),
      })
    })
  })

  test('should load and display tables page correctly', async ({ page }) => {
    await page.goto(tablesPageURL)

    // Wait for page to load
    await page.waitForLoadState('networkidle')

    // Check page title and description
    await expect(page.getByText('Tables & Reservations')).toBeVisible()
    await expect(page.getByText("Manage your restaurant's table layout and reservations.")).toBeVisible()

    // Check tabs are present
    await expect(page.getByText('Floor Plan')).toBeVisible()
    await expect(page.getByText('Reservations')).toBeVisible()

    // Check tables are displayed
    await expect(page.getByText('Table 1')).toBeVisible()
    await expect(page.getByText('Table 2')).toBeVisible()
    await expect(page.getByText('Table 3')).toBeVisible()

    // Check areas are displayed
    await expect(page.getByText('Dining Area')).toBeVisible()
    await expect(page.getByText('Outdoor Patio')).toBeVisible()
  })

  test('should switch between tabs correctly', async ({ page }) => {
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Initially on floor plan tab
    await expect(page.getByText('Table 1')).toBeVisible()
    await expect(page.getByText('Dining Area')).toBeVisible()

    // Click reservations tab
    await page.getByText('Reservations').click()

    // Wait for tab content to change
    await page.waitForTimeout(500)

    // Check reservations content is visible
    await expect(page.getByText('John Doe')).toBeVisible()
    await expect(page.getByText('Jane Smith')).toBeVisible()
    await expect(page.getByText('7:00 PM')).toBeVisible()
    await expect(page.getByText('8:00 PM')).toBeVisible()

    // Switch back to floor plan
    await page.getByText('Floor Plan').click()
    await page.waitForTimeout(500)

    // Check floor plan content is visible again
    await expect(page.getByText('Table 1')).toBeVisible()
    await expect(page.getByText('Dining Area')).toBeVisible()
  })

  test('should display table information correctly', async ({ page }) => {
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Check table details
    await expect(page.getByText('Capacity: 4')).toBeVisible()
    await expect(page.getByText('Capacity: 2')).toBeVisible()
    await expect(page.getByText('Capacity: 6')).toBeVisible()

    // Check table statuses
    await expect(page.getByText('Status: available')).toBeVisible()
    await expect(page.getByText('Status: occupied')).toBeVisible()
    await expect(page.getByText('Status: reserved')).toBeVisible()
  })

  test('should display reservations table correctly', async ({ page }) => {
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Switch to reservations tab
    await page.getByText('Reservations').click()
    await page.waitForTimeout(500)

    // Check table headers
    await expect(page.getByText('Time')).toBeVisible()
    await expect(page.getByText('Customer')).toBeVisible()
    await expect(page.getByText('Party Size')).toBeVisible()
    await expect(page.getByText('Table')).toBeVisible()
    await expect(page.getByText('Status')).toBeVisible()
    await expect(page.getByText('Actions')).toBeVisible()

    // Check reservation data
    await expect(page.getByText('John Doe')).toBeVisible()
    await expect(page.getByText('Jane Smith')).toBeVisible()
    await expect(page.getByText('confirmed')).toBeVisible()
    await expect(page.getByText('pending')).toBeVisible()

    // Check view links
    const viewLinks = page.getByText('View')
    await expect(viewLinks).toHaveCount(2)
  })

  test('should handle table clicks for navigation', async ({ page }) => {
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Click on a table card
    const tableCard = page.locator('text=Table 1').first()
    await expect(tableCard).toBeVisible()

    // Check that table card is clickable (has link)
    const tableLink = page.locator(`a[href*="/tables/table-1"]`).first()
    await expect(tableLink).toBeVisible()
  })

  test('should handle reservation view links', async ({ page }) => {
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Switch to reservations tab
    await page.getByText('Reservations').click()
    await page.waitForTimeout(500)

    // Check reservation view links
    const viewLinks = page.locator('a[href*="/reservations/"]')
    await expect(viewLinks).toHaveCount(2)

    // Check that links have correct hrefs
    const firstViewLink = viewLinks.first()
    await expect(firstViewLink).toHaveAttribute('href', expect.stringContaining('/reservations/reservation-1'))
  })

  test('should be responsive on mobile devices', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Check that content is still visible and accessible
    await expect(page.getByText('Tables & Reservations')).toBeVisible()
    await expect(page.getByText('Floor Plan')).toBeVisible()
    await expect(page.getByText('Reservations')).toBeVisible()

    // Check that tables are still displayed properly
    await expect(page.getByText('Table 1')).toBeVisible()
    await expect(page.getByText('Dining Area')).toBeVisible()

    // Test tab switching on mobile
    await page.getByText('Reservations').click()
    await page.waitForTimeout(500)
    await expect(page.getByText('John Doe')).toBeVisible()
  })

  test('should handle API errors gracefully', async ({ page }) => {
    // Override API to return error
    await page.route('**/api/merchants/*/branches/*/tables', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Internal server error' }),
      })
    })

    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Should show error message
    await expect(page.getByText('Error Loading Data')).toBeVisible()
    await expect(page.getByText('There was an error loading the table data. Please try again.')).toBeVisible()
  })

  test('should handle empty states correctly', async ({ page }) => {
    // Override APIs to return empty data
    await page.route('**/api/merchants/*/branches/*/tables', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify([]),
      })
    })

    await page.route('**/api/merchants/*/branches/*/reservations', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ data: [] }),
      })
    })

    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Should show empty state for tables
    await expect(page.getByText('No tables found. Add tables to get started.')).toBeVisible()

    // Switch to reservations and check empty state
    await page.getByText('Reservations').click()
    await page.waitForTimeout(500)
    await expect(page.getByText('No reservations found')).toBeVisible()
  })

  test('should support keyboard navigation', async ({ page }) => {
    await page.goto(tablesPageURL)
    await page.waitForLoadState('networkidle')

    // Focus on the reservations tab using keyboard
    await page.keyboard.press('Tab')
    await page.keyboard.press('Tab')
    
    // Press Enter to activate the tab
    await page.keyboard.press('Enter')
    await page.waitForTimeout(500)

    // Should switch to reservations tab
    await expect(page.getByText('John Doe')).toBeVisible()
  })
})
